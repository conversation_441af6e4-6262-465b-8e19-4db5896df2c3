@echo off
echo ========================================
echo Building dataset_export with MinIO support
echo ========================================

REM 设置环境变量
set CMAKE_BUILD_TYPE=Release
set VCPKG_ROOT=C:\vcpkg
set CMAKE_TOOLCHAIN_FILE=%VCPKG_ROOT%\scripts\buildsystems\vcpkg.cmake

REM 检查vcpkg是否存在
if not exist "%VCPKG_ROOT%" (
    echo Error: vcpkg not found at %VCPKG_ROOT%
    echo Please install vcpkg first or update VCPKG_ROOT path
    pause
    exit /b 1
)

REM 检查是否已安装MinIO C++ SDK
echo Checking MinIO C++ SDK installation...
%VCPKG_ROOT%\vcpkg list | findstr "minio-cpp"
if %errorlevel% neq 0 (
    echo MinIO C++ SDK not found. Installing...
    echo Installing dependencies...
    %VCPKG_ROOT%\vcpkg install curl:x64-windows
    %VCPKG_ROOT%\vcpkg install openssl:x64-windows
    %VCPKG_ROOT%\vcpkg install pugixml:x64-windows
    
    echo Installing MinIO C++ SDK...
    REM 注意：MinIO C++ SDK可能需要手动编译，这里提供安装指导
    echo Please manually install MinIO C++ SDK:
    echo 1. git clone https://github.com/minio/minio-cpp.git
    echo 2. cd minio-cpp
    echo 3. mkdir build && cd build
    echo 4. cmake .. -DCMAKE_TOOLCHAIN_FILE=%CMAKE_TOOLCHAIN_FILE%
    echo 5. cmake --build . --config Release
    echo 6. cmake --install .
    pause
)

REM 检查OpenCV
echo Checking OpenCV installation...
%VCPKG_ROOT%\vcpkg list | findstr "opencv"
if %errorlevel% neq 0 (
    echo Installing OpenCV...
    %VCPKG_ROOT%\vcpkg install opencv:x64-windows
)

REM 检查nlohmann-json
echo Checking nlohmann-json installation...
%VCPKG_ROOT%\vcpkg list | findstr "nlohmann-json"
if %errorlevel% neq 0 (
    echo Installing nlohmann-json...
    %VCPKG_ROOT%\vcpkg install nlohmann-json:x64-windows
)

REM 创建构建目录
if not exist "build" mkdir build
cd build

REM 清理之前的构建
if exist "CMakeCache.txt" del CMakeCache.txt
if exist "CMakeFiles" rmdir /s /q CMakeFiles

echo ========================================
echo Configuring CMake...
echo ========================================

REM 配置CMake
cmake .. ^
    -DCMAKE_TOOLCHAIN_FILE=%CMAKE_TOOLCHAIN_FILE% ^
    -DCMAKE_BUILD_TYPE=%CMAKE_BUILD_TYPE% ^
    -DVCPKG_TARGET_TRIPLET=x64-windows ^
    -G "Visual Studio 17 2022" ^
    -A x64

if %errorlevel% neq 0 (
    echo CMake configuration failed!
    pause
    exit /b 1
)

echo ========================================
echo Building project...
echo ========================================

REM 构建项目
cmake --build . --config %CMAKE_BUILD_TYPE% --parallel

if %errorlevel% neq 0 (
    echo Build failed!
    pause
    exit /b 1
)

echo ========================================
echo Build completed successfully!
echo ========================================

REM 检查生成的库文件
if exist "%CMAKE_BUILD_TYPE%\dataset_export.dll" (
    echo Library file generated: %CMAKE_BUILD_TYPE%\dataset_export.dll
    
    REM 复制到Java项目的resources目录
    set JAVA_RESOURCES_DIR=..\ylzx-annotation\src\main\resources\native
    if exist "%JAVA_RESOURCES_DIR%" (
        echo Copying library to Java resources directory...
        copy "%CMAKE_BUILD_TYPE%\dataset_export.dll" "%JAVA_RESOURCES_DIR%\"
        if %errorlevel% equ 0 (
            echo Library copied successfully!
        ) else (
            echo Failed to copy library to Java resources directory
        )
    ) else (
        echo Java resources directory not found: %JAVA_RESOURCES_DIR%
    )
) else (
    echo Warning: Library file not found!
)

echo ========================================
echo Build process completed!
echo ========================================

REM 显示构建信息
echo Build information:
echo - Build type: %CMAKE_BUILD_TYPE%
echo - Toolchain: %CMAKE_TOOLCHAIN_FILE%
echo - Target: x64-windows
echo.
echo Next steps:
echo 1. Make sure MinIO C++ SDK is properly installed
echo 2. Test the Java application with MinIO integration
echo 3. Check the logs for any runtime issues
echo.

pause

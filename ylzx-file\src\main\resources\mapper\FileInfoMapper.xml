<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylzx.file.mapper.FileInfoMapper">

    <!-- 结果映射 -->
    <resultMap id="FileInfoResult" type="com.ylzx.file.domain.FileInfo">
        <id property="id" column="id"/>
        <result property="fileName" column="file_name"/>
        <result property="originalFilename" column="original_filename"/>
        <result property="fileSize" column="file_size"/>
        <result property="contentType" column="content_type"/>
        <result property="fileExtension" column="file_extension"/>
        <result property="filePath" column="file_path"/>
        <result property="fileUrl" column="file_url"/>
        <result property="md5Hash" column="md5_hash"/>
        <result property="thumbnailPath" column="thumbnail_path"/>
        <result property="thumbnailUrl" column="thumbnail_url"/>
        <result property="thumbnailSize" column="thumbnail_size"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="selectFileInfoVo">
        SELECT id, file_name, original_filename, file_size, content_type, file_extension,
               file_path, file_url, md5_hash, thumbnail_path, thumbnail_url, thumbnail_size,
               status, create_time, update_time, create_by, update_by, remark
        FROM file_info
    </sql>

    <!-- 根据MD5哈希查询文件 -->
    <select id="selectByMd5Hash" parameterType="String" resultMap="FileInfoResult">
        <include refid="selectFileInfoVo"/>
        WHERE md5_hash = #{md5Hash} AND status = 1
        LIMIT 1
    </select>

    <!-- 根据文件路径前缀查询文件列表 -->
    <select id="selectByPathPrefix" parameterType="String" resultMap="FileInfoResult">
        <include refid="selectFileInfoVo"/>
        WHERE file_path LIKE CONCAT(#{pathPrefix}, '%') AND status = 1
        ORDER BY file_path
    </select>

    <!-- 统计文件夹下的文件数量 -->
    <select id="countByFolderPath" parameterType="String" resultType="Long">
        SELECT COUNT(1)
        FROM file_info
        WHERE file_path LIKE CONCAT(#{folderPath}, '%') AND status = 1
    </select>

    <!-- 统计文件夹下的文件总大小 -->
    <select id="sumFileSizeByFolderPath" parameterType="String" resultType="Long">
        SELECT COALESCE(SUM(file_size), 0)
        FROM file_info
        WHERE file_path LIKE CONCAT(#{folderPath}, '%') AND status = 1
    </select>

    <!-- 根据文件扩展名统计文件数量 -->
    <select id="countByExtension" parameterType="String" resultType="Long">
        SELECT COUNT(1)
        FROM file_info
        WHERE file_extension = #{extension} AND status = 1
    </select>

    <!-- 批量更新文件路径 -->
    <update id="batchUpdateFilePath">
        UPDATE file_info
        SET file_path = REPLACE(file_path, #{oldPathPrefix}, #{newPathPrefix}),
            file_url = REPLACE(file_url, #{oldPathPrefix}, #{newPathPrefix}),
            update_time = CURRENT_TIMESTAMP
        WHERE id IN
        <foreach collection="fileIds" item="fileId" open="(" separator="," close=")">
            #{fileId}
        </foreach>
        AND status = 1
    </update>

    <!-- 查询重复文件（相同MD5） -->
    <select id="selectDuplicateFiles" resultMap="FileInfoResult">
        <include refid="selectFileInfoVo"/>
        WHERE md5_hash IN (
            SELECT md5_hash
            FROM file_info
            WHERE status = 1 AND md5_hash IS NOT NULL
            GROUP BY md5_hash
            HAVING COUNT(1) > 1
        )
        AND status = 1
        ORDER BY md5_hash, create_time
    </select>

    <!-- 查询孤儿文件 -->
    <select id="selectOrphanFiles" resultMap="FileInfoResult">
        <include refid="selectFileInfoVo"/>
        WHERE status = 1
        <if test="existingPaths != null and existingPaths.size() > 0">
            AND file_path NOT IN
            <foreach collection="existingPaths" item="path" open="(" separator="," close=")">
                #{path}
            </foreach>
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 自定义查询：按时间范围统计文件上传量 -->
    <select id="selectUploadStatsByDateRange" resultType="map">
        SELECT 
            DATE(create_time) as upload_date,
            COUNT(1) as file_count,
            SUM(file_size) as total_size
        FROM file_info
        WHERE status = 1
        <if test="startDate != null">
            AND create_time >= #{startDate}
        </if>
        <if test="endDate != null">
            AND create_time &lt;= #{endDate}
        </if>
        GROUP BY DATE(create_time)
        ORDER BY upload_date DESC
    </select>

    <!-- 自定义查询：按文件类型统计 -->
    <select id="selectFileTypeStats" resultType="map">
        SELECT 
            file_extension,
            COUNT(1) as file_count,
            SUM(file_size) as total_size,
            AVG(file_size) as avg_size
        FROM file_info
        WHERE status = 1
        GROUP BY file_extension
        ORDER BY file_count DESC
    </select>

    <!-- 自定义查询：查询大文件 -->
    <select id="selectLargeFiles" resultMap="FileInfoResult">
        <include refid="selectFileInfoVo"/>
        WHERE status = 1 AND file_size > #{minSize}
        ORDER BY file_size DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 自定义查询：查询最近上传的文件 -->
    <select id="selectRecentFiles" resultMap="FileInfoResult">
        <include refid="selectFileInfoVo"/>
        WHERE status = 1
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

</mapper>

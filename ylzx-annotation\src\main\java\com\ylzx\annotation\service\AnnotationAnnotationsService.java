package com.ylzx.annotation.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylzx.annotation.domain.AnnotationAnnotations;

/**
 * 标注Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface AnnotationAnnotationsService  extends IService<AnnotationAnnotations>
{
    /**
     * 查询标注
     * 
     * @param annotationId 标注主键
     * @return 标注
     */
    AnnotationAnnotations selectAnnotationAnnotationsByAnnotationId(Long annotationId);

    /**
     * 查询标注列表
     * 
     * @param annotationAnnotations 标注
     * @return 标注集合
     */
    List<AnnotationAnnotations> selectAnnotationAnnotationsList(AnnotationAnnotations annotationAnnotations);

    /**
     * 新增标注
     * 
     * @param annotationAnnotations 标注
     * @return 结果
     */
    int insertAnnotationAnnotations(AnnotationAnnotations annotationAnnotations);

    /**
     * 修改标注
     * 
     * @param annotationAnnotations 标注
     * @return 结果
     */
    int updateAnnotationAnnotations(AnnotationAnnotations annotationAnnotations);

    /**
     * 修改标注状态
     * 
     * @param annotationAnnotations 标注
     * @return 结果
     */
    int updateAnnotationAnnotationsStatus(AnnotationAnnotations annotationAnnotations);

    /**
     * 批量删除标注
     * 
     * @param annotationIds 需要删除的标注主键集合
     * @return 结果
     */
    int deleteAnnotationAnnotationsByAnnotationIds(Long[] annotationIds);

    /**
     * 删除标注信息
     * 
     * @param annotationId 标注主键
     * @return 结果
     */
    int deleteAnnotationAnnotationsByAnnotationId(Long annotationId);

    /**
     * 【SQL优化】根据单个标注ID，一次性查询同一个图片和类别下的所有标注的整体状态
     *
     * @param annotationId 标注ID
     * @return 整体状态 ('1': 未审核完, '2': 审核通过, '3': 审核不通过)
     */
    AnnotationAnnotations checkOverallStatusByAnnotationId(Long annotationId);


    /**
     *
     * @param annotationAnnotationsList
     * @return
     */
    int insertAnnotationAnnotationsBatch(List<AnnotationAnnotations> annotationAnnotationsList);


    /**
     *
     * @param annotationAnnotations
     * @return
     */
    int deleteAnnotationAnnotations(AnnotationAnnotations annotationAnnotations);
}

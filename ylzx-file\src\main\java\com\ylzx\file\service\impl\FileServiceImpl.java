package com.ylzx.file.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylzx.file.config.MinioProperties;
import com.ylzx.file.domain.FileInfo;
import com.ylzx.file.domain.dto.*;
import com.ylzx.file.mapper.FileInfoMapper;
import com.ylzx.file.service.FileService;
import com.ylzx.file.util.FileUtil;
import com.ylzx.file.util.MinioUtil;
import com.ylzx.file.util.ZipUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文件服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileServiceImpl implements FileService {

    private final MinioUtil minioUtil;
    private final MinioProperties minioProperties;
    private final FileInfoMapper fileInfoMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FileUploadResponse uploadFile(MultipartFile file, FileUploadRequest request) {
        try {
            // 1. 验证文件
            validateFile(file);

            // 2. 计算文件MD5
            String md5Hash = FileUtil.calculateMD5(file);
            
            // 3. 检查文件是否已存在（去重）
            FileInfo existingFile = checkFileExists(md5Hash);
            if (existingFile != null) {
                log.info("文件已存在，返回已有文件信息：{}", existingFile.getOriginalFilename());
                return convertToUploadResponse(existingFile);
            }

            // 4. 生成文件名和路径
            String fileName = FileUtil.generateFileName(file.getOriginalFilename());
            String filePath = FileUtil.generateFilePath(request.getFolderPath(), fileName);

            // 5. 确保存储桶存在
            String bucketName = minioProperties.getBucketName();
            if (!minioUtil.bucketExists(bucketName)) {
                minioUtil.createBucket(bucketName);
            }

            // 6. 上传文件到MinIO
            boolean uploadSuccess = minioUtil.uploadFile(bucketName, filePath, file);
            if (!uploadSuccess) {
                throw new RuntimeException("文件上传到MinIO失败");
            }

            // 7. 获取图片尺寸信息（如果是图片文件）
            Integer imageWidth = null;
            Integer imageHeight = null;
            if (FileUtil.isImageFile(file.getOriginalFilename(), file.getContentType())) {
                try {
                    FileUtil.ImageDimensions dimensions = FileUtil.getImageDimensions(file.getInputStream());
                    if (dimensions != null) {
                        imageWidth = dimensions.getWidth();
                        imageHeight = dimensions.getHeight();
                        log.debug("获取图片尺寸: {}x{} - {}", imageWidth, imageHeight, file.getOriginalFilename());
                    }
                } catch (Exception e) {
                    log.warn("获取图片尺寸失败: {} - {}", file.getOriginalFilename(), e.getMessage());
                }
            }

            // 8. 生成缩略图（如果是图片且需要）
            String thumbnailPath = null;
            Long thumbnailSize = null;
            if (request.getGenerateThumbnail() && FileUtil.isImageFile(file.getOriginalFilename(), file.getContentType())) {
                thumbnailPath = generateAndUploadThumbnail(file, filePath, request);
                if (StrUtil.isNotBlank(thumbnailPath)) {
                    // 获取缩略图大小
                    var thumbnailInfo = minioUtil.getFileInfo(bucketName, thumbnailPath);
                    if (thumbnailInfo != null) {
                        thumbnailSize = thumbnailInfo.size();
                    }
                }
            }

            // 9. 保存文件信息到数据库
            FileInfo fileInfo = new FileInfo();
            fileInfo.setFileName(fileName);
            fileInfo.setOriginalFilename(file.getOriginalFilename());
            fileInfo.setFileSize(file.getSize());
            fileInfo.setContentType(file.getContentType());
            fileInfo.setFileExtension(FileUtil.getFileExtension(file.getOriginalFilename()));
            fileInfo.setFilePath(filePath);
            fileInfo.setFileUrl(generateFileUrl(fileInfo.getId()));
            fileInfo.setMd5Hash(md5Hash);
            fileInfo.setThumbnailPath(thumbnailPath);
            fileInfo.setThumbnailUrl(StrUtil.isNotBlank(thumbnailPath) ? generateThumbnailUrl(fileInfo.getId()) : null);
            fileInfo.setThumbnailSize(thumbnailSize);
            fileInfo.setImageWidth(imageWidth);
            fileInfo.setImageHeight(imageHeight);
            fileInfo.setStatus(1);
            fileInfo.setCreateBy(request.getCreateBy());
            fileInfo.setUpdateBy(request.getCreateBy());
            fileInfo.setRemark(request.getRemark());

            fileInfoMapper.insert(fileInfo);

            log.info("文件上传成功：{}", file.getOriginalFilename());
            return convertToUploadResponse(fileInfo);

        } catch (Exception e) {
            log.error("文件上传失败：{}", e.getMessage(), e);
            throw new RuntimeException("文件上传失败：" + e.getMessage());
        }
    }

    @Override
    public List<FileUploadResponse> uploadFiles(List<MultipartFile> files, FileUploadRequest request) {
        List<FileUploadResponse> responses = new ArrayList<>();
        for (MultipartFile file : files) {
            try {
                FileUploadResponse response = uploadFile(file, request);
                responses.add(response);
            } catch (Exception e) {
                log.error("批量上传文件失败：{}", file.getOriginalFilename(), e);
                // 继续处理其他文件
            }
        }
        return responses;
    }

    @Override
    public InputStream downloadFile(String fileId) {
        try {
            FileInfo fileInfo = fileInfoMapper.selectById(fileId);
            if (fileInfo == null) {
                throw new RuntimeException("文件不存在");
            }

            String bucketName = minioProperties.getBucketName();
            return minioUtil.downloadFile(bucketName, fileInfo.getFilePath());
        } catch (Exception e) {
            log.error("文件下载失败：{}", e.getMessage(), e);
            throw new RuntimeException("文件下载失败：" + e.getMessage());
        }
    }

    @Override
    public FileInfo getFileInfo(String fileId) {
        return fileInfoMapper.selectById(fileId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFile(String fileId) {
        try {
            FileInfo fileInfo = fileInfoMapper.selectById(fileId);
            if (fileInfo == null) {
                return false;
            }

            String bucketName = minioProperties.getBucketName();
            
            // 删除MinIO中的文件
            minioUtil.deleteFile(bucketName, fileInfo.getFilePath());
            
            // 删除缩略图
            if (StrUtil.isNotBlank(fileInfo.getThumbnailPath())) {
                minioUtil.deleteFile(bucketName, fileInfo.getThumbnailPath());
            }

            // 软删除数据库记录
            fileInfoMapper.deleteById(fileId);

            log.info("文件删除成功：{}", fileInfo.getOriginalFilename());
            return true;
        } catch (Exception e) {
            log.error("文件删除失败：{}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteFiles(List<String> fileIds) {
        try {
            List<String> objectNames = new ArrayList<>();
            List<FileInfo> fileInfos = fileInfoMapper.selectBatchIds(fileIds);
            
            for (FileInfo fileInfo : fileInfos) {
                objectNames.add(fileInfo.getFilePath());
                if (StrUtil.isNotBlank(fileInfo.getThumbnailPath())) {
                    objectNames.add(fileInfo.getThumbnailPath());
                }
            }

            // 批量删除MinIO中的文件
            String bucketName = minioProperties.getBucketName();
            minioUtil.deleteFiles(bucketName, objectNames);

            // 批量软删除数据库记录
            fileInfoMapper.deleteBatchIds(fileIds);

            log.info("批量删除文件成功，数量：{}", fileIds.size());
            return true;
        } catch (Exception e) {
            log.error("批量删除文件失败：{}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public IPage<FileInfo> queryFiles(FileQueryRequest request) {
        Page<FileInfo> page = new Page<>(request.getPageNum(), request.getPageSize());
        
        LambdaQueryWrapper<FileInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(StrUtil.isNotBlank(request.getFileName()), FileInfo::getFileName, request.getFileName())
               .like(StrUtil.isNotBlank(request.getOriginalFilename()), FileInfo::getOriginalFilename, request.getOriginalFilename())
               .eq(StrUtil.isNotBlank(request.getFileExtension()), FileInfo::getFileExtension, request.getFileExtension())
               .like(StrUtil.isNotBlank(request.getFolderPath()), FileInfo::getFilePath, request.getFolderPath())
               .eq(StrUtil.isNotBlank(request.getContentType()), FileInfo::getContentType, request.getContentType())
               .ge(request.getMinFileSize() != null, FileInfo::getFileSize, request.getMinFileSize())
               .le(request.getMaxFileSize() != null, FileInfo::getFileSize, request.getMaxFileSize())
               .ge(request.getStartTime() != null, FileInfo::getCreateTime, request.getStartTime())
               .le(request.getEndTime() != null, FileInfo::getCreateTime, request.getEndTime())
               .eq(StrUtil.isNotBlank(request.getCreateBy()), FileInfo::getCreateBy, request.getCreateBy());

        // 排序
        if ("asc".equalsIgnoreCase(request.getOrderDirection())) {
            switch (request.getOrderBy()) {
                case "fileName":
                    wrapper.orderByAsc(FileInfo::getFileName);
                    break;
                case "fileSize":
                    wrapper.orderByAsc(FileInfo::getFileSize);
                    break;
                case "createTime":
                    wrapper.orderByAsc(FileInfo::getCreateTime);
                    break;
                default:
                    wrapper.orderByAsc(FileInfo::getCreateTime);
            }
        } else {
            switch (request.getOrderBy()) {
                case "fileName":
                    wrapper.orderByDesc(FileInfo::getFileName);
                    break;
                case "fileSize":
                    wrapper.orderByDesc(FileInfo::getFileSize);
                    break;
                case "createTime":
                    wrapper.orderByDesc(FileInfo::getCreateTime);
                    break;
                default:
                    wrapper.orderByDesc(FileInfo::getCreateTime);
            }
        }

        return fileInfoMapper.selectPage(page, wrapper);
    }

    /**
     * 验证文件
     */
    private void validateFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("文件不能为空");
        }
        
        if (StrUtil.isBlank(file.getOriginalFilename())) {
            throw new RuntimeException("文件名不能为空");
        }

        // 可以添加更多验证逻辑，如文件大小限制、文件类型限制等
        long maxSize = 100 * 1024 * 1024; // 100MB
        if (file.getSize() > maxSize) {
            throw new RuntimeException("文件大小不能超过100MB");
        }
    }

    /**
     * 生成并上传缩略图
     */
    private String generateAndUploadThumbnail(MultipartFile file, String originalPath, FileUploadRequest request) {
        try {
            byte[] thumbnailBytes = FileUtil.generateThumbnail(
                file.getInputStream(), 
                request.getThumbnailWidth(), 
                request.getThumbnailHeight()
            );
            
            if (thumbnailBytes == null) {
                return null;
            }

            String thumbnailFileName = FileUtil.generateThumbnailFileName(FileUtil.generateFileName(file.getOriginalFilename()));
            String thumbnailPath = FileUtil.generateFilePath(request.getFolderPath() + "/thumbnails", thumbnailFileName);

            boolean uploadSuccess = minioUtil.uploadFile(
                minioProperties.getBucketName(),
                thumbnailPath,
                new ByteArrayInputStream(thumbnailBytes),
                thumbnailBytes.length,
                "image/jpeg"
            );

            return uploadSuccess ? thumbnailPath : null;
        } catch (Exception e) {
            log.error("生成缩略图失败", e);
            return null;
        }
    }

    /**
     * 生成文件访问URL
     */
    private String generateFileUrl(String fileId) {
        return "/api/file/download/" + fileId;
    }

    /**
     * 生成缩略图访问URL
     */
    private String generateThumbnailUrl(String fileId) {
        return "/api/file/thumbnail/" + fileId;
    }

    /**
     * 转换为上传响应
     */
    private FileUploadResponse convertToUploadResponse(FileInfo fileInfo) {
        FileUploadResponse response = new FileUploadResponse();
        BeanUtil.copyProperties(fileInfo, response);

        // 手动设置图片尺寸信息（确保字段名映射正确）
        response.setImageWidth(fileInfo.getImageWidth());
        response.setImageHeight(fileInfo.getImageHeight());

        return response;
    }

    @Override
    public boolean moveFolder(FolderOperationRequest request) {
        try {
            String sourcePath = FileUtil.cleanPath(request.getSourcePath());
            String targetPath = FileUtil.cleanPath(request.getTargetPath());

            if (StrUtil.isBlank(sourcePath) || StrUtil.isBlank(targetPath)) {
                throw new RuntimeException("源路径和目标路径不能为空");
            }

            // 查询源文件夹下的所有文件
            List<FileInfo> files = listFolderFiles(sourcePath);
            if (files.isEmpty()) {
                log.warn("源文件夹为空：{}", sourcePath);
                return true;
            }

            String bucketName = minioProperties.getBucketName();

            for (FileInfo fileInfo : files) {
                // 计算新的文件路径
                String relativePath = fileInfo.getFilePath().substring(sourcePath.length());
                String newFilePath = targetPath + relativePath;

                // 复制文件到新位置
                boolean copySuccess = minioUtil.copyFile(bucketName, fileInfo.getFilePath(), bucketName, newFilePath);
                if (copySuccess) {
                    // 删除原文件
                    minioUtil.deleteFile(bucketName, fileInfo.getFilePath());

                    // 更新数据库中的文件路径
                    fileInfo.setFilePath(newFilePath);
                    fileInfo.setFileUrl(generateFileUrl(fileInfo.getId()));
                    fileInfo.setUpdateBy(request.getOperator());
                    fileInfo.setUpdateTime(LocalDateTime.now());
                    fileInfoMapper.updateById(fileInfo);

                    // 处理缩略图
                    if (StrUtil.isNotBlank(fileInfo.getThumbnailPath())) {
                        String newThumbnailPath = newFilePath.replace(fileInfo.getFileName(),
                            FileUtil.generateThumbnailFileName(fileInfo.getFileName()));
                        minioUtil.copyFile(bucketName, fileInfo.getThumbnailPath(), bucketName, newThumbnailPath);
                        minioUtil.deleteFile(bucketName, fileInfo.getThumbnailPath());

                        fileInfo.setThumbnailPath(newThumbnailPath);
                        fileInfo.setThumbnailUrl(generateThumbnailUrl(fileInfo.getId()));
                        fileInfoMapper.updateById(fileInfo);
                    }
                }
            }

            log.info("文件夹移动成功：{} -> {}", sourcePath, targetPath);
            return true;
        } catch (Exception e) {
            log.error("文件夹移动失败：{}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FileUploadResponse compressFolder(FolderOperationRequest request) {
        try {
            String sourcePath = FileUtil.cleanPath(request.getSourcePath());
            if (StrUtil.isBlank(sourcePath)) {
                throw new RuntimeException("源文件夹路径不能为空");
            }

            // 查询文件夹下的所有文件
            List<FileInfo> files = listFolderFiles(sourcePath);
            if (files.isEmpty()) {
                throw new RuntimeException("文件夹为空，无法压缩");
            }

            String bucketName = minioProperties.getBucketName();
            Map<String, InputStream> fileStreams = new HashMap<>();
            long totalSize = 0;

            // 收集所有文件流
            for (FileInfo fileInfo : files) {
                try {
                    InputStream inputStream = minioUtil.downloadFile(bucketName, fileInfo.getFilePath());
                    if (inputStream != null) {
                        // 计算相对路径
                        String relativePath = fileInfo.getFilePath();
                        if (relativePath.startsWith(sourcePath + "/")) {
                            relativePath = relativePath.substring(sourcePath.length() + 1);
                        }
                        fileStreams.put(relativePath, inputStream);
                        totalSize += fileInfo.getFileSize();
                    }
                } catch (Exception e) {
                    log.error("获取文件流失败：{}", fileInfo.getFilePath(), e);
                }
            }

            if (fileStreams.isEmpty()) {
                throw new RuntimeException("无法获取文件内容，压缩失败");
            }

            // 生成ZIP文件名
            String zipFileName = StrUtil.isNotBlank(request.getZipFileName()) ?
                request.getZipFileName() : sourcePath.replaceAll("/", "_") + ".zip";
            zipFileName = ZipUtil.validateZipFileName(zipFileName);

            // 压缩文件
            byte[] zipBytes = ZipUtil.compressFiles(fileStreams, zipFileName);
            if (zipBytes == null) {
                throw new RuntimeException("文件压缩失败");
            }

            // 生成压缩文件路径
            String compressedFileName = FileUtil.generateFileName(zipFileName);
            String compressedFilePath = FileUtil.generateFilePath("compressed", compressedFileName);

            // 上传压缩文件到MinIO
            boolean uploadSuccess = minioUtil.uploadFile(
                bucketName,
                compressedFilePath,
                new ByteArrayInputStream(zipBytes),
                zipBytes.length,
                "application/zip"
            );

            if (!uploadSuccess) {
                throw new RuntimeException("压缩文件上传失败");
            }

            // 保存压缩文件信息到数据库
            FileInfo compressedFileInfo = new FileInfo();
            compressedFileInfo.setFileName(compressedFileName);
            compressedFileInfo.setOriginalFilename(zipFileName);
            compressedFileInfo.setFileSize((long) zipBytes.length);
            compressedFileInfo.setContentType("application/zip");
            compressedFileInfo.setFileExtension("zip");
            compressedFileInfo.setFilePath(compressedFilePath);
            compressedFileInfo.setFileUrl(generateFileUrl(compressedFileInfo.getId()));
            compressedFileInfo.setMd5Hash(FileUtil.calculateMD5(new ByteArrayInputStream(zipBytes)));
            compressedFileInfo.setStatus(1);
            compressedFileInfo.setCreateBy(request.getOperator());
            compressedFileInfo.setUpdateBy(request.getOperator());
            compressedFileInfo.setRemark(String.format("压缩文件夹：%s，包含%d个文件，%s",
                sourcePath, files.size(), ZipUtil.formatCompressionInfo(totalSize, zipBytes.length)));

            fileInfoMapper.insert(compressedFileInfo);

            log.info("文件夹压缩成功：{} -> {}，原始大小：{}，压缩后：{}",
                sourcePath, zipFileName, FileUtil.formatFileSize(totalSize), FileUtil.formatFileSize(zipBytes.length));

            return convertToUploadResponse(compressedFileInfo);

        } catch (Exception e) {
            log.error("文件夹压缩失败：{}", e.getMessage(), e);
            throw new RuntimeException("文件夹压缩失败：" + e.getMessage());
        }
    }

    @Override
    public String getPresignedUrl(String fileId) {
        try {
            FileInfo fileInfo = fileInfoMapper.selectById(fileId);
            if (fileInfo == null) {
                throw new RuntimeException("文件不存在");
            }

            String bucketName = minioProperties.getBucketName();
            return minioUtil.getPresignedUrl(bucketName, fileInfo.getFilePath());
        } catch (Exception e) {
            log.error("获取预签名URL失败：{}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public FileInfo checkFileExists(String md5Hash) {
        if (StrUtil.isBlank(md5Hash)) {
            return null;
        }

        LambdaQueryWrapper<FileInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(FileInfo::getMd5Hash, md5Hash)
               .eq(FileInfo::getStatus, 1)
               .last("LIMIT 1");

        return fileInfoMapper.selectOne(wrapper);
    }

    @Override
    public FileUtil.ImageDimensions getImageDimensions(String fileId) {
        try {
            FileInfo fileInfo = fileInfoMapper.selectById(fileId);
            if (fileInfo == null) {
                log.warn("文件不存在: {}", fileId);
                return null;
            }

            // 检查是否为图片文件
            if (!FileUtil.isImageFile(fileInfo.getOriginalFilename(), fileInfo.getContentType())) {
                log.warn("文件不是图片类型: {}", fileInfo.getOriginalFilename());
                return null;
            }

            String bucketName = minioProperties.getBucketName();
            InputStream inputStream = minioUtil.downloadFile(bucketName, fileInfo.getFilePath());
            if (inputStream == null) {
                log.error("无法从MinIO下载文件: {}", fileInfo.getFilePath());
                return null;
            }

            return FileUtil.getImageDimensions(inputStream);
        } catch (Exception e) {
            log.error("获取图片尺寸失败: {}", fileId, e);
            return null;
        }
    }

    @Override
    public List<FileInfo> listFolderFiles(String folderPath) {
        String cleanPath = FileUtil.cleanPath(folderPath);

        LambdaQueryWrapper<FileInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.likeRight(FileInfo::getFilePath, cleanPath)
               .eq(FileInfo::getStatus, 1)
               .orderByAsc(FileInfo::getFilePath);

        return fileInfoMapper.selectList(wrapper);
    }

    @Override
    public List<FileInfo> listArchiveFilesInMinIO(String folderPath) {
        String cleanPath = FileUtil.cleanPath(folderPath);

        LambdaQueryWrapper<FileInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.likeRight(FileInfo::getFilePath, cleanPath)
               .eq(FileInfo::getStatus, 1)
               .and(w -> w.like(FileInfo::getFileExtension, "zip")
                         .or()
                         .like(FileInfo::getFileExtension, "tar.gz")
                         .or()
                         .like(FileInfo::getFileExtension, "tgz"))
               .orderByAsc(FileInfo::getFilePath);

        return fileInfoMapper.selectList(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FileUploadResponse copyFile(String sourceFileId, String targetPath, String newFileName) {
        try {
            FileInfo sourceFile = fileInfoMapper.selectById(sourceFileId);
            if (sourceFile == null) {
                throw new RuntimeException("源文件不存在");
            }

            String bucketName = minioProperties.getBucketName();

            // 生成新文件名和路径
            String fileName = StrUtil.isNotBlank(newFileName) ? newFileName :
                FileUtil.generateFileName(sourceFile.getOriginalFilename());
            String filePath = FileUtil.generateFilePath(targetPath, fileName);

            // 复制文件
            boolean copySuccess = minioUtil.copyFile(bucketName, sourceFile.getFilePath(), bucketName, filePath);
            if (!copySuccess) {
                throw new RuntimeException("文件复制失败");
            }

            // 创建新的文件记录
            FileInfo newFileInfo = new FileInfo();
            BeanUtil.copyProperties(sourceFile, newFileInfo);
            newFileInfo.setId(null); // 让数据库自动生成新ID
            newFileInfo.setFileName(fileName);
            newFileInfo.setFilePath(filePath);
            newFileInfo.setFileUrl(generateFileUrl(newFileInfo.getId()));
            newFileInfo.setCreateTime(LocalDateTime.now());
            newFileInfo.setUpdateTime(LocalDateTime.now());

            // 复制缩略图
            if (StrUtil.isNotBlank(sourceFile.getThumbnailPath())) {
                String thumbnailFileName = FileUtil.generateThumbnailFileName(fileName);
                String thumbnailPath = FileUtil.generateFilePath(targetPath + "/thumbnails", thumbnailFileName);

                boolean thumbnailCopySuccess = minioUtil.copyFile(bucketName, sourceFile.getThumbnailPath(),
                    bucketName, thumbnailPath);
                if (thumbnailCopySuccess) {
                    newFileInfo.setThumbnailPath(thumbnailPath);
                    newFileInfo.setThumbnailUrl(generateThumbnailUrl(newFileInfo.getId()));
                }
            }

            fileInfoMapper.insert(newFileInfo);

            log.info("文件复制成功：{} -> {}", sourceFile.getOriginalFilename(), fileName);
            return convertToUploadResponse(newFileInfo);
        } catch (Exception e) {
            log.error("文件复制失败：{}", e.getMessage(), e);
            throw new RuntimeException("文件复制失败：" + e.getMessage());
        }
    }

    /**
     * 获取排序字段
     */
    private String getOrderByColumn(String orderBy) {
        return switch (orderBy) {
            case "fileName" -> "file_name";
            case "fileSize" -> "file_size";
            case "createTime" -> "create_time";
            case "updateTime" -> "update_time";
            default -> "create_time";
        };
    }
}

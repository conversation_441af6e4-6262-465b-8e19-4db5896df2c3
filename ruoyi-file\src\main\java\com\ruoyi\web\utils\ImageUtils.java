package com.ruoyi.web.utils;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import javax.imageio.*;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.ColorModel;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;

@Slf4j
public class ImageUtils {

    private static List<String> imageTypeList = new LinkedList<>();

    static {
        imageTypeList.add("jpeg");
        imageTypeList.add("jpg");
        imageTypeList.add("png");
        imageTypeList.add("bmp");
        imageTypeList.add("webp");
    }

    public static BufferedImage resizeImage(int width, int height, BufferedImage bfi) {
        int border = 2;
        int imgWidth = width - border;
        int imgHeight = height - border;
        BufferedImage buffImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        Graphics2D g2d = (Graphics2D) buffImage.getGraphics();

        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setColor(Color.lightGray);
        g2d.fillRect(0, 0, width, height);
        g2d.drawImage(bfi.getScaledInstance(imgWidth, imgHeight, Image.SCALE_SMOOTH), 1, 1, null);
        g2d.dispose();
        return buffImage;
    }

    public static BufferedImage merge(
            List<BufferedImage> imgList,
            int width, int height, int columns, int gutter, int padding) throws IOException {
        if (CollectionUtils.isEmpty(imgList)) {
            throw new IllegalArgumentException("图片列表不能为空！");
        }
        int len = imgList.size();
        int rows = (len > columns) ? ((len % columns) == 0 ? (len / columns) : ((len / columns) + 1)) : 1;
        BufferedImage dest = new BufferedImage((((width + gutter) * (Math.min(len, columns))) - (gutter) + (padding * 2)), rows > 1 ? ((rows * (height + gutter)) - gutter + (padding * 2)) : (height + (padding * 2)), BufferedImage.TYPE_INT_RGB);
        Graphics2D g2d = dest.createGraphics();
        g2d.setColor(Color.white);
        g2d.fillRect(0, 0, dest.getWidth(), dest.getHeight());
        for (int i = 0; i < len; i++) {
            BufferedImage src = imgList.get(i);//ImageIO.read(new FileInputStream(imgList.get(i)));
            BufferedImage fixed = resizeImage(width, height, src);
            int x = (i % columns == 0 ? padding : ((i % columns) * (width + gutter)) + padding);
            int y = (i >= columns ? (((i / columns) * (height + gutter)) + padding) : padding);
            g2d.drawImage(fixed, x, y, width, height, null);
        }
        g2d.dispose();
        return dest;
    }

    public static byte[] compress(BufferedImage image, float compressQuality) throws IOException {
        Iterator<ImageWriter> iter = ImageIO.getImageWritersByFormatName("jpeg");
        ImageWriter writer = (ImageWriter) iter.next();
        ImageWriteParam iwp = writer.getDefaultWriteParam();
        iwp.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
        iwp.setCompressionQuality(compressQuality);
        iwp.setProgressiveMode(ImageWriteParam.MODE_DISABLED);
        ColorModel colorModel = ColorModel.getRGBdefault();
        iwp.setDestinationType(new ImageTypeSpecifier(colorModel, colorModel.createCompatibleSampleModel(16, 16)));
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        IIOImage iImage = new IIOImage(image, null, null);
        writer.setOutput(ImageIO.createImageOutputStream(baos));
        writer.write(null, iImage, iwp);
        return baos.toByteArray();
    }

    public static byte[] mergeAndCompress(
            List<BufferedImage> imgList,
            int width, int height, int columns, int gutter, int padding, float compressQuality) throws IOException {
        BufferedImage image = merge(imgList, width, height, columns, gutter, padding);
        return compress(image, compressQuality);
    }

    public static boolean isImageType(String suffix) {
        if (StrUtil.isEmpty(suffix)) {
            return false;
        }
        return imageTypeList.contains(suffix.toLowerCase());

    }
}


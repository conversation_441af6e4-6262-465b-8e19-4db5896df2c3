<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylzx.annotation.mapper.AnnotationImagesMapper">
    
    <resultMap type="com.ylzx.annotation.domain.AnnotationImages" id="AnnotationImagesResult">
        <result property="imageId"    column="image_id"    />
        <result property="categoryId"    column="category_id"    />
        <result property="originalFilename"    column="original_filename"    />
        <result property="fileId"    column="file_id"    />
        <!-- storage_path 字段暂时忽略，因为实体类中已经移除了该属性 -->
        <result property="md5Hash"    column="md5_hash"    />
        <result property="width"    column="width"    />
        <result property="height"    column="height"    />
        <result property="fileSizeBytes"    column="file_size_bytes"    />
        <result property="sourceId"    column="source_id"    />
        <result property="uploadedAt"    column="uploaded_at"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectAnnotationImagesVo">
        select image_id, category_id, original_filename, file_id, storage_path, md5_hash, width, height, file_size_bytes, source_id, uploaded_at, status, create_time, update_time, create_by, update_by from annotation_images
    </sql>

    <select id="selectAnnotationImagesList" parameterType="AnnotationImages" resultMap="AnnotationImagesResult">
        <include refid="selectAnnotationImagesVo"/>
        <where>  
            <if test="categoryId != null "> and category_id = #{categoryId}</if>
            <if test="originalFilename != null  and originalFilename != ''"> and original_filename like '%' || #{originalFilename} || '%'</if>
            <if test="fileId != null  and fileId != ''"> and file_id = #{fileId}</if>
            <if test="md5Hash != null  and md5Hash != ''"> and md5_hash = #{md5Hash}</if>
            <if test="width != null "> and width = #{width}</if>
            <if test="height != null "> and height = #{height}</if>
            <if test="fileSizeBytes != null "> and file_size_bytes = #{fileSizeBytes}</if>
            <if test="sourceId != null "> and source_id = #{sourceId}</if>
            <if test="uploadedAt != null  and uploadedAt != ''"> and uploaded_at = #{uploadedAt}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectAnnotationImagesByImageId" parameterType="Long" resultMap="AnnotationImagesResult">
        <include refid="selectAnnotationImagesVo"/>
        where image_id = #{imageId}
    </select>

    <insert id="insertAnnotationImages" parameterType="AnnotationImages" useGeneratedKeys="true" keyProperty="imageId">
        insert into annotation_images
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">category_id,</if>
            <if test="originalFilename != null">original_filename,</if>
            <if test="fileId != null and fileId != ''">file_id,</if>
            <if test="fileId != null and fileId != ''">storage_path,</if>
            <if test="md5Hash != null and md5Hash != ''">md5_hash,</if>
            <if test="width != null">width,</if>
            <if test="height != null">height,</if>
            <if test="fileSizeBytes != null">file_size_bytes,</if>
            <if test="sourceId != null">source_id,</if>
            <if test="uploadedAt != null">uploaded_at,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">#{categoryId},</if>
            <if test="originalFilename != null">#{originalFilename},</if>
            <if test="fileId != null and fileId != ''">#{fileId},</if>
            <if test="fileId != null and fileId != ''">#{fileId},</if>
            <if test="md5Hash != null and md5Hash != ''">#{md5Hash},</if>
            <if test="width != null">#{width},</if>
            <if test="height != null">#{height},</if>
            <if test="fileSizeBytes != null">#{fileSizeBytes},</if>
            <if test="sourceId != null">#{sourceId},</if>
            <if test="uploadedAt != null">#{uploadedAt},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateAnnotationImages" parameterType="AnnotationImages">
        update annotation_images
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="originalFilename != null">original_filename = #{originalFilename},</if>
            <if test="fileId != null and fileId != ''">file_id = #{fileId},</if>
            <if test="md5Hash != null and md5Hash != ''">md5_hash = #{md5Hash},</if>
            <if test="width != null">width = #{width},</if>
            <if test="height != null">height = #{height},</if>
            <if test="fileSizeBytes != null">file_size_bytes = #{fileSizeBytes},</if>
            <if test="sourceId != null">source_id = #{sourceId},</if>
            <if test="uploadedAt != null">uploaded_at = #{uploadedAt},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where image_id = #{imageId}
    </update>

    <delete id="deleteAnnotationImagesByImageId" parameterType="Long">
        delete from annotation_images where image_id = #{imageId}
    </delete>

    <delete id="deleteAnnotationImagesByImageIds" parameterType="String">
        delete from annotation_images where image_id in 
        <foreach item="imageId" collection="array" open="(" separator="," close=")">
            #{imageId}
        </foreach>
    </delete>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into annotation_images (
            category_id, original_filename, file_id, md5_hash, width, height,
            file_size_bytes, source_id, uploaded_at, status, create_time, update_time,
            create_by, update_by
        )
        values
        <foreach collection="list" item="item" separator=",">
            (
                #{item.categoryId}, #{item.originalFilename}, #{item.fileId}, #{item.md5Hash},
                #{item.width}, #{item.height}, #{item.fileSizeBytes}, #{item.sourceId},
                #{item.uploadedAt}, #{item.status}, #{item.createTime}, #{item.updateTime},
                #{item.createBy}, #{item.updateBy}
            )
        </foreach>
    </insert>

    <update id="updateBatch" parameterType="java.util.List">
        update annotation_images
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="category_id = case" suffix="end,">
                <foreach collection="list" item="item">
                    when image_id = #{item.imageId} then #{item.categoryId}
                </foreach>
            </trim>
            <trim prefix="original_filename = case" suffix="end,">
                <foreach collection="list" item="item">
                    when image_id = #{item.imageId} then #{item.originalFilename}
                </foreach>
            </trim>
            <trim prefix="file_id = case" suffix="end,">
                <foreach collection="list" item="item">
                    when image_id = #{item.imageId} then #{item.fileId}
                </foreach>
            </trim>
            <trim prefix="md5_hash = case" suffix="end,">
                <foreach collection="list" item="item">
                    when image_id = #{item.imageId} then #{item.md5Hash}
                </foreach>
            </trim>
            <trim prefix="width = case" suffix="end,">
                <foreach collection="list" item="item">
                    when image_id = #{item.imageId} then #{item.width}
                </foreach>
            </trim>
            <trim prefix="height = case" suffix="end,">
                <foreach collection="list" item="item">
                    when image_id = #{item.imageId} then #{item.height}
                </foreach>
            </trim>
            <trim prefix="file_size_bytes = case" suffix="end,">
                <foreach collection="list" item="item">
                    when image_id = #{item.imageId} then #{item.fileSizeBytes}
                </foreach>
            </trim>
            <trim prefix="source_id = case" suffix="end,">
                <foreach collection="list" item="item">
                    when image_id = #{item.imageId} then #{item.sourceId}
                </foreach>
            </trim>
            <trim prefix="uploaded_at = case" suffix="end,">
                <foreach collection="list" item="item">
                    when image_id = #{item.imageId} then #{item.uploadedAt}
                </foreach>
            </trim>
            <trim prefix="status = case" suffix="end,">
                <foreach collection="list" item="item">
                    when image_id = #{item.imageId} then #{item.status}
                </foreach>
            </trim>
            <trim prefix="update_time = case" suffix="end,">
                <foreach collection="list" item="item">
                    when image_id = #{item.imageId} then #{item.updateTime}
                </foreach>
            </trim>
            <trim prefix="update_by = case" suffix="end,">
                <foreach collection="list" item="item">
                    when image_id = #{item.imageId} then #{item.updateBy}
                </foreach>
            </trim>
        </trim>
        where image_id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item.imageId}
        </foreach>
    </update>

    <!-- 检查MD5哈希是否存在 -->
    <select id="countByMd5Hash" parameterType="String" resultType="int">
        SELECT COUNT(1) FROM annotation_images WHERE md5_hash = #{md5Hash}
    </select>
</mapper>
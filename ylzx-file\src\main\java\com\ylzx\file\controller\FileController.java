package com.ylzx.file.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ylzx.common.core.domain.AjaxResult;
import com.ylzx.common.core.page.TableDataInfo;
import com.ylzx.file.domain.FileInfo;
import com.ylzx.file.domain.dto.*;
import com.ylzx.file.service.FileService;
import com.ylzx.file.util.FileUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 文件管理控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/file")
@RequiredArgsConstructor
@Tag(name = "文件管理", description = "文件上传、下载、管理等功能")
public class FileController {

    private final FileService fileService;

    /**
     * 单文件上传
     */
    @PostMapping("/upload")
    @Operation(summary = "单文件上传", description = "上传单个文件到MinIO存储")
    public AjaxResult uploadFile(
            @Parameter(description = "上传的文件") @RequestParam("file") MultipartFile file,
            @Parameter(description = "上传参数") @ModelAttribute FileUploadRequest request) {

        if (file == null || file.isEmpty()) {
            return AjaxResult.error("上传文件不能为空");
        }

        try {
            FileUploadResponse response = fileService.uploadFile(file, request);
            return AjaxResult.success("文件上传成功", response);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return AjaxResult.error("文件上传失败：" + e.getMessage());
        }
    }

    /**
     * 批量文件上传
     */
    @PostMapping("/upload/batch")
    @Operation(summary = "批量文件上传", description = "批量上传多个文件到MinIO存储")
    public AjaxResult uploadFiles(
            @Parameter(description = "上传的文件列表") @RequestParam("files") List<MultipartFile> files,
            @Parameter(description = "上传参数") @ModelAttribute FileUploadRequest request) {

        if (files == null || files.isEmpty()) {
            return AjaxResult.error("上传文件列表不能为空");
        }

        try {
            List<FileUploadResponse> responses = fileService.uploadFiles(files, request);
            AjaxResult result = AjaxResult.success("批量上传完成", responses);
            result.put("total", responses.size());
            return result;
        } catch (Exception e) {
            log.error("批量文件上传失败", e);
            return AjaxResult.error("批量上传失败：" + e.getMessage());
        }
    }

    /**
     * 文件下载
     */
    @GetMapping("/download/{fileId}")
    @Operation(summary = "文件下载", description = "根据文件ID下载文件")
    public ResponseEntity<InputStreamResource> downloadFile(
            @Parameter(description = "文件ID") @PathVariable String fileId) {
        
        try {
            FileInfo fileInfo = fileService.getFileInfo(fileId);
            if (fileInfo == null) {
                return ResponseEntity.notFound().build();
            }

            InputStream inputStream = fileService.downloadFile(fileId);
            if (inputStream == null) {
                return ResponseEntity.notFound().build();
            }

            String encodedFilename = URLEncoder.encode(fileInfo.getOriginalFilename(), StandardCharsets.UTF_8);
            
            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + encodedFilename + "\"")
                    .header(HttpHeaders.CONTENT_TYPE, fileInfo.getContentType())
                    .header(HttpHeaders.CONTENT_LENGTH, String.valueOf(fileInfo.getFileSize()))
                    .body(new InputStreamResource(inputStream));
                    
        } catch (Exception e) {
            log.error("文件下载失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 缩略图下载
     */
    @GetMapping("/thumbnail/{fileId}")
    @Operation(summary = "缩略图下载", description = "根据文件ID下载缩略图")
    public ResponseEntity<InputStreamResource> downloadThumbnail(
            @Parameter(description = "文件ID") @PathVariable String fileId) {
        
        try {
            FileInfo fileInfo = fileService.getFileInfo(fileId);
            if (fileInfo == null || fileInfo.getThumbnailPath() == null) {
                return ResponseEntity.notFound().build();
            }

            // 这里需要实现缩略图下载逻辑
            // 暂时返回原文件
            InputStream inputStream = fileService.downloadFile(fileId);
            if (inputStream == null) {
                return ResponseEntity.notFound().build();
            }

            return ResponseEntity.ok()
                    .header(HttpHeaders.CONTENT_TYPE, "image/jpeg")
                    .body(new InputStreamResource(inputStream));
                    
        } catch (Exception e) {
            log.error("缩略图下载失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * 获取文件信息
     */
    @GetMapping("/info/{fileId}")
    @Operation(summary = "获取文件信息", description = "根据文件ID获取文件详细信息")
    public AjaxResult getFileInfo(
            @Parameter(description = "文件ID") @PathVariable String fileId) {

        try {
            FileInfo fileInfo = fileService.getFileInfo(fileId);
            if (fileInfo != null) {
                return AjaxResult.success("获取文件信息成功", fileInfo);
            } else {
                return AjaxResult.error("文件不存在");
            }
        } catch (Exception e) {
            log.error("获取文件信息失败", e);
            return AjaxResult.error("获取文件信息失败：" + e.getMessage());
        }
    }

    /**
     * 删除文件
     */
    @DeleteMapping("/{fileId}")
    @Operation(summary = "删除文件", description = "根据文件ID删除文件")
    public AjaxResult deleteFile(
            @Parameter(description = "文件ID") @PathVariable String fileId) {

        try {
            boolean success = fileService.deleteFile(fileId);
            return success ? AjaxResult.success("文件删除成功") : AjaxResult.error("文件删除失败");
        } catch (Exception e) {
            log.error("删除文件失败", e);
            return AjaxResult.error("删除文件失败：" + e.getMessage());
        }
    }

    /**
     * 批量删除文件
     */
    @DeleteMapping("/batch")
    @Operation(summary = "批量删除文件", description = "根据文件ID列表批量删除文件")
    public AjaxResult deleteFiles(
            @Parameter(description = "文件ID列表") @RequestBody List<String> fileIds) {

        if (fileIds == null || fileIds.isEmpty()) {
            return AjaxResult.error("文件ID列表不能为空");
        }

        try {
            boolean success = fileService.deleteFiles(fileIds);
            return success ? AjaxResult.success("批量删除成功") : AjaxResult.error("批量删除失败");
        } catch (Exception e) {
            log.error("批量删除文件失败", e);
            return AjaxResult.error("批量删除失败：" + e.getMessage());
        }
    }

    /**
     * 查询文件列表
     */
    @PostMapping("/query")
    @Operation(summary = "查询文件列表", description = "根据条件分页查询文件列表")
    public TableDataInfo queryFiles(
            @Parameter(description = "查询条件") @RequestBody @Valid FileQueryRequest request) {

        try {
            IPage<FileInfo> page = fileService.queryFiles(request);
            return getDataTable(page.getRecords(), page.getTotal());
        } catch (Exception e) {
            log.error("查询文件列表失败", e);
            return getDataTable(null, 0L);
        }
    }

    /**
     * 构建分页数据
     */
    private TableDataInfo getDataTable(List<FileInfo> list, Long total) {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(200);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        rspData.setTotal(total);
        return rspData;
    }

    /**
     * 获取预签名URL
     */
    @GetMapping("/presigned-url/{fileId}")
    @Operation(summary = "获取预签名URL", description = "获取文件的预签名访问URL")
    public AjaxResult getPresignedUrl(
            @Parameter(description = "文件ID") @PathVariable String fileId) {

        try {
            String presignedUrl = fileService.getPresignedUrl(fileId);
            if (presignedUrl != null) {
                return AjaxResult.success("获取预签名URL成功", presignedUrl);
            } else {
                return AjaxResult.error("获取预签名URL失败");
            }
        } catch (Exception e) {
            log.error("获取预签名URL失败", e);
            return AjaxResult.error("获取预签名URL失败：" + e.getMessage());
        }
    }
}

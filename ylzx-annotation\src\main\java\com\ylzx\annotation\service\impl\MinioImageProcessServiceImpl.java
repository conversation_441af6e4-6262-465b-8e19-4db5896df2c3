package com.ylzx.annotation.service.impl;

import com.ylzx.annotation.domain.AnnotationAnnotations;
import com.ylzx.annotation.domain.AnnotationImages;
import com.ylzx.annotation.domain.AnnotationProjectConfigs;
import com.ylzx.annotation.jni.DatasetExportNative;
import com.ylzx.annotation.service.AnnotationProjectConfigsService;
import com.ylzx.annotation.service.AnnotationProjectCategoriesService;
import com.ylzx.annotation.service.AnnotationImagesService;
import com.ylzx.annotation.service.AnnotationAnnotationsService;
import com.ylzx.annotation.service.MinioImageProcessService;
import com.ylzx.file.config.MinioProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * MinIO图像处理服务实现类
 * 提供基于MinIO的图像预处理和裁剪功能
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class MinioImageProcessServiceImpl implements MinioImageProcessService {

    @Autowired
    private DatasetExportNative exportNative;

    @Autowired
    private MinioProperties minioProperties;

    @Autowired
    private AnnotationProjectConfigsService projectConfigsService;

    @Autowired
    private AnnotationProjectCategoriesService projectCategoriesService;

    @Autowired
    private AnnotationImagesService imagesService;

    @Autowired
    private AnnotationAnnotationsService annotationsService;

    private boolean minioInitialized = false;

    @PostConstruct
    public void initializeMinioClient() {
        try {
            DatasetExportNative.MinioConfig config = new DatasetExportNative.MinioConfig();
            config.endpoint = minioProperties.getEndpoint();
            config.accessKey = minioProperties.getAccessKey();
            config.secretKey = minioProperties.getSecretKey();
            config.bucketName = minioProperties.getBucketName();
            config.useSSL = minioProperties.getSecure();
            config.region = "us-east-1"; // 默认区域

            minioInitialized = exportNative.initializeMinioClient(config);
            if (minioInitialized) {
                log.info("MinIO客户端初始化成功");
            } else {
                log.error("MinIO客户端初始化失败");
            }
        } catch (Exception e) {
            log.error("MinIO客户端初始化异常", e);
            minioInitialized = false;
        }
    }

    @Override
    public ProcessResult preprocessProjectImages(Long projectId, Date updateTimeThreshold) {
        ProcessResult result = new ProcessResult();
        
        if (!minioInitialized) {
            result.setSuccess(false);
            result.setMessage("MinIO客户端未初始化");
            return result;
        }

        try {
            // 1. 获取项目配置和分类
            AnnotationProjectConfigs config = projectConfigsService.selectConfigWithCategoriesByProjectId(projectId);
            if (config == null) {
                result.setSuccess(false);
                result.setMessage("项目配置不存在: " + projectId);
                return result;
            }

            List<Long> categoryIds = projectCategoriesService.selectCategoryIdsByProjectId(projectId);
            if (categoryIds.isEmpty()) {
                result.setSuccess(false);
                result.setMessage("项目未配置分类: " + projectId);
                return result;
            }

            // 2. 获取需要处理的图片（审核通过且更新时间新于阈值）
            List<AnnotationImages> images = getApprovedImagesAfterTime(categoryIds, updateTimeThreshold);
            if (images.isEmpty()) {
                result.setSuccess(true);
                result.setMessage("没有需要处理的图片");
                result.setTotalCount(0);
                return result;
            }

            // 3. 批量处理图片
            String projectFolder = "projects/" + projectId + "/processed/";
            BatchProcessResult batchResult = batchProcessImages(images, projectFolder, config);

            result.setSuccess(batchResult.getTotalCount() > 0);
            result.setMessage(batchResult.getMessage());
            result.setTotalCount(batchResult.getTotalCount());
            result.setSuccessCount(batchResult.getSuccessCount());
            result.setFailedCount(batchResult.getFailedCount());
            result.setProcessingTimeMs(batchResult.getProcessingTimeMs());

            return result;

        } catch (Exception e) {
            log.error("项目图片预处理失败: {}", projectId, e);
            result.setSuccess(false);
            result.setMessage("预处理异常: " + e.getMessage());
            return result;
        }
    }

    @Override
    public ProcessResult cropImageByAnnotations(AnnotationImages image, List<AnnotationAnnotations> annotations,
                                              String outputBucket, String outputObject, CropConfig cropConfig) {
        ProcessResult result = new ProcessResult();
        
        if (!minioInitialized) {
            result.setSuccess(false);
            result.setMessage("MinIO客户端未初始化");
            return result;
        }

        try {
            // 构建标注坐标字符串
            StringBuilder coordinatesBuilder = new StringBuilder();
            coordinatesBuilder.append("[");
            for (int i = 0; i < annotations.size(); i++) {
                if (i > 0) {
                    coordinatesBuilder.append(",");
                }
                coordinatesBuilder.append(annotations.get(i).getCoordinates());
            }
            coordinatesBuilder.append("]");

            // 转换裁剪配置
            DatasetExportNative.CropConfig nativeConfig = convertToCppConfig(cropConfig);

            // 执行MinIO到MinIO的裁剪
            String sourceBucket = minioProperties.getBucketName();
            String sourceObject = getObjectPathFromFileId(image.getFileId());

            boolean success = exportNative.smartCropMinioToMinio(
                sourceBucket, sourceObject, outputBucket, outputObject,
                coordinatesBuilder.toString(), nativeConfig);

            result.setSuccess(success);
            result.setMessage(success ? "裁剪成功" : "裁剪失败");
            result.setTotalCount(1);
            result.setSuccessCount(success ? 1 : 0);
            result.setFailedCount(success ? 0 : 1);

            return result;

        } catch (Exception e) {
            log.error("图像裁剪失败: {}", image.getImageId(), e);
            result.setSuccess(false);
            result.setMessage("裁剪异常: " + e.getMessage());
            return result;
        }
    }

    @Override
    public BatchProcessResult batchCropImages(List<ImageAnnotationPair> imageAnnotationPairs,
                                            String outputBucketPrefix, CropConfig cropConfig) {
        BatchProcessResult result = new BatchProcessResult();
        
        if (!minioInitialized) {
            result.setSuccess(false);
            result.setMessage("MinIO客户端未初始化");
            return result;
        }

        long startTime = System.currentTimeMillis();

        try {
            // 准备批量处理的数据
            List<String> sourceBuckets = new ArrayList<>();
            List<String> sourceObjects = new ArrayList<>();
            List<String> targetBuckets = new ArrayList<>();
            List<String> targetObjects = new ArrayList<>();
            List<String> annotationCoordinatesArray = new ArrayList<>();

            String sourceBucket = minioProperties.getBucketName();

            for (int i = 0; i < imageAnnotationPairs.size(); i++) {
                ImageAnnotationPair pair = imageAnnotationPairs.get(i);
                AnnotationImages image = pair.getImage();
                List<AnnotationAnnotations> annotations = pair.getAnnotations();

                // 源路径
                sourceBuckets.add(sourceBucket);
                sourceObjects.add(getObjectPathFromFileId(image.getFileId()));

                // 目标路径
                targetBuckets.add(sourceBucket); // 使用同一个bucket
                String outputFileName = generateOutputFileName(image, i);
                String outputPath = outputBucketPrefix + "/" + outputFileName;
                targetObjects.add(outputPath);

                // 标注坐标
                StringBuilder coordinatesBuilder = new StringBuilder();
                coordinatesBuilder.append("[");
                for (int j = 0; j < annotations.size(); j++) {
                    if (j > 0) {
                        coordinatesBuilder.append(",");
                    }
                    coordinatesBuilder.append(annotations.get(j).getCoordinates());
                }
                coordinatesBuilder.append("]");
                annotationCoordinatesArray.add(coordinatesBuilder.toString());
            }

            // 转换裁剪配置
            DatasetExportNative.CropConfig nativeConfig = convertToCppConfig(cropConfig);

            // 调用C++批量MinIO到MinIO裁剪
            DatasetExportNative.BatchCropResult nativeResult = exportNative.batchSmartCropMinioToMinio(
                sourceBuckets.toArray(new String[0]),
                sourceObjects.toArray(new String[0]),
                targetBuckets.toArray(new String[0]),
                targetObjects.toArray(new String[0]),
                annotationCoordinatesArray.toArray(new String[0]),
                nativeConfig,
                Math.min(8, imageAnnotationPairs.size())
            );

            // 转换结果
            result.setSuccess(nativeResult.success);
            result.setMessage(nativeResult.message);
            result.setTotalCount(nativeResult.totalCount);
            result.setSuccessCount(nativeResult.successCount);
            result.setFailedCount(nativeResult.failedCount);

        } catch (Exception e) {
            log.error("批量图像裁剪失败", e);
            result.setSuccess(false);
            result.setMessage("批量裁剪异常: " + e.getMessage());
            result.setTotalCount(imageAnnotationPairs.size());
            result.setSuccessCount(0);
            result.setFailedCount(imageAnnotationPairs.size());
        } finally {
            result.setProcessingTimeMs(System.currentTimeMillis() - startTime);
        }

        return result;
    }

    /**
     * 获取指定时间之后审核通过的图片
     */
    private List<AnnotationImages> getApprovedImagesAfterTime(List<Long> categoryIds, Date updateTimeThreshold) {
        // TODO: 实现根据分类ID和更新时间查询审核通过的图片
        // 这里需要调用相应的Service方法
        return new ArrayList<>();
    }

    /**
     * 批量处理图片
     */
    private BatchProcessResult batchProcessImages(List<AnnotationImages> images, String outputFolder, 
                                                AnnotationProjectConfigs config) {
        // TODO: 实现批量处理逻辑
        // 分批处理，每200张图片一批
        BatchProcessResult result = new BatchProcessResult();
        result.setSuccess(true);
        result.setMessage("批量处理完成");
        result.setTotalCount(images.size());
        result.setSuccessCount(images.size());
        result.setFailedCount(0);
        return result;
    }

    /**
     * 转换为C++配置对象
     */
    private DatasetExportNative.CropConfig convertToCppConfig(CropConfig cropConfig) {
        DatasetExportNative.CropConfig cppConfig = new DatasetExportNative.CropConfig();
        cppConfig.targetWidth = cropConfig.getTargetWidth() != null ? cropConfig.getTargetWidth() : 0;
        cppConfig.targetHeight = cropConfig.getTargetHeight() != null ? cropConfig.getTargetHeight() : 0;
        cppConfig.padding = cropConfig.getPadding();
        cppConfig.enableRandomPlacement = cropConfig.isEnableRandomPlacement();
        cppConfig.backgroundColor = cropConfig.getBackgroundColor();
        cppConfig.maintainAspectRatio = cropConfig.isMaintainAspectRatio();
        return cppConfig;
    }

    /**
     * 从文件ID获取对象路径
     */
    private String getObjectPathFromFileId(String fileId) {
        // TODO: 实现从文件ID到MinIO对象路径的转换
        // 这里需要查询文件信息获取实际的对象路径
        return fileId; // 临时实现
    }

    /**
     * 生成输出文件名
     */
    private String generateOutputFileName(AnnotationImages image, int index) {
        String originalName = image.getOriginalFilename();
        String baseName = originalName.substring(0, originalName.lastIndexOf('.'));
        String extension = originalName.substring(originalName.lastIndexOf('.'));
        return String.format("%s_crop_%d_%d%s", baseName, image.getImageId(), index, extension);
    }
}

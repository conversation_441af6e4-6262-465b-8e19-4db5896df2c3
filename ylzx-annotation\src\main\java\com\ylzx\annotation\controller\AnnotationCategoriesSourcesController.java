package com.ylzx.annotation.controller;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

import com.ylzx.annotation.service.AnnotationCategoriesSourcesService;
import jakarta.annotation.Resource;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ylzx.common.annotation.Log;
import com.ylzx.common.core.controller.BaseController;
import com.ylzx.common.core.domain.AjaxResult;
import com.ylzx.common.enums.BusinessType;
import com.ylzx.annotation.domain.AnnotationCategoriesSources;
import com.ylzx.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 标注分类数据源关联Controller
 * 
 * <AUTHOR>
 * @date 2025-01-24
 */
@Api(tags = "标注分类数据源关联管理")
@RestController
@RequestMapping("/annotation/categoriesSources")
public class AnnotationCategoriesSourcesController extends BaseController
{
    private static final Logger logger = LoggerFactory.getLogger(AnnotationCategoriesSourcesController.class);

    @Resource
    private AnnotationCategoriesSourcesService annotationCategoriesSourcesService;

    /**
     * 查询标注分类数据源关联列表
     */
    @ApiOperation("查询标注分类数据源关联列表")
    @PreAuthorize("@ss.hasPermi('annotation:categoriesSources:list')")
    @GetMapping("/list")
    public TableDataInfo list(AnnotationCategoriesSources annotationCategoriesSources)
    {
        startPage();
        List<AnnotationCategoriesSources> list = annotationCategoriesSourcesService.selectAnnotationCategoriesSourcesList(annotationCategoriesSources);
        return getDataTable(list);
    }

    /**
     * 根据分类ID查询关联的数据源ID列表
     */
    @ApiOperation("根据分类ID查询关联的数据源ID列表")
    @PreAuthorize("@ss.hasPermi('annotation:categoriesSources:list')")
    @GetMapping("/sourcesByCategory/{categoryId}")
    public AjaxResult getSourcesByCategory(@PathVariable("categoryId") Long categoryId) {
        try {
             List<Long> sourceIds = annotationCategoriesSourcesService.getSourceIdsByCategoryId(categoryId);
            return AjaxResult.success(sourceIds);
        } catch (Exception e) {
            logger.error("查询分类关联的数据源失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据数据源ID查询关联的分类ID列表
     */
    @ApiOperation("根据数据源ID查询关联的分类ID列表")
    @PreAuthorize("@ss.hasPermi('annotation:categoriesSources:list')")
    @GetMapping("/categoriesBySource/{sourceId}")
    public AjaxResult getCategoriesBySource(@PathVariable Long sourceId) {
        try {
            List<Long> categoryIds = annotationCategoriesSourcesService.getCategoryIdsBySourceId(sourceId);
            return AjaxResult.success(categoryIds);
        } catch (Exception e) {
            logger.error("查询数据源关联的分类失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 创建分类与数据源的关联
     */
    @ApiOperation("创建分类与数据源的关联")
    @PreAuthorize("@ss.hasPermi('annotation:categoriesSources:add')")
    @Log(title = "分类数据源关联", businessType = BusinessType.INSERT)
    @PostMapping("/associate")
    public AjaxResult associateCategoryWithSource(
            @RequestParam("categoryId") Long categoryId,
            @RequestParam("sourceId") Long sourceId) {
        try {
            // 检查是否已经关联
            if (annotationCategoriesSourcesService.isAssociated(categoryId, sourceId)) {
                return AjaxResult.error("分类和数据源已经关联");
            }
            
            AnnotationCategoriesSources association = annotationCategoriesSourcesService.associateCategoryWithSource(
                categoryId, sourceId, getUserId());
            return AjaxResult.success("关联创建成功", association);
        } catch (Exception e) {
            logger.error("创建分类数据源关联失败", e);
            return AjaxResult.error("创建关联失败: " + e.getMessage());
        }
    }

    /**
     * 批量创建分类与数据源的关联
     */
    @ApiOperation("批量创建分类与数据源的关联")
    @PreAuthorize("@ss.hasPermi('annotation:categoriesSources:add')")
    @Log(title = "分类数据源关联", businessType = BusinessType.INSERT)
    @PostMapping("/batchAssociate")
    public AjaxResult batchAssociateCategoryWithSources(@RequestBody Map<String, Object> params) {
        try {
            Long categoryId = Long.valueOf(params.get("categoryId").toString());
            @SuppressWarnings("unchecked")
            List<Long> sourceIds = (List<Long>) params.get("sourceIds");
            
            int count = annotationCategoriesSourcesService.batchAssociateCategoryWithSources(
                categoryId, sourceIds, getUserId());
            return AjaxResult.success("批量关联成功，创建了 " + count + " 个关联");
        } catch (Exception e) {
            logger.error("批量创建分类数据源关联失败", e);
            return AjaxResult.error("批量关联失败: " + e.getMessage());
        }
    }

    /**
     * 移除分类与数据源的关联
     */
    @ApiOperation("移除分类与数据源的关联")
    @PreAuthorize("@ss.hasPermi('annotation:categoriesSources:remove')")
    @Log(title = "分类数据源关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/remove")
    public AjaxResult removeAssociation(
            @RequestParam("categoryId") Long categoryId,
            @RequestParam("sourceId") Long sourceId) {
        try {
            boolean success = annotationCategoriesSourcesService.removeAssociation(categoryId, sourceId);
            if (success) {
                return AjaxResult.success("关联移除成功");
            } else {
                return AjaxResult.error("关联不存在或移除失败");
            }
        } catch (Exception e) {
            logger.error("移除分类数据源关联失败", e);
            return AjaxResult.error("移除关联失败: " + e.getMessage());
        }
    }

    /**
     * 移除分类的所有数据源关联
     */
    @ApiOperation("移除分类的所有数据源关联")
    @PreAuthorize("@ss.hasPermi('annotation:categoriesSources:remove')")
    @Log(title = "分类数据源关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/removeAllFromCategory/{categoryId}")
    public AjaxResult removeAllSourcesFromCategory(@PathVariable Long categoryId) {
        try {
            int count = annotationCategoriesSourcesService.removeAllSourcesFromCategory(categoryId);
            return AjaxResult.success("成功移除分类的所有关联，共 " + count + " 个");
        } catch (Exception e) {
            logger.error("移除分类所有数据源关联失败", e);
            return AjaxResult.error("移除失败: " + e.getMessage());
        }
    }

    /**
     * 移除数据源的所有分类关联
     */
    @ApiOperation("移除数据源的所有分类关联")
    @PreAuthorize("@ss.hasPermi('annotation:categoriesSources:remove')")
    @Log(title = "分类数据源关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/removeAllFromSource/{sourceId}")
    public AjaxResult removeAllCategoriesFromSource(@PathVariable Long sourceId) {
        try {
            int count = annotationCategoriesSourcesService.removeAllCategoriesFromSource(sourceId);
            return AjaxResult.success("成功移除数据源的所有关联，共 " + count + " 个");
        } catch (Exception e) {
            logger.error("移除数据源所有分类关联失败", e);
            return AjaxResult.error("移除失败: " + e.getMessage());
        }
    }

    /**
     * 获取分类与数据源关联的统计信息
     */
    @ApiOperation("获取分类与数据源关联的统计信息")
    @PreAuthorize("@ss.hasPermi('annotation:categoriesSources:list')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 统计总关联数
            long totalAssociations = annotationCategoriesSourcesService.count();
            stats.put("totalAssociations", totalAssociations);
            
            return AjaxResult.success(stats);
        } catch (Exception e) {
            logger.error("获取统计信息失败", e);
            return AjaxResult.error("获取统计信息失败: " + e.getMessage());
        }
    }
}

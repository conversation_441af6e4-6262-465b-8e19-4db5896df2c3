<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.web.mapper.SysFileInfoSignMapper">
    <resultMap type="com.ruoyi.web.domain.SysFileInfoSign" id="SysFileInfoSignMap">
        <result property="signName" column="sign_name" jdbcType="VARCHAR"/>
        <result property="fileId" column="file_id" jdbcType="VARCHAR"/>
    </resultMap>
    
    <!-- 定义文件签名组合视图的结果映射 -->
    <resultMap type="com.ruoyi.web.domain.vo.FileInfoSignVO" id="FileInfoSignVOMap">
        <!-- BaseTableEntity的字段 -->
        <id property="id" column="id" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        
        <!-- FileInfoEntity的字段 -->
        <result property="ownerId" column="owner_id" jdbcType="VARCHAR"/>
        <result property="platform" column="platform" jdbcType="VARCHAR"/>
        <result property="storagePath" column="storage_path" jdbcType="VARCHAR"/>
        <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
        <result property="originalFilename" column="original_filename" jdbcType="VARCHAR"/>
        <result property="fileSize" column="file_size" jdbcType="BIGINT"/>
        <result property="ext" column="ext" jdbcType="VARCHAR"/>
        <result property="url" column="url" jdbcType="VARCHAR"/>
        <result property="thumbUrl" column="thumb_url" jdbcType="VARCHAR"/>
        <result property="contentType" column="content_type" jdbcType="VARCHAR"/>
        <result property="thFilename" column="th_filename" jdbcType="VARCHAR"/>
        <result property="thSize" column="th_size" jdbcType="BIGINT"/>
        <result property="isStatic" column="is_static" jdbcType="BOOLEAN"/>
        <result property="sort" column="sort" jdbcType="BIGINT"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="delFlag" column="del_flag" jdbcType="BOOLEAN"/>
        
        <!-- FileInfoSignVO的额外字段 -->
        <result property="signName" column="sign_name" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 定义基础列列表 -->
    <sql id="Base_Column_List">
        sys_file_info_sign.name,
        sys_file_info_sign.file_id
    </sql>

    <!-- 定义公共的 WHERE 条件 -->
    <sql id="Common_Where_Clause">
        <where>
            <if test="request.signName != null and request.name != ''">
                AND name = #{request.signName}
            </if>
            <if test="request.fileId != null and request.fileId != ''">
                AND file_id = #{request.fileId}
            </if>
            <if test="request.fileIdList != null and request.fileIdList.size() > 0">
                AND file_id IN
                <foreach collection="request.fileIdList" item="fileId" open="(" separator="," close=")">
                    #{fileId}
                </foreach>
            </if>
            <if test="request.signNameList != null and request.signNameList.size() > 0">
                AND name IN
                <foreach collection="request.signNameList" item="name" open="(" separator="," close=")">
                    #{name}
                </foreach>
            </if>
        </where>
    </sql>

    <!-- 查询总数 -->
    <select id="countAssetBaseData" resultType="int">
        SELECT COUNT(1)
        FROM sys_file_info_sign
        <include refid="Common_Where_Clause"/>
    </select>

    <!-- 查询数据 -->
    <select id="selectAssetBaseData" resultMap="SysFileInfoSignMap">
        <choose>
            <!-- 当 offset 和 pageSize 不为空时，使用分页的 INNER JOIN 模式 -->
            <when test="offset != null and pageSize != null">
                SELECT
                <include refid="Base_Column_List"/>
                FROM (
                SELECT sign_name
                FROM sys_file_info_sign
                <include refid="Common_Where_Clause"/>
                ORDER BY sign_name ASC
                LIMIT #{offset}, #{pageSize}
                ) AS t
                INNER JOIN sys_file_info ON sys_file_info.id = t.file_id
            </when>
            <!-- 当 offset 或 pageSize 为空时，使用普通的查询模式 -->
            <otherwise>
                SELECT
                <include refid="Base_Column_List"/>
                FROM sys_file_info_sign
                <include refid="Common_Where_Clause"/>
                ORDER BY name ASC
            </otherwise>
        </choose>
    </select>

    <!-- 批量插入 -->
    <insert id="insertBatch">
        insert into sys_file_info_sign(sign_name,file_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.signName},#{entity.fileId})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch">
        insert into sys_file_info_sign(sign_name,file_id)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.signName},#{entity.fileId})
        </foreach>
        on duplicate key update
        file_id = values(file_id)
    </insert>
    
    <!-- 联合查询文件信息和签名信息 -->
    <select id="selectFileInfoWithSign" resultMap="FileInfoSignVOMap">
        SELECT 
            f.*,
            s.sign_name as sign_name
        FROM 
            sys_file_info_sign s
            LEFT JOIN
            sys_file_info f On s.file_id = f.id
        WHERE
            s.sign_name = #{signName}
    </select>
    
    <!-- 批量联合查询文件信息和签名信息 -->
    <select id="selectFileInfoWithSignBatch" resultMap="FileInfoSignVOMap">
        SELECT
        f.*,
        s.sign_name as sign_name
        FROM
        sys_file_info_sign s
        LEFT JOIN
        sys_file_info f on s.file_id = f.id
        WHERE
        s.sign_name IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectFileInfoBySignName" resultMap="SysFileInfoSignMap">
        SELECT sign_name,file_id
        FROM sys_file_info_sign
        WHERE sign_name = #{signName}
    </select>

    <select id="selectFileInfoBySignNameList" resultMap="SysFileInfoSignMap">
        SELECT sign_name,file_id
        FROM sys_file_info_sign
        WHERE sign_name IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>

package com.ruoyi.web.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.web.domain.SysFileInfoMerge;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SysFileInfoMergeMapper extends BaseMapper<SysFileInfoMerge> {
    
    /**
     * 查询合并文件信息总数
     *
     * @param request 查询条件
     * @return 结果总数
     */
    int countMergeData(@Param("request") SysFileInfoMerge request);
    
    /**
     * 查询合并文件信息数据
     *
     * @param request 查询条件
     * @param offset 偏移量
     * @param pageSize 页大小
     * @return 查询结果列表
     */
    List<SysFileInfoMerge> selectMergeData(@Param("request") SysFileInfoMerge request, 
                                           @Param("offset") Integer offset, 
                                           @Param("pageSize") Integer pageSize);
    
    /**
     * 根据拥有者ID查询合并文件信息
     * 注意：由于现在是联合主键，此方法可能返回多条记录
     *
     * @param ownerId 拥有者ID
     * @return 合并文件信息列表
     */
    List<SysFileInfoMerge> selectByOwnerId(@Param("ownerId") String ownerId);
    
    /**
     * 根据签名名称查询合并文件信息
     * 注意：由于现在是联合主键，此方法可能返回多条记录
     *
     * @param signName 签名名称
     * @return 合并文件信息列表
     */
    List<SysFileInfoMerge> selectBySignName(@Param("signName") String signName);
    
    /**
     * 根据拥有者ID和签名名称联合查询合并文件信息
     * owner_id和sign_name作为联合主键，可以唯一确定一条记录
     *
     * @param ownerId 拥有者ID
     * @param signName 签名名称
     * @return 合并文件信息
     */
    SysFileInfoMerge selectByOwnerIdAndSignName(@Param("ownerId") String ownerId, @Param("signName") String signName);
    
    /**
     * 插入单条合并文件信息
     *
     * @param sysFileInfoMerge 合并文件信息
     * @return 影响行数
     */
    int insert(SysFileInfoMerge sysFileInfoMerge);
    
    /**
     * 批量插入合并文件信息
     *
     * @param list 合并文件信息列表
     * @return 影响行数
     */
    int insertBatch(@Param("list") List<SysFileInfoMerge> list);
    
    /**
     * 批量插入或更新合并文件信息
     * 注意：基于联合主键(owner_id, sign_name)进行更新
     *
     * @param list 合并文件信息列表
     * @return 影响行数
     */
    int insertOrUpdateBatch(@Param("list") List<SysFileInfoMerge> list);
    
    /**
     * 根据联合主键更新合并文件信息
     * 注意：需要同时提供owner_id和sign_name两个字段
     *
     * @param sysFileInfoMerge 合并文件信息
     * @return 影响行数
     */
    int updateByPrimaryKey(SysFileInfoMerge sysFileInfoMerge);
    
    /**
     * 根据联合主键删除合并文件信息
     * 注意：需要同时提供owner_id和sign_name两个字段
     *
     * @param ownerId 拥有者ID
     * @param signName 签名名称
     * @return 影响行数
     */
    int deleteByPrimaryKey(@Param("ownerId") String ownerId, @Param("signName") String signName);
    
    /**
     * 根据拥有者ID删除合并文件信息
     * 注意：此操作将删除所有匹配owner_id的记录，不考虑sign_name
     *
     * @param ownerId 拥有者ID
     * @return 影响行数
     */
    int deleteByOwnerId(@Param("ownerId") String ownerId);
    
    /**
     * 批量删除合并文件信息
     * 注意：此操作将删除所有匹配owner_id的记录，不考虑sign_name
     *
     * @param list 拥有者ID列表
     * @return 影响行数
     */
    int deleteBatchByOwnerIds(@Param("list") List<String> list);
}

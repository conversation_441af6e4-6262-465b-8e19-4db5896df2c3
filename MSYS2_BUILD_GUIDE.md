# MSYS2 MinGW 构建指南

本指南介绍如何在MSYS2 MinGW环境中编译带有MinIO支持的C++库。

## 前提条件

### 1. MSYS2环境
确保你已经安装了MSYS2，并且在MinGW64环境中工作：
```bash
# 检查当前环境
echo $MSYSTEM
# 应该显示：MINGW64
```

### 2. vcpkg安装
确保vcpkg已经安装并且MinIO已经安装：
```bash
# 检查vcpkg
ls $VCPKG_ROOT/vcpkg.exe

# 检查MinIO安装
$VCPKG_ROOT/vcpkg.exe list | grep minio
```

如果MinIO未安装：
```bash
$VCPKG_ROOT/vcpkg.exe install minio-cpp:x64-mingw-dynamic
```

### 3. 其他依赖
```bash
$VCPKG_ROOT/vcpkg.exe install curl:x64-mingw-dynamic
$VCPKG_ROOT/vcpkg.exe install openssl:x64-mingw-dynamic
$VCPKG_ROOT/vcpkg.exe install nlohmann-json:x64-mingw-dynamic
$VCPKG_ROOT/vcpkg.exe install opencv4:x64-mingw-dynamic
```

## 构建步骤

### 方法1：使用简化脚本（推荐）

1. **设置环境变量**：
```bash
export VCPKG_ROOT=/path/to/your/vcpkg  # 替换为你的vcpkg路径
```

2. **运行构建脚本**：
```bash
cd native
chmod +x build-simple.sh
./build-simple.sh
```

### 方法2：手动构建

1. **设置环境**：
```bash
export VCPKG_ROOT=/path/to/your/vcpkg
export CMAKE_TOOLCHAIN_FILE="$VCPKG_ROOT/scripts/buildsystems/vcpkg.cmake"
export VCPKG_TARGET_TRIPLET="x64-mingw-dynamic"
```

2. **创建构建目录**：
```bash
mkdir -p build
cd build
```

3. **配置CMake**：
```bash
cmake .. \
    -G "MinGW Makefiles" \
    -DCMAKE_BUILD_TYPE=Release \
    -DCMAKE_TOOLCHAIN_FILE="$CMAKE_TOOLCHAIN_FILE" \
    -DVCPKG_TARGET_TRIPLET="$VCPKG_TARGET_TRIPLET" \
    -DCMAKE_C_COMPILER=gcc \
    -DCMAKE_CXX_COMPILER=g++
```

4. **编译**：
```bash
make -j$(nproc)
```

### 方法3：从Windows命令行启动

如果你在Windows命令行中，可以使用：
```cmd
cd native
build-msys2.bat
```

这会启动MSYS2终端并设置好环境。

## 验证构建

### 1. 检查生成的库文件
```bash
ls -la build/libdataset_export.dll
```

### 2. 运行MinIO测试（如果构建了测试程序）
```bash
cd build
./test_minio.exe
```

### 3. 检查依赖
```bash
ldd build/libdataset_export.dll | grep -i minio
```

## 常见问题

### 1. vcpkg路径问题
**错误**: `vcpkg.exe not found`
**解决**: 确保VCPKG_ROOT环境变量正确设置：
```bash
export VCPKG_ROOT=/c/path/to/vcpkg  # 注意路径格式
```

### 2. MinIO库未找到
**错误**: `MinIO C++ SDK not found`
**解决**: 
```bash
# 检查安装
$VCPKG_ROOT/vcpkg.exe list | grep minio

# 如果未安装
$VCPKG_ROOT/vcpkg.exe install minio-cpp:x64-mingw-dynamic
```

### 3. 编译错误
**错误**: 各种编译错误
**解决**: 
1. 确保在MINGW64环境中（不是MSYS环境）
2. 检查所有依赖是否安装
3. 清理构建目录重新构建

### 4. 链接错误
**错误**: `undefined reference to minio functions`
**解决**: 
1. 检查MinIO库是否正确安装
2. 确保使用正确的triplet (x64-mingw-dynamic)
3. 检查CMake是否找到了MinIO

## 环境变量参考

```bash
# 必需的环境变量
export VCPKG_ROOT=/path/to/vcpkg
export CMAKE_TOOLCHAIN_FILE="$VCPKG_ROOT/scripts/buildsystems/vcpkg.cmake"
export VCPKG_TARGET_TRIPLET="x64-mingw-dynamic"

# 可选的环境变量
export CMAKE_BUILD_TYPE=Release
export CMAKE_C_COMPILER=gcc
export CMAKE_CXX_COMPILER=g++
```

## 构建输出

成功构建后，你应该看到：
- `build/libdataset_export.dll` - 主要的JNI库
- `build/test_minio.exe` - MinIO测试程序（如果启用）
- 库文件自动复制到 `ylzx-annotation/src/main/resources/native/`

## 下一步

1. **测试Java集成**：
   - 启动Java应用程序
   - 调用MinIO处理接口
   - 检查日志输出

2. **配置MinIO连接**：
   - 更新application.yml中的MinIO配置
   - 测试连接性

3. **运行图像处理测试**：
   - 使用提供的REST API测试功能
   - 验证图像处理结果

## 性能优化

1. **并行编译**：
```bash
make -j$(nproc)  # 使用所有CPU核心
```

2. **Release构建**：
```bash
cmake .. -DCMAKE_BUILD_TYPE=Release
```

3. **链接时优化**：
```bash
cmake .. -DCMAKE_CXX_FLAGS="-O3 -DNDEBUG"
```

## 故障排除命令

```bash
# 检查环境
echo "MSYSTEM: $MSYSTEM"
echo "VCPKG_ROOT: $VCPKG_ROOT"
echo "PATH: $PATH"

# 检查编译器
gcc --version
g++ --version

# 检查vcpkg包
$VCPKG_ROOT/vcpkg.exe list | grep -E "(minio|curl|openssl|opencv|nlohmann)"

# 检查库依赖
ldd build/libdataset_export.dll

# 清理重建
rm -rf build
mkdir build && cd build
```

package com.ruoyi.web.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.system.api.domain.vo.SysFileSearchVO;
import com.ruoyi.web.domain.FileInfoEntity;
import com.ruoyi.web.mapper.FileInfoMapper;
import com.ruoyi.web.service.FileInfoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class FileInfoServiceImpl extends ServiceImpl<FileInfoMapper, FileInfoEntity> implements FileInfoService {

    private static final Long GAP = 1000L;

    /**
     * 主方法，处理排序逻辑
     *
     * @param fileList 要排序的文件列表
     * @return 需要更新的FileInfoEntity列表
     */
    @Override
    public List<FileInfoEntity> processFileSort(List<SysFileSearchVO> fileList) {
        Map<String, FileInfoEntity> entitiesMap = new HashMap<>();
        if (fileList == null || fileList.isEmpty()) {
            return new ArrayList<>();
        }
        if (fileList.get(0).getSort() < 2) {
            renumberSortOrder(fileList, entitiesMap, 0, 0);
            return this.updateFileSort(entitiesMap);
        }
        long newSort;
        for (int i = 0; i < fileList.size() - 1; i++) {
            SysFileSearchVO pre = i > 0 ? fileList.get(i - 1) : null;
            SysFileSearchVO current = fileList.get(i);
            SysFileSearchVO next = fileList.get(i + 1);

            if (current.getSort() >= next.getSort()) {
                // 需要调整next的sort
                if (pre == null) {
                    newSort = next.getSort() / 2;
                } else {
                    if(next.getSort()>=pre.getSort()){
                        // 逆序排序
                        i = reverseSortOrder(fileList, entitiesMap, i-1);
                        continue;
                    }
                    if (next.getSort() - pre.getSort() <= 1) {
                        adjustSortOrderRecursively(fileList, entitiesMap, i + 1, 1);
                        adjustSortOrderRecursively(fileList, entitiesMap, i - 1, -1);
                    }
                    newSort = (pre.getSort() + next.getSort()) / 2;
                }
                FileInfoEntity entity = new FileInfoEntity();
                // 如果排序比序号大10倍，太过稀松，则重新排序
                if (newSort / GAP > 10L * i) {
                    renumberSortOrder(fileList, entitiesMap, pre == null ? 0 : pre.getSort(), i);
                    return this.updateFileSort(entitiesMap);
                }
                entity.setId(current.getFileId());
                entity.setSort(newSort);
                entitiesMap.put(current.getFileId(), entity);
            }
        }
        return this.updateFileSort(entitiesMap);
    }

    /**
     * 逆序排序，情况需要找到下一个更大的序号
     * @param fileList 文件列表
     * @param entitiesMap 需要更新的FileInfoEntity列表
     * @param index 当前文件的索引
     * @return 更新到的index
     */
    private int reverseSortOrder(List<SysFileSearchVO> fileList, Map<String, FileInfoEntity> entitiesMap, int index) {
        long newSort;
        Long startSort = fileList.get(index).getSort();
        Long endSort = null;
        int endIdx = 0;
        for (int i = 3; i < fileList.size() - index - 1; i++) {
            SysFileSearchVO current = fileList.get(index+i);
            if(current.getSort()>startSort){
                endSort = current.getSort();
                endIdx = index+i;
                break;
            }
        }
        if(endSort == null){
            renumberSortOrder(fileList, entitiesMap, startSort, index+1);
            return fileList.size() - 1;
        }else{
            if(endSort - startSort < endIdx - index){
                adjustSortOrderRecursively(fileList, entitiesMap, endIdx, 1);
                adjustSortOrderRecursively(fileList, entitiesMap, index, -1);
            }
            long step = (endSort - startSort) / (endIdx - index);
            newSort = startSort;
            for(int i = index +1; i < endIdx; i++){
                newSort += step;
                FileInfoEntity entity = new FileInfoEntity();
                entity.setId(fileList.get(i).getFileId());
                entity.setSort(newSort);
                entitiesMap.put(fileList.get(i).getFileId(), entity);
            }
            return endIdx;
        }
    }

    /**
     * 批量更新文件排序
     * @param entitiesMap 需要更新的FileInfoEntity列表
     * @return  List<FileInfoEntity> 更新后的FileInfoEntity列表
     */
    private List<FileInfoEntity> updateFileSort(Map<String, FileInfoEntity> entitiesMap) {
        List<FileInfoEntity> entities = new ArrayList<>(entitiesMap.values());
        // 如果没有需要更新的数据，直接返回
        if(entities.isEmpty()){
            return entities;
        }
        baseMapper.batchUpdateFileInfo(entities);
        return entities;
    }

    /**
     * 递归调整指定位置的sort_order，确保排序间隔足够
     *
     * @param fileList    文件列表
     * @param entitiesMap 需要更新的FileInfoEntity列表
     * @param index       当前需要调整的文件索引
     */
    private void adjustSortOrderRecursively(List<SysFileSearchVO> fileList, Map<String, FileInfoEntity> entitiesMap, int index, int direct) {
        // 找到当前节点
        SysFileSearchVO node = (index >= 0 && index < fileList.size()) ? fileList.get(index) : null;
        if (node == null) {
            return;
        }
        long nodeSort = node.getSort();
        int nextIndex = index + direct;

        // 找到方向上的下一个节点
        SysFileSearchVO next = (nextIndex >= 0 && nextIndex < fileList.size()) ? fileList.get(nextIndex) : null;
        long newSort;
        long nextSort;

        if (next != null) {
            nextSort = next.getSort();
            if (nodeSort - nextSort <= 1 && nextSort - nodeSort <= 1) {
                // 无法在右侧找到足够的间隔，继续向direct方向递归
                adjustSortOrderRecursively(fileList, entitiesMap, index + direct, direct);
                if (nextSort == next.getSort()) {
                    return;
                }
            }
            newSort = (nodeSort + next.getSort()) / 2;

        } else {
            nextSort = nextIndex == fileList.size() ? (nodeSort + GAP) * GAP / GAP : 0;
            newSort = (nodeSort + nextSort) / 2;
        }
        FileInfoEntity entity = new FileInfoEntity();
        entity.setId(node.getFileId());
        entity.setSort(newSort);
        entitiesMap.put(node.getFileId(), entity);
    }

    /**
     * 重新为所有文件分配sort_order，确保有足够的间隔
     *
     * @param fileList 已排序的文件列表
     *                 entitiesMap      需要更新的FileInfoEntity列表
     *                 sort             当前文件的sort_order
     *                 index            当前文件的索引
     */
    private void renumberSortOrder(List<SysFileSearchVO> fileList,
                                   Map<String, FileInfoEntity> entitiesMap, long sort, int index) {
        sort = (sort + GAP) * GAP / GAP;
        for (int i = index; i < fileList.size(); i++) {
            SysFileSearchVO file = fileList.get(i);
            FileInfoEntity entity = new FileInfoEntity();
            entity.setId(file.getFileId());
            entity.setSort(sort);
            entitiesMap.put(file.getFileId(), entity);
            sort += GAP;
        }
    }


    /**
     * 根据文件id保存文件备注
     * @param fileInfoEntity 文件信息
     * @return FileInfoEntity 更新后的FileInfoEntity
     */
    @Override
    public FileInfoEntity saveFileInfoRemark(SysFileSearchVO fileInfoEntity){
        FileInfoEntity entity = new FileInfoEntity();
        if(fileInfoEntity.getFileId() == null){
            throw new IllegalArgumentException("传入的文件id为空");
        }
        entity.setId(fileInfoEntity.getFileId());
        entity.setRemark(fileInfoEntity.getRemark());
        baseMapper.updateById(entity);
        return entity;
    }

    /**
     * 根据文件id保存文件备注
     * @param fileInfoEntityList 文件信息列表
     * @return List<FileInfoEntity> 更新后的FileInfoEntity列表
     */
    @Override
    public List<FileInfoEntity> saveFileInfoRemark(List<SysFileSearchVO> fileInfoEntityList){
        List<FileInfoEntity> entities = fileInfoEntityList.stream().map(fileInfoEntity -> {
            FileInfoEntity entity = new FileInfoEntity();
            entity.setId(fileInfoEntity.getFileId());
            entity.setRemark(fileInfoEntity.getRemark());
            return entity;
        }).collect(Collectors.toList());
        baseMapper.batchUpdateFileInfo(entities);
        return entities;
    }

    /**
     * 根据文件id删除文件
     * @param fileId 文件id
     * @return boolean 是否删除成功
     */
    @Override
    public boolean deleteFileInfo(String fileId){
        // 假删除 把del_flag改为true
        FileInfoEntity entity = new FileInfoEntity();
        entity.setId(fileId);
        entity.setDelFlag(true);
        return baseMapper.updateById(entity) > 0;
    }

    /**
     * 根据文件id删除文件
     * @param fileIds 文件id列表
     * @return boolean 是否删除成功
     */
    @Override
    public boolean deleteFileInfo(List<String> fileIds){
        // 假删除 把del_flag改为true
        List<FileInfoEntity> entities = fileIds.stream().map(fileId -> {
            FileInfoEntity entity = new FileInfoEntity();
            entity.setId(fileId);
            entity.setDelFlag(true);
            return entity;
        }).collect(Collectors.toList());
        return baseMapper.batchUpdateFileInfo(entities) > 0;
    }


}

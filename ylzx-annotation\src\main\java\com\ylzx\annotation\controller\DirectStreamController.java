package com.ylzx.annotation.controller;

import com.ylzx.annotation.domain.AnnotationImages;
import com.ylzx.annotation.service.AnnotationImagesService;
import com.ylzx.file.service.FileService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.*;


@RestController
@RequestMapping("/preview")
public class DirectStreamController {

    @Resource
    private AnnotationImagesService AnnotationImagesService;

    @Resource
    private FileService fileService;

    @GetMapping("/**")
    public void previewImage(HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 这个方法已废弃，因为现在使用MinIO存储
        // 建议使用 /{imageId} 接口
        response.sendError(HttpServletResponse.SC_NOT_IMPLEMENTED,
            "此接口已废弃，请使用 /{imageId} 接口访问图片");
    }


    @GetMapping("/{imageId}")
    public void previewImageByImageId(@PathVariable("imageId") Long imageId, HttpServletResponse response) throws IOException {
        // 1. 查询图片信息
        AnnotationImages image = AnnotationImagesService.selectAnnotationImagesByImageId(imageId);
        if (image == null) {
            response.sendError(HttpServletResponse.SC_NOT_FOUND, "图片不存在");
            return;
        }

        // 2. 获取文件ID
        String fileId = image.getFileId();
        if (fileId == null || fileId.isEmpty()) {
            response.sendError(HttpServletResponse.SC_NOT_FOUND, "文件ID不存在");
            return;
        }

        // 3. 从MinIO流式传输图片
        InputStream inputStream = null;
        OutputStream outputStream = null;
        try {
            // 从MinIO下载文件流
            inputStream = fileService.downloadFile(fileId);
            if (inputStream == null) {
                response.sendError(HttpServletResponse.SC_NOT_FOUND, "文件在MinIO中不存在");
                return;
            }

            // 设置响应头
            String filename = image.getOriginalFilename();
            if (filename != null) {
                // 根据文件扩展名设置Content-Type
                String contentType = getContentTypeByFilename(filename);
                response.setContentType(contentType);

                // 设置为内联显示
                response.setHeader("Content-Disposition",
                    "inline; filename=\"" + filename + "\"");
            } else {
                response.setContentType("image/jpeg"); // 默认类型
            }

            // 设置缓存控制
            response.setHeader("Cache-Control", "max-age=3600"); // 缓存1小时

            // 流式传输
            outputStream = response.getOutputStream();
            byte[] buffer = new byte[8192]; // 8KB缓冲区
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();

        } catch (Exception e) {
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "读取文件失败: " + e.getMessage());
        } finally {
            // 关闭流
            if (inputStream != null) {
                try { inputStream.close(); } catch (IOException e) { /* 忽略 */ }
            }
            if (outputStream != null) {
                try { outputStream.close(); } catch (IOException e) { /* 忽略 */ }
            }
        }
    }

    /**
     * 根据文件名获取Content-Type
     */
    private String getContentTypeByFilename(String filename) {
        if (filename == null) return "application/octet-stream";

        String lowerName = filename.toLowerCase();
        if (lowerName.endsWith(".jpg") || lowerName.endsWith(".jpeg")) {
            return "image/jpeg";
        } else if (lowerName.endsWith(".png")) {
            return "image/png";
        } else if (lowerName.endsWith(".gif")) {
            return "image/gif";
        } else if (lowerName.endsWith(".webp")) {
            return "image/webp";
        } else if (lowerName.endsWith(".bmp")) {
            return "image/bmp";
        } else {
            return "image/jpeg"; // 默认
        }
    }


}

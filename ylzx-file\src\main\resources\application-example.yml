# 示例配置文件
# 复制此文件为 application.yml 并修改相应配置

# 服务器配置
server:
  port: 8083
  servlet:
    context-path: /file

# Spring配置
spring:
  application:
    name: ylzx-file
  profiles:
    active: dev
  servlet:
    multipart:
      max-file-size: 100MB      # 单个文件最大大小
      max-request-size: 100MB   # 请求最大大小
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ************************************************
    username: postgres
    password: 123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 20
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*.xml
  type-aliases-package: com.ylzx.file.domain
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: status
      logic-delete-value: 0
      logic-not-delete-value: 1

# MinIO配置
minio:
  endpoint: http://localhost:9000        # MinIO服务地址
  access-key: minioadmin                 # 访问密钥
  secret-key: minioadmin                 # 秘密密钥
  bucket-name: data-annotation           # 存储桶名称
  secure: false                          # 是否使用HTTPS
  connect-timeout: 10000                 # 连接超时时间（毫秒）
  write-timeout: 60000                   # 写入超时时间（毫秒）
  read-timeout: 10000                    # 读取超时时间（毫秒）
  presigned-url-expiry: 3600             # 预签名URL过期时间（秒）

# 日志配置
logging:
  level:
    com.ylzx.file: debug
    org.springframework.web: info
    org.mybatis: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: logs/ylzx-file.log

# SpringDoc配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  info:
    title: YLZX文件服务API
    description: 基于MinIO的文件存储服务
    version: 1.0.0
    contact:
      name: YLZX Team
      email: <EMAIL>

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

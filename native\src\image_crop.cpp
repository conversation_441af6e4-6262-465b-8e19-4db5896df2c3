#include "image_crop.h"
#include "minio_client.h"
#include <opencv2/opencv.hpp>
#include <opencv2/imgproc.hpp>
#include <nlohmann/json.hpp>
#include <algorithm>
#include <vector>
#include <iostream>
#include <stdexcept>

namespace ImageCrop {

ImageCropper::ImageCropper() {}

ImageCropper::~ImageCropper() {}

CropResult ImageCropper::cropImage(const std::string& imagePath, const std::string& outputPath,
                                 const CropRegion& region, const CropConfig& config) {
    CropResult result;
    result.success = false;
    result.message = "";

    try {
        cv::Mat image = cv::imread(imagePath);
        if (image.empty()) {
            result.message = "Failed to load image: " + imagePath;
            return result;
        }

        // 确保裁剪区域在图像范围内
        cv::Rect cropRect(
            std::max(0, static_cast<int>(region.x)),
            std::max(0, static_cast<int>(region.y)),
            std::min(static_cast<int>(region.width), image.cols - static_cast<int>(region.x)),
            std::min(static_cast<int>(region.height), image.rows - static_cast<int>(region.y))
        );

        if (cropRect.width <= 0 || cropRect.height <= 0) {
            result.message = "Invalid crop region";
            return result;
        }

        cv::Mat croppedImage = image(cropRect);

        // 调整大小（如果需要）
        if (config.targetWidth > 0 && config.targetHeight > 0) {
            cv::resize(croppedImage, croppedImage, cv::Size(config.targetWidth, config.targetHeight));
        }

        // 保存图像
        if (!cv::imwrite(outputPath, croppedImage)) {
            result.message = "Failed to save image: " + outputPath;
            return result;
        }

        result.success = true;
        result.message = "Crop successful";
        return result;

    } catch (const std::exception& e) {
        result.message = "Exception: " + std::string(e.what());
        return result;
    }
}

BatchCropResult ImageCropper::batchCropImages(const BatchCropConfig& batchConfig) {
    BatchCropResult result;
    result.totalCount = batchConfig.sourcePaths.size();
    result.successCount = 0;
    result.failedCount = 0;
    result.message = "";

    if (batchConfig.sourcePaths.size() != batchConfig.targetPaths.size() ||
        batchConfig.sourcePaths.size() != batchConfig.cropRegions.size()) {
        result.message = "Input arrays size mismatch";
        return result;
    }

    for (size_t i = 0; i < batchConfig.sourcePaths.size(); ++i) {
        CropResult cropResult = cropImage(batchConfig.sourcePaths[i],
                                        batchConfig.targetPaths[i],
                                        batchConfig.cropRegions[i],
                                        batchConfig.cropConfig);

        if (cropResult.success) {
            result.successCount++;
        } else {
            result.failedCount++;
        }
    }

    result.message = "Batch crop completed";
    return result;
}

CropResult ImageCropper::smartCropByAnnotations(const std::string& imagePath,
                                              const std::string& outputPath,
                                              const std::string& annotationCoordinates,
                                              const CropConfig& config) {
    CropResult result;
    result.success = false;
    result.message = "";

    try {
        // 解析JSON坐标
        nlohmann::json j = nlohmann::json::parse(annotationCoordinates);

        if (!j.is_array() || j.empty()) {
            result.message = "Invalid annotation coordinates";
            return result;
        }

        // 计算边界框
        float minX = std::numeric_limits<float>::max();
        float maxX = std::numeric_limits<float>::min();
        float minY = std::numeric_limits<float>::max();
        float maxY = std::numeric_limits<float>::min();

        for (const auto& point : j) {
            if (point.is_array() && point.size() >= 2) {
                float x = point[0].get<float>();
                float y = point[1].get<float>();
                minX = std::min(minX, x);
                maxX = std::max(maxX, x);
                minY = std::min(minY, y);
                maxY = std::max(maxY, y);
            }
        }

        // 添加边距
        float padding = config.padding;
        minX = std::max(0.0f, minX - padding);
        minY = std::max(0.0f, minY - padding);
        maxX += padding;
        maxY += padding;

        // 创建裁剪区域
        CropRegion region;
        region.x = minX;
        region.y = minY;
        region.width = maxX - minX;
        region.height = maxY - minY;

        // 执行裁剪
        return cropImage(imagePath, outputPath, region, config);

    } catch (const std::exception& e) {
        result.message = "Exception in smart crop: " + std::string(e.what());
        return result;
    }
}

BatchCropResult ImageCropper::batchSmartCropByAnnotations(const std::vector<std::string>& imagePaths,
                                                        const std::vector<std::string>& outputPaths,
                                                        const std::vector<std::string>& annotationCoordinatesArray,
                                                        const CropConfig& config,
                                                        int threadCount) {
    BatchCropResult result;
    result.totalCount = imagePaths.size();
    result.successCount = 0;
    result.failedCount = 0;

    if (imagePaths.size() != outputPaths.size() ||
        imagePaths.size() != annotationCoordinatesArray.size()) {
        result.message = "Input arrays size mismatch";
        return result;
    }

    // 简化处理：不使用并行，直接顺序处理
    for (size_t i = 0; i < imagePaths.size(); ++i) {
        CropResult cropResult = smartCropByAnnotations(imagePaths[i], outputPaths[i], annotationCoordinatesArray[i], config);
        if (cropResult.success) {
            result.successCount++;
        } else {
            result.failedCount++;
        }
    }

    result.message = "Batch smart crop completed";
    return result;
}

// ==================== MinIO直接处理方法实现 ====================

CropResult ImageCropper::smartCropMinioToMinio(const std::string& sourceBucket,
                                              const std::string& sourceObject,
                                              const std::string& targetBucket,
                                              const std::string& targetObject,
                                              const std::string& annotationCoordinates,
                                              const CropConfig& config) {
    CropResult result;
    result.success = false;
    result.message = "";

    try {
        // 获取MinIO客户端
        MinioIntegration::MinioClient* minioClient = MinioIntegration::MinioManager::getInstance();
        if (!minioClient || !minioClient->isInitialized()) {
            result.message = "MinIO client not initialized";
            return result;
        }

        // 从MinIO下载图像数据
        std::vector<uint8_t> imageData;
        MinioIntegration::MinioResult downloadResult = minioClient->downloadToMemory(sourceBucket, sourceObject, imageData);
        if (!downloadResult.success) {
            result.message = "Failed to download image from MinIO: " + downloadResult.message;
            return result;
        }

        // 处理图像数据
        std::vector<uint8_t> processedData;
        CropResult cropResult = smartCropFromMemory(imageData, annotationCoordinates, config, processedData);
        if (!cropResult.success) {
            result.message = "Failed to crop image: " + cropResult.message;
            return result;
        }

        // 上传处理后的图像到MinIO
        MinioIntegration::MinioResult uploadResult = minioClient->uploadFromMemory(
            targetBucket, targetObject, processedData, "image/jpeg");
        if (!uploadResult.success) {
            result.message = "Failed to upload processed image to MinIO: " + uploadResult.message;
            return result;
        }

        result.success = true;
        result.message = "MinIO to MinIO crop successful";
        return result;

    } catch (const std::exception& e) {
        result.message = "Exception in MinIO to MinIO crop: " + std::string(e.what());
        return result;
    }
}

CropResult ImageCropper::smartCropFromMemory(const std::vector<uint8_t>& imageData,
                                           const std::string& annotationCoordinates,
                                           const CropConfig& config,
                                           std::vector<uint8_t>& outputData) {
    CropResult result;
    result.success = false;
    result.message = "";

    try {
        // 从内存数据解码图像
        cv::Mat image = cv::imdecode(imageData, cv::IMREAD_COLOR);
        if (image.empty()) {
            result.message = "Failed to decode image from memory";
            return result;
        }

        // 解析JSON坐标
        nlohmann::json j = nlohmann::json::parse(annotationCoordinates);
        if (!j.is_array() || j.empty()) {
            result.message = "Invalid annotation coordinates";
            return result;
        }

        // 计算边界框
        float minX = std::numeric_limits<float>::max();
        float maxX = std::numeric_limits<float>::min();
        float minY = std::numeric_limits<float>::max();
        float maxY = std::numeric_limits<float>::min();

        for (const auto& point : j) {
            if (point.is_array() && point.size() >= 2) {
                float x = point[0].get<float>();
                float y = point[1].get<float>();
                minX = std::min(minX, x);
                maxX = std::max(maxX, x);
                minY = std::min(minY, y);
                maxY = std::max(maxY, y);
            }
        }

        // 添加边距
        float padding = config.padding;
        minX = std::max(0.0f, minX - padding);
        minY = std::max(0.0f, minY - padding);
        maxX = std::min(static_cast<float>(image.cols), maxX + padding);
        maxY = std::min(static_cast<float>(image.rows), maxY + padding);

        // 确保裁剪区域在图像范围内
        cv::Rect cropRect(
            static_cast<int>(minX),
            static_cast<int>(minY),
            static_cast<int>(maxX - minX),
            static_cast<int>(maxY - minY)
        );

        if (cropRect.width <= 0 || cropRect.height <= 0 ||
            cropRect.x + cropRect.width > image.cols ||
            cropRect.y + cropRect.height > image.rows) {
            result.message = "Invalid crop region";
            return result;
        }

        cv::Mat croppedImage = image(cropRect);

        // 调整大小（如果需要）
        if (config.targetWidth > 0 && config.targetHeight > 0) {
            cv::resize(croppedImage, croppedImage, cv::Size(config.targetWidth, config.targetHeight));
        }

        // 编码图像到内存
        std::vector<uchar> buffer;
        if (!cv::imencode(".jpg", croppedImage, buffer)) {
            result.message = "Failed to encode processed image";
            return result;
        }

        outputData.assign(buffer.begin(), buffer.end());
        result.success = true;
        result.message = "Memory crop successful";
        return result;

    } catch (const std::exception& e) {
        result.message = "Exception in memory crop: " + std::string(e.what());
        return result;
    }
}

BatchCropResult ImageCropper::batchSmartCropMinioToMinio(const std::vector<std::string>& sourceBuckets,
                                                       const std::vector<std::string>& sourceObjects,
                                                       const std::vector<std::string>& targetBuckets,
                                                       const std::vector<std::string>& targetObjects,
                                                       const std::vector<std::string>& annotationCoordinatesArray,
                                                       const CropConfig& config,
                                                       int threadCount) {
    BatchCropResult result;
    result.totalCount = sourceBuckets.size();
    result.successCount = 0;
    result.failedCount = 0;

    if (sourceBuckets.size() != sourceObjects.size() ||
        sourceBuckets.size() != targetBuckets.size() ||
        sourceBuckets.size() != targetObjects.size() ||
        sourceBuckets.size() != annotationCoordinatesArray.size()) {
        result.message = "Input arrays size mismatch";
        return result;
    }

    // 简化处理：不使用并行，直接顺序处理
    // TODO: 可以在未来添加线程池支持
    (void)threadCount; // 避免未使用参数警告

    for (size_t i = 0; i < sourceBuckets.size(); ++i) {
        CropResult cropResult = smartCropMinioToMinio(
            sourceBuckets[i], sourceObjects[i],
            targetBuckets[i], targetObjects[i],
            annotationCoordinatesArray[i], config);

        if (cropResult.success) {
            result.successCount++;
        } else {
            result.failedCount++;
        }
    }

    result.message = "Batch MinIO to MinIO crop completed";
    return result;
}

} // namespace ImageCrop
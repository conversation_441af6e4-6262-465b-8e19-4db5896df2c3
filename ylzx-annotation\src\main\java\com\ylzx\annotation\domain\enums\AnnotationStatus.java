package com.ylzx.annotation.domain.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IEnum;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.Getter;


/**
 * 标注状态枚举
 * 0:未标注 1:标注中 2:待审核 3:审核通过 4:审核不通过
 */
@Getter
public enum AnnotationStatus  implements IEnum<String> {
    NOT_ANNOTATED("0", "未标注"),
    IN_PROGRESS("1", "标注中"),
    PENDING_REVIEW("2", "待审核"),
    APPROVED("3", "审核通过"),
    REJECTED("4", "审核不通过"),
    ANNOTATED("5", "已标注");


    @EnumValue
    private final String code;

    @JsonValue
    private final String description;

    @Override
    public String getValue() {
        return this.code;
    }

    AnnotationStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }


    public static AnnotationStatus fromCode(String code) {
        for (AnnotationStatus status : AnnotationStatus.values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid AnnotationStatus code: " + code);
    }
} 
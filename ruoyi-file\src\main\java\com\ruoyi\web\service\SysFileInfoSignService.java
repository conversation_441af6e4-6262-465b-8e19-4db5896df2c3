package com.ruoyi.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.SysFile;
import com.ruoyi.system.api.domain.vo.SysFileSign;
import com.ruoyi.web.domain.SysFileInfoSign;
import com.ruoyi.web.domain.vo.FileInfoSignVO;

import java.util.List;

public interface SysFileInfoSignService extends IService<SysFileInfoSign> {
    List<SysFileInfoSign> selectAssetBaseData(SysFileInfoSign sysFileInfoSign);

    int countAssetBaseData(SysFileInfoSign sysFileInfoSign);

    int insertBatch(List<SysFileInfoSign> sysFileInfoSignList);
    int insertOrUpdateBatch(List<SysFileInfoSign> sysFileInfoSignList);

    SysFileSign insert(SysFileInfoSign sysFileInfoSign);
    SysFileSign insert(String name);
    SysFileSign selectFileInfoWithSign(String signName);
    List<SysFileSign> selectFileInfoWithSignBatch(List<String> list);
}

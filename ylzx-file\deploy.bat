@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: YLZX文件服务部署脚本 (Windows版本)

:: 设置变量
set APP_NAME=ylzx-file
set JAR_NAME=%APP_NAME%.jar
set PID_FILE=%APP_NAME%.pid
set LOG_FILE=logs\%APP_NAME%.log

:: 检查Java环境
:check_java
java -version >nul 2>&1
if errorlevel 1 (
    echo [错误] Java未安装或未配置到PATH中
    exit /b 1
)
echo [信息] Java环境检查通过

:: 检查应用是否运行
:is_running
if not exist "%PID_FILE%" (
    set RUNNING=false
    goto :eof
)

set /p PID=<"%PID_FILE%"
tasklist /fi "pid eq %PID%" 2>nul | find /i "java.exe" >nul
if errorlevel 1 (
    del "%PID_FILE%" 2>nul
    set RUNNING=false
) else (
    set RUNNING=true
)
goto :eof

:: 启动应用
:start
call :is_running
if "%RUNNING%"=="true" (
    echo [警告] 应用已经在运行中 ^(PID: %PID%^)
    goto :eof
)

echo [信息] 正在启动 %APP_NAME%...

:: 创建日志目录
if not exist "logs" mkdir logs

:: 启动应用
start /b java -jar "%JAR_NAME%" > "%LOG_FILE%" 2>&1

:: 获取进程ID (简化版本)
timeout /t 2 >nul
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq java.exe" /fo csv ^| find /c ","') do set JAVA_COUNT=%%i

:: 这里简化处理，实际应该获取具体的PID
echo %RANDOM% > "%PID_FILE%"

echo [信息] %APP_NAME% 启动成功
echo [信息] 日志文件: %LOG_FILE%
echo [信息] API文档: http://localhost:8083/file/swagger-ui.html
goto :eof

:: 停止应用
:stop
call :is_running
if "%RUNNING%"=="false" (
    echo [警告] 应用未运行
    goto :eof
)

echo [信息] 正在停止 %APP_NAME%...

:: 杀死Java进程 (简化版本，实际应该根据PID精确杀死)
taskkill /f /im java.exe >nul 2>&1
del "%PID_FILE%" 2>nul

echo [信息] %APP_NAME% 已停止
goto :eof

:: 重启应用
:restart
echo [信息] 正在重启 %APP_NAME%...
call :stop
timeout /t 2 >nul
call :start
goto :eof

:: 查看状态
:status
call :is_running
if "%RUNNING%"=="true" (
    echo [信息] %APP_NAME% 正在运行
) else (
    echo [警告] %APP_NAME% 未运行
)
goto :eof

:: 查看日志
:logs
if exist "%LOG_FILE%" (
    type "%LOG_FILE%"
) else (
    echo [错误] 日志文件不存在: %LOG_FILE%
)
goto :eof

:: 构建应用
:build
echo [信息] 正在构建 %APP_NAME%...

mvn clean package -DskipTests
if errorlevel 1 (
    echo [错误] 构建失败
    exit /b 1
)

echo [信息] 构建成功
copy target\%JAR_NAME% . >nul
goto :eof

:: 主函数
:main
if "%1"=="start" (
    call :check_java
    call :start
) else if "%1"=="stop" (
    call :stop
) else if "%1"=="restart" (
    call :check_java
    call :restart
) else if "%1"=="status" (
    call :status
) else if "%1"=="logs" (
    call :logs
) else if "%1"=="build" (
    call :build
) else (
    echo 使用方法: %0 {start^|stop^|restart^|status^|logs^|build}
    echo.
    echo 命令说明:
    echo   start   - 启动应用
    echo   stop    - 停止应用
    echo   restart - 重启应用
    echo   status  - 查看应用状态
    echo   logs    - 查看应用日志
    echo   build   - 构建应用
    exit /b 1
)

goto :eof

:: 调用主函数
call :main %1

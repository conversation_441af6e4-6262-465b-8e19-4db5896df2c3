package com.ylzx.annotation.domain;

import java.io.Serial;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.ylzx.annotation.domain.enums.AnnotationStatus;
import com.ylzx.common.annotation.Excel;
import com.ylzx.common.core.domain.BaseEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 标注项目对象 annotation_projects
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class AnnotationProjects extends BaseEntity
{
    @Serial
    private static final long serialVersionUID = 1L;

    /** 标注项目主键 */
    private Long projectId;

    /** 标注项目名称 */
    @Excel(name = "标注项目名称")
    private String name;

    /** 标注项目描述 */
    @Excel(name = "标注项目描述")
    private String description;

    /** 标注状态 */
    @Excel(name = "标注状态", readConverterExp = "0=未标注,1=未审核,2=审核通过,3=审核不通过")
    private AnnotationStatus status;
}

package com.ylzx.annotation.domain.dto;

import com.ylzx.annotation.domain.AnnotationImages;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 标注图片DTO，包含预签名URL
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AnnotationImageDto extends AnnotationImages {

    /**
     * 图片预签名URL（用于前端显示）
     */
    private String presignedUrl;

    /**
     * 缩略图预签名URL（如果有）
     */
    private String thumbnailPresignedUrl;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件类型
     */
    private String contentType;

    /**
     * 从AnnotationImages转换为AnnotationImageDto
     * 
     * @param image 原始图片对象
     * @return DTO对象
     */
    public static AnnotationImageDto fromAnnotationImages(AnnotationImages image) {
        AnnotationImageDto dto = new AnnotationImageDto();
        
        // 复制基础属性
        dto.setImageId(image.getImageId());
        dto.setSourceId(image.getSourceId());
        dto.setFileId(image.getFileId());
        dto.setOriginalFilename(image.getOriginalFilename());
        dto.setWidth(image.getWidth());
        dto.setHeight(image.getHeight());
        dto.setMd5Hash(image.getMd5Hash());
        dto.setCreateTime(image.getCreateTime());
        dto.setUpdateTime(image.getUpdateTime());
        dto.setCreateBy(image.getCreateBy());
        dto.setUpdateBy(image.getUpdateBy());
        dto.setRemark(image.getRemark());
        
        return dto;
    }
}

package com.ylzx.annotation.mapper;

import java.util.Date;
import java.util.List;
import org.apache.ibatis.annotations.Param;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylzx.annotation.domain.AnnotationCategoriesImages;
import com.ylzx.annotation.domain.enums.AnnotationStatus;
import org.apache.ibatis.annotations.Mapper;

/**
 * 标注项目图片Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Mapper
public interface AnnotationCategoriesImagesMapper extends BaseMapper<AnnotationCategoriesImages>
{
    /**
     * 查询标注项目图片
     * 
     * @param CategoriesId 标注项目图片主键
     * @return 标注项目图片
     */
    AnnotationCategoriesImages selectAnnotationCategoriesImagesByCategoryId(Long CategoriesId);

    /**
     * 查询标注项目图片列表
     * 
     * @param annotationCategoriesImages 标注项目图片
     * @return 标注项目图片集合
     */
    List<AnnotationCategoriesImages> selectAnnotationCategoriesImagesList(AnnotationCategoriesImages annotationCategoriesImages);

    /**
     * 新增标注项目图片
     * 
     * @param annotationCategoriesImages 标注项目图片
     * @return 结果
     */
    int insertAnnotationCategoriesImages(AnnotationCategoriesImages annotationCategoriesImages);

    /**
     * 修改标注项目图片
     * 
     * @param annotationCategoriesImages 标注项目图片
     * @return 结果
     */
    int updateAnnotationCategoriesImages(AnnotationCategoriesImages annotationCategoriesImages);

    /**
     * 删除标注项目图片
     * 
     * @param CategoriesId 标注
     * @return 结果
     */
    int deleteAnnotationCategoriesImagesByCategoryId(Long CategoriesId);


    /**
     *
     * @param id
     * @return
     */
    int deleteAnnotationCategoriesImagesById(Long id);

    /**
     * 查询并锁定可用于标注的图片ID列表
     * @param categoryId 类目ID
     * @param status 状态 (应为 NOT_ANNOTATED)
     * @param limit 数量
     * @return 图片ID列表
     */
    public List<Long> findAvailableImageIds(@Param("categoryId") Long categoryId, @Param("status") AnnotationStatus status, @Param("limit") int limit);

    /**
     * 将指定的图片列表分配给用户
     * @param ids 图片ID列表
     * @param userId 用户ID
     * @param claimTime 领取时间
     * @param targetStatus 目标状态 (应为 IN_PROGRESS)
     * @return 更新的行数
     */
    public int assignImagesToUser(@Param("list") List<Long> ids, @Param("userId") Long userId, @Param("claimTime") Date claimTime, @Param("targetStatus") AnnotationStatus targetStatus);

    /**
     * 根据ID列表查询图片详情
     * @param ids 图片ID列表
     * @return 图片详情列表
     */
    public List<AnnotationCategoriesImages> findByIds(@Param("list") List<Long> ids);

    /**
     * 释放超时的图片
     * @param timeoutThreshold 超时时间点
     * @param sourceStatus 源状态 (应为 IN_PROGRESS)
     * @param targetStatus 目标状态 (应为 NOT_ANNOTATED)
     * @return 更新的行数
     */
    public int releaseTimedOutImages(@Param("timeoutThreshold") Date timeoutThreshold, @Param("sourceStatus") AnnotationStatus sourceStatus, @Param("targetStatus") AnnotationStatus targetStatus);

    /**
     * 批量删除标注项目图片
     * 
     * @param categoryIds 需要删除的标注项目图片主键集合
     * @return 结果
     */
    public int deleteAnnotationCategoriesImagesByCategoryIds(Long[] categoryIds);

    /**
     * 批量插入标注项目图片
     * 
     * @param annotationCategoriesImages 标注项目图片
     * @return 结果
     */
    int insertBatch(List<AnnotationCategoriesImages> annotationCategoriesImages);

    /**
     * 批量更新标注项目图片
     * 
     * @param annotationCategoriesImages 标注项目图片
     * @return 结果
     */
    int updateBatch(List<AnnotationCategoriesImages> annotationCategoriesImages);

    /**
     * 根据用户ID和状态查询任务列表
     * @param categoryId 类目ID
     * @param userId 用户ID
     * @param status 状态
     * @return 任务列表
     */
    List<AnnotationCategoriesImages> findUserTasksByStatus(@Param("categoryId") Long categoryId, @Param("userId") Long userId, @Param("status") AnnotationStatus status);

    /**
     * 根据ID列表释放任务
     * @param taskIds 要释放的任务ID列表
     * @param targetStatus 目标状态
     * @return 更新的行数
     */
    int releaseTasksByIds(@Param("list") List<Long> taskIds, @Param("targetStatus") AnnotationStatus targetStatus);

    /**
     * 根据用户ID和多个状态查询任务列表
     * @param categoryId 类目ID，可为null表示查询所有分类
     * @param userId 用户ID
     * @param statuses 状态列表
     * @return 任务列表
     */
    List<AnnotationCategoriesImages> findUserTasksByStatuses(@Param("categoryId") Long categoryId, @Param("userId") Long userId, @Param("statuses") List<AnnotationStatus> statuses);

    /**
     * 根据用户ID和状态查询所有分类下的任务列表
     * @param userId 用户ID
     * @param statuses 状态列表
     * @return 任务列表
     */
    List<AnnotationCategoriesImages> findAllUserTasksByStatuses(@Param("userId") Long userId, @Param("statuses") List<AnnotationStatus> statuses);
}

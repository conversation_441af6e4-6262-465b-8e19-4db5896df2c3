package com.ylzx.annotation.service.impl;

import java.io.*;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylzx.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.ylzx.annotation.mapper.AnnotationImageSourcesMapper;
import com.ylzx.annotation.domain.AnnotationImageSources;
import com.ylzx.annotation.domain.AnnotationImages;
import com.ylzx.annotation.service.AnnotationImageSourcesService;
import com.ylzx.annotation.service.AnnotationImagesService;
import com.ylzx.annotation.service.AnnotationCategoriesSourcesService;

import com.ylzx.file.service.FileService;
import com.ylzx.file.service.BatchFileService;
import com.ylzx.file.domain.dto.FileUploadRequest;
import com.ylzx.file.domain.dto.FileUploadResponse;
import com.ylzx.file.domain.FileInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import jakarta.annotation.Resource;

/**
 * 标注图片来源Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Slf4j
@Service
public class AnnotationImageSourcesServiceImpl extends ServiceImpl<AnnotationImageSourcesMapper,AnnotationImageSources> implements AnnotationImageSourcesService
{
    // 支持的压缩文件格式
    private static final Set<String> SUPPORTED_ARCHIVE_EXTENSIONS = Set.of(".zip", ".tar.gz");

    @Autowired
    private AnnotationImageSourcesMapper annotationImageSourcesMapper;

    @Autowired
    private AnnotationImagesService annotationImagesService;

    @Autowired
    private AnnotationCategoriesSourcesService categoriesSourcesService;

    @Resource
    private FileService fileService;

    @Resource
    private BatchFileService batchFileService;

    /**
     * 查询标注图片来源
     *
     * @param sourceId 标注图片来源主键
     * @return 标注图片来源
     */
    @Override
    public AnnotationImageSources selectAnnotationImageSourcesBySourceId(Long sourceId)
    {
        return this.getById(sourceId);
    }

    /**
     * 查询标注图片来源列表
     *
     * @param annotationImageSources 标注图片来源
     * @return 标注图片来源集合
     */
    @Override
    public List<AnnotationImageSources> selectAnnotationImageSourcesList(AnnotationImageSources annotationImageSources)
    {
        return annotationImageSourcesMapper.selectAnnotationImageSourcesList(annotationImageSources);
    }

    /**
     * 批量删除标注图片来源
     *
     * @param sourceIds 需要删除的标注图片来源主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationImageSourcesBySourceIds(Long[] sourceIds)
    {
        return annotationImageSourcesMapper.deleteBatchIds(Arrays.asList(sourceIds));
    }

    /**
     * 删除标注图片来源信息
     *
     * @param sourceId 标注图片来源主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationImageSourcesBySourceId(Long sourceId)
    {
        return annotationImageSourcesMapper.deleteAnnotationImageSourcesBySourceId(sourceId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AnnotationImageSources uploadSource(MultipartFile file) throws IOException {
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !isSupportedArchiveFile(originalFilename)) {
            throw new IllegalArgumentException("不支持的文件格式，仅支持: " + SUPPORTED_ARCHIVE_EXTENSIONS);
        }

        try {

            // 1. 计算文件哈希并检查重复
            String hash = calculatePartialHash(file);
            checkDuplicateHash(hash);

            // 2. 使用BatchFileService上传并解压文件到MinIO
            List<FileUploadResponse> uploadResults = batchFileService.extractAndUploadFromArchive(
                    file, "annotation/sources", SecurityUtils.getUsername());

            if (uploadResults.isEmpty()) {
                throw new IOException("文件上传和解压失败，没有返回结果");
            }

            // 3. 第一个文件是压缩包本身，其余是解压后的文件
            FileUploadResponse archiveFile = uploadResults.get(0);

            // 4. 保存数据源记录
            AnnotationImageSources source = saveSourceRecordWithExtractedFiles(
                    archiveFile.getId(), originalFilename, "manual_upload", hash, uploadResults);

            // 5. 自动处理解压后的图片文件（不指定分类）
            try {
                processExtractedImagesFromMinIO(source.getSourceId(), null, uploadResults);
                log.info("已自动处理解压后的图片文件，sourceId: {}", source.getSourceId());
            } catch (Exception e) {
                log.error("处理解压后的图片文件失败，sourceId: {}", source.getSourceId(), e);
                // 不抛出异常，允许数据源创建成功，后续可以手动处理图片
            }






            return source;

        } catch (Exception e) {
            log.error("上传数据源失败", e);
            throw new IOException("上传数据源失败: " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AnnotationImageSources uploadSourceWithCategory(MultipartFile file, Long categoryId) throws IOException {
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || !isSupportedArchiveFile(originalFilename)) {
            throw new IllegalArgumentException("不支持的文件格式，仅支持: " + SUPPORTED_ARCHIVE_EXTENSIONS);
        }

        try {
            // 1. 计算文件哈希并检查重复
            String hash = calculatePartialHash(file);
            checkDuplicateHash(hash);

            // 2. 使用BatchFileService上传并解压文件到MinIO
            List<FileUploadResponse> uploadResults = batchFileService.extractAndUploadFromArchive(
                    file, "annotation/sources", SecurityUtils.getUsername());

            if (uploadResults.isEmpty()) {
                throw new IOException("文件上传和解压失败，没有返回结果");
            }

            // 3. 第一个文件是压缩包本身，其余是解压后的文件
            FileUploadResponse archiveFile = uploadResults.get(0);

            // 4. 保存数据源记录
            AnnotationImageSources source = saveSourceRecordWithExtractedFiles(
                    archiveFile.getId(), originalFilename, "manual_upload", hash, uploadResults);

            List<AnnotationImageSources> annotationImageSources = annotationImageSourcesMapper.selectAnnotationImageSourcesList(source);
            if (annotationImageSources.size() == 1){
               source.setSourceId(annotationImageSources.get(0).getSourceId());
            }

            // 5. 如果指定了分类ID，创建分类与数据源的关联
            if (categoryId != null) {
                categoriesSourcesService.associateCategoryWithSource(categoryId, source.getSourceId(), SecurityUtils.getUserId());
                log.info("已创建分类{}与数据源{}的关联", categoryId, source.getSourceId());

                // 6. 自动处理解压后的图片文件并关联到指定分类
                try {
                    processExtractedImagesFromMinIO(source.getSourceId(), categoryId, uploadResults);
                    log.info("已自动处理数据源{}的图片并关联到分类{}", source.getSourceId(), categoryId);
                } catch (Exception e) {
                    log.error("自动处理图片失败，数据源ID: {}, 分类ID: {} - 异常: {}", source.getSourceId(), categoryId, e.getMessage(), e);
                    // 不抛出异常，允许手动后续处理
                }
            }

            return source;
        } catch (Exception e) {
            log.error("上传数据源失败", e);
            throw new IOException("上传数据源失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void scanAndImportSources() throws IOException {
        int processedCount = 0;
        int skippedCount = 0;
        int errorCount = 0;

        try {
            // 扫描MinIO存储桶中的压缩文件
            List<com.ylzx.file.domain.FileInfo> archiveFiles = fileService.listArchiveFilesInMinIO("annotation/scan");

            if (archiveFiles.isEmpty()) {
                log.info("MinIO扫描目录 'annotation/scan' 中没有找到压缩文件");
                return;
            }

            log.info("在MinIO中找到 {} 个压缩文件，开始处理", archiveFiles.size());

            for (com.ylzx.file.domain.FileInfo fileInfo : archiveFiles) {
                String filename = fileInfo.getOriginalFilename();
                try {
                    log.info("正在处理MinIO文件: {}, 文件ID: {}", filename, fileInfo.getId());

                    // 1. 计算文件哈希并检查重复（使用已存储的MD5）
                    String hash = fileInfo.getMd5Hash();
                    if (hash == null) {
                        log.warn("文件 {} 没有MD5哈希值，跳过", filename);
                        skippedCount++;
                        continue;
                    }

                    checkDuplicateHash(hash);

                    // 2. 使用BatchFileService解压并上传文件
                    List<FileUploadResponse> uploadResults = processMinIOArchiveFile(fileInfo);

                    if (uploadResults.isEmpty()) {
                        log.warn("文件 {} 解压失败，没有返回结果", filename);
                        errorCount++;
                        continue;
                    }

                    // 3. 保存数据源记录
                    AnnotationImageSources source = saveSourceRecordWithExtractedFiles(
                            fileInfo.getId(), filename, "scanned_import", hash, uploadResults);

                    processedCount++;
                    log.info("成功处理MinIO文件: {}, 数据源ID: {}", filename, source.getSourceId());

                } catch (IllegalStateException e) {
                    // 文件重复，跳过
                    log.warn("跳过重复文件: {} - {}", filename, e.getMessage());
                    skippedCount++;
                } catch (Exception e) {
                    log.error("处理MinIO文件失败: {} - 错误: {}", filename, e.getMessage(), e);
                    errorCount++;
                }
            }
        } catch (Exception e) {
            log.error("扫描MinIO目录失败", e);
            throw new IOException("扫描MinIO目录失败: " + e.getMessage(), e);
        }

        log.info("MinIO扫描导入完成 - 成功: {}, 跳过: {}, 失败: {}", processedCount, skippedCount, errorCount);
    }

    @Override
    public void scanAndImportSourcesWithImageProcessing(Long categoryId) throws IOException {
        int processedCount = 0;
        int skippedCount = 0;
        int errorCount = 0;

        try {
            // 扫描MinIO存储桶中的压缩文件
            List<com.ylzx.file.domain.FileInfo> archiveFiles = fileService.listArchiveFilesInMinIO("annotation/scan");

            if (archiveFiles.isEmpty()) {
                log.info("MinIO扫描目录 'annotation/scan' 中没有找到压缩文件");
                return;
            }

            log.info("在MinIO中找到 {} 个压缩文件，开始处理并自动关联到分类 {}", archiveFiles.size(), categoryId);

            for (com.ylzx.file.domain.FileInfo fileInfo : archiveFiles) {
                String filename = fileInfo.getOriginalFilename();
                try {
                    log.info("正在处理MinIO文件: {}, 文件ID: {}", filename, fileInfo.getId());

                    // 1. 计算文件哈希并检查重复（使用已存储的MD5）
                    String hash = fileInfo.getMd5Hash();
                    if (hash == null) {
                        log.warn("文件 {} 没有MD5哈希值，跳过", filename);
                        skippedCount++;
                        continue;
                    }

                    checkDuplicateHash(hash);

                    // 2. 使用BatchFileService解压并上传文件
                    List<FileUploadResponse> uploadResults = processMinIOArchiveFile(fileInfo);

                    if (uploadResults.isEmpty()) {
                        log.warn("文件 {} 解压失败，没有返回结果", filename);
                        errorCount++;
                        continue;
                    }

                    // 3. 保存数据源记录
                    AnnotationImageSources source = saveSourceRecordWithExtractedFiles(
                            fileInfo.getId(), filename, "scanned_import", hash, uploadResults);

                    // 4. 如果指定了分类ID，自动处理图片并关联到分类
                    if (categoryId != null) {
                        categoriesSourcesService.associateCategoryWithSource(categoryId, source.getSourceId(), SecurityUtils.getUserId());
                        processExtractedImagesFromMinIO(source.getSourceId(), categoryId, uploadResults);
                        log.info("已自动处理数据源{}的图片并关联到分类{}", source.getSourceId(), categoryId);
                    }

                    processedCount++;
                    log.info("成功处理MinIO文件: {}, 数据源ID: {}", filename, source.getSourceId());

                } catch (IllegalStateException e) {
                    // 文件重复，跳过
                    log.warn("跳过重复文件: {} - {}", filename, e.getMessage());
                    skippedCount++;
                } catch (Exception e) {
                    log.error("处理MinIO文件失败: {} - 错误: {}", filename, e.getMessage(), e);
                    errorCount++;
                }
            }
        } catch (Exception e) {
            log.error("扫描MinIO目录失败", e);
            throw new IOException("扫描MinIO目录失败: " + e.getMessage(), e);
        }

        log.info("MinIO扫描导入完成（含图片处理） - 成功: {}, 跳过: {}, 失败: {}", processedCount, skippedCount, errorCount);
    }

    /**
     * 上传文件到MinIO
     */
    private String uploadFileToMinio(MultipartFile file, String folderPath, String createBy) {
        try {
            // 创建上传请求
            FileUploadRequest request = new FileUploadRequest();
            request.setFolderPath(folderPath);
            request.setCreateBy(createBy);
            request.setRemark("标注数据源文件");

            // 调用文件服务上传
            FileUploadResponse response = fileService.uploadFile(file, request);
            return response.getId();
        } catch (Exception e) {
            log.error("上传文件到MinIO失败", e);
            throw new RuntimeException("上传文件到MinIO失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存数据源记录
     */
    private AnnotationImageSources saveSourceRecord(String fileId, String originalFilename, String uploadType, String hash) {
        AnnotationImageSources newSource = new AnnotationImageSources();
        newSource.setSourceName(originalFilename);
        newSource.setUploadType(uploadType);
        newSource.setFileId(fileId);  // 使用MinIO文件ID
        newSource.setContentHash(hash);
        newSource.setUploadedByUserId(SecurityUtils.getUserId());
        newSource.setUploadedAt(LocalDateTime.now());
        newSource.setCreateBy(SecurityUtils.getUsername());
        newSource.setCreateTime(LocalDateTime.now());
        newSource.setUpdateTime(LocalDateTime.now());

        // 插入数据库，MyBatis 会自动回填 sourceId
        annotationImageSourcesMapper.insertAnnotationImageSources(newSource);

        log.info("保存数据源记录成功，sourceId: {}, 文件名: {}", newSource.getSourceId(), originalFilename);
        return newSource;
    }

    /**
     * 保存数据源记录（包含解压文件信息）
     */
    private AnnotationImageSources saveSourceRecordWithExtractedFiles(String fileId, String originalFilename,
                                                                      String uploadType, String hash, List<FileUploadResponse> uploadResults) {
        AnnotationImageSources newSource = new AnnotationImageSources();
        newSource.setSourceName(originalFilename);
        newSource.setUploadType(uploadType);
        newSource.setFileId(fileId);  // 压缩包文件ID
        newSource.setContentHash(hash);
        newSource.setUploadedByUserId(SecurityUtils.getUserId());
        newSource.setUploadedAt(LocalDateTime.now());
        newSource.setCreateBy(SecurityUtils.getUsername());
        newSource.setCreateTime(LocalDateTime.now());
        newSource.setUpdateTime(LocalDateTime.now());

        // 可以在这里记录解压后的文件数量等信息
        int extractedFileCount = uploadResults.size() - 1; // 减去压缩包本身
        newSource.setRemark("已解压到MinIO，包含 " + extractedFileCount + " 个文件");

        // 插入数据库，MyBatis 会自动回填 sourceId
        annotationImageSourcesMapper.insertAnnotationImageSources(newSource);

        log.info("保存数据源记录成功，sourceId: {}, 文件名: {}", newSource.getSourceId(), originalFilename);
        return newSource;
    }

    /**
     * 处理MinIO中解压后的图片文件（并行处理）
     */
    private void processExtractedImagesFromMinIO(Long sourceId, Long categoryId, List<FileUploadResponse> uploadResults) {
        try {
            // 跳过第一个文件（压缩包本身），处理解压后的文件
            List<FileUploadResponse> extractedFiles = uploadResults.subList(1, uploadResults.size());

            // 过滤出图片文件
            List<FileUploadResponse> imageFiles = extractedFiles.stream()
                    .filter(file -> file.getOriginalFilename() != null && isSupportedImageFile(file.getOriginalFilename()))
                    .toList();

            if (imageFiles.isEmpty()) {
                log.warn("没有找到支持的图片文件");
                return;
            }

            log.info("开始并行处理 {} 个图片文件", imageFiles.size());

            // 在并行处理前获取用户信息，避免在并行流中访问SecurityContext
            String currentUsername = SecurityUtils.getUsername();
            LocalDateTime uploadTime = LocalDateTime.now();

            // 使用并行流处理图片文件
            List<AnnotationImages> processedImages = imageFiles.parallelStream()
                    .map(fileResponse -> processImageFile(sourceId, categoryId, fileResponse, currentUsername, uploadTime))
                    .filter(image -> image != null)
                    .toList();

            // 批量保存图片记录
            if (!processedImages.isEmpty()) {
                annotationImagesService.insertBatch(processedImages);
                log.info("成功批量保存 {} 个图片文件到数据源 {} 和分类 {}", processedImages.size(), sourceId, categoryId);
            }

        } catch (Exception e) {
            log.error("处理MinIO解压文件失败", e);
            throw new RuntimeException("处理MinIO解压文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 处理单个图片文件
     */
    private AnnotationImages processImageFile(Long sourceId, Long categoryId, FileUploadResponse fileResponse,
                                            String username, LocalDateTime uploadTime) {
        try {
            String fileName = fileResponse.getOriginalFilename();
            log.debug("开始处理图片文件: {}, 文件ID: {}", fileName, fileResponse.getId());

            // 创建图片记录
            AnnotationImages image = new AnnotationImages();
            image.setSourceId(sourceId);
            image.setCategoryId(categoryId);
            image.setFileId(fileResponse.getId()); // MinIO文件ID
            image.setOriginalFilename(fileName);
            image.setMd5Hash(fileResponse.getMd5Hash());
            image.setFileSizeBytes(fileResponse.getFileSize());
            image.setUploadedAt(uploadTime);
            image.setCreateBy(username);

            // 直接从 FileUploadResponse 获取图片尺寸信息（避免额外的 I/O 操作）
            if (fileResponse.getImageWidth() != null && fileResponse.getImageHeight() != null) {
                image.setWidth(fileResponse.getImageWidth().longValue());
                image.setHeight(fileResponse.getImageHeight().longValue());
            } else {
                log.warn("图片尺寸信息缺失: {}", fileName);
                image.setWidth(0L);
                image.setHeight(0L);
            }

            log.debug("已处理图片文件: {}, 尺寸: {}x{}", fileName, image.getWidth(), image.getHeight());
            return image;

        } catch (Exception e) {
            log.error("处理单个图片文件失败: {}", fileResponse.getOriginalFilename(), e);
            return null;
        }
    }

    /**
     * 处理MinIO中的压缩文件
     */
    private List<FileUploadResponse> processMinIOArchiveFile(com.ylzx.file.domain.FileInfo fileInfo) {
        try {
            // 从MinIO下载文件
            InputStream fileStream = fileService.downloadFile(fileInfo.getId());
            if (fileStream == null) {
                throw new RuntimeException("无法从MinIO下载文件: " + fileInfo.getOriginalFilename());
            }

            // 创建临时MultipartFile
            byte[] fileBytes = fileStream.readAllBytes();
            MultipartFile tempFile = new SimpleMultipartFile(fileInfo.getOriginalFilename(), fileBytes);

            // 使用BatchFileService解压并上传
            return batchFileService.extractAndUploadFromArchive(
                    tempFile, "annotation/sources", SecurityUtils.getUsername());

        } catch (Exception e) {
            log.error("处理MinIO压缩文件失败: {}", fileInfo.getOriginalFilename(), e);
            throw new RuntimeException("处理MinIO压缩文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查是否为支持的图片文件
     */
    private boolean isSupportedImageFile(String filename) {
        if (filename == null) return false;
        String lowerName = filename.toLowerCase();
        return lowerName.endsWith(".jpg") || lowerName.endsWith(".jpeg") ||
                lowerName.endsWith(".png") || lowerName.endsWith(".bmp") ||
                lowerName.endsWith(".gif") || lowerName.endsWith(".webp");
    }

    /**
     * 简单的MultipartFile实现
     */
    private static class SimpleMultipartFile implements MultipartFile {
        private final String name;
        private final byte[] content;

        public SimpleMultipartFile(String name, byte[] content) {
            this.name = name;
            this.content = content;
        }

        @Override
        public String getName() {
            return "file";
        }

        @Override
        public String getOriginalFilename() {
            return name;
        }

        @Override
        public String getContentType() {
            return "application/octet-stream";
        }

        @Override
        public boolean isEmpty() {
            return content.length == 0;
        }

        @Override
        public long getSize() {
            return content.length;
        }

        @Override
        public byte[] getBytes() throws IOException {
            return content;
        }

        @Override
        public InputStream getInputStream() throws IOException {
            return new ByteArrayInputStream(content);
        }

        @Override
        public void transferTo(File dest) throws IOException, IllegalStateException {
            try (FileOutputStream fos = new FileOutputStream(dest)) {
                fos.write(content);
            }
        }
    }

    /**
     * 计算文件的部分哈希值（支持MultipartFile）
     */
    private String calculatePartialHash(MultipartFile file) throws IOException {
        final int CHUNK_SIZE = 1024 * 1024; // 1MB
        try {
            long fileSize = file.getSize();
            MessageDigest digest = MessageDigest.getInstance("SHA-256");

            // 1. 读取头部
            if (fileSize > 0) {
                byte[] fileBytes = file.getBytes();
                int headSize = Math.min(CHUNK_SIZE, fileBytes.length);
                digest.update(fileBytes, 0, headSize);

                // 2. 读取中部（如果文件足够大）
                if (fileSize > 2 * CHUNK_SIZE) {
                    int midStart = (int) (fileSize / 2 - CHUNK_SIZE / 2);
                    int midSize = Math.min(CHUNK_SIZE, fileBytes.length - midStart);
                    digest.update(fileBytes, midStart, midSize);
                }

                // 3. 读取尾部（如果文件足够大）
                if (fileSize > CHUNK_SIZE) {
                    int tailStart = Math.max(0, fileBytes.length - CHUNK_SIZE);
                    int tailSize = fileBytes.length - tailStart;
                    digest.update(fileBytes, tailStart, tailSize);
                }
            }

            // 4. 转换为十六进制字符串
            byte[] hashBytes = digest.digest();
            StringBuilder hexString = new StringBuilder();
            for (byte b : hashBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new IOException("SHA-256算法不可用", e);
        }
    }




    /**
     * 检查内容哈希是否存在，如果存在则抛出异常.
     * @param hash 要检查的哈希值
     * @throws IllegalStateException 如果哈希已存在
     */
    private void checkDuplicateHash(String hash) {
        // 使用COUNT查询检查是否存在重复哈希，性能更好
        int count = this.baseMapper.countByContentHash(hash);
        if (count > 0) {
            throw new IllegalStateException("一个具有相同内容的文件已经存在，内容哈希: " + hash);
        }
    }





    /**
     * 【定时任务】定时扫描并导入MinIO中的压缩文件
     * 默认每10分钟执行一次，扫描MinIO指定目录下的压缩文件并自动导入
     * 需要确保在启动类上有 @EnableScheduling 注解
     */
    @Scheduled(cron = "0 */10 * * * ?")
    public void scheduledScanAndImport() {
        try {
            log.info("开始执行MinIO定时扫描任务...");
            // 默认启用自动图片处理，使用分类ID为1（可以通过配置文件配置）
            Long defaultCategoryId = 1L; // 可以从配置文件读取

            log.info("启用自动图片处理，默认分类ID: {}", defaultCategoryId);
            scanAndImportSourcesWithImageProcessing(defaultCategoryId);

            log.info("MinIO定时扫描任务执行完成");
        } catch (Exception e) {
            log.error("MinIO定时扫描任务执行失败", e);
        }
    }

    /**
     * 检查文件是否为支持的压缩文件格式
     * @param filename 文件名
     * @return 是否支持
     */
    private boolean isSupportedArchiveFile(String filename) {
        String lowerCaseName = filename.toLowerCase();
        return SUPPORTED_ARCHIVE_EXTENSIONS.stream()
                .anyMatch(lowerCaseName::endsWith);
    }







    /**
     * 【定时任务】清理MinIO中的过期临时文件
     * 每天凌晨2点执行，清理7天前的临时文件
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredFiles() {
        try {
            log.info("开始执行MinIO临时文件清理任务");
            // 设置清理时间阈值：7天前
            LocalDateTime cutoffTime = LocalDateTime.now().minusDays(7);

            // 清理MinIO中的过期临时文件
            cleanupExpiredMinIOFiles("annotation/temp", cutoffTime);
            cleanupExpiredMinIOFiles("annotation/error", cutoffTime);

            log.info("MinIO临时文件清理任务执行完成");
        } catch (Exception e) {
            log.error("MinIO临时文件清理任务执行失败", e);
        }
    }

    /**
     * 清理MinIO中指定目录的过期文件
     */
    private void cleanupExpiredMinIOFiles(String folderPath, LocalDateTime cutoffTime) {
        try {
            List<com.ylzx.file.domain.FileInfo> files = fileService.listFolderFiles(folderPath);
            int deletedCount = 0;

            for (com.ylzx.file.domain.FileInfo fileInfo : files) {
                if (fileInfo.getCreateTime().isBefore(cutoffTime)) {
                    try {
                        fileService.deleteFile(fileInfo.getId());
                        deletedCount++;
                        log.debug("已删除过期文件: {}", fileInfo.getOriginalFilename());
                    } catch (Exception e) {
                        log.warn("删除过期文件失败: {} - 错误: {}", fileInfo.getOriginalFilename(), e.getMessage());
                    }
                }
            }

            if (deletedCount > 0) {
                log.info("在目录 {} 中删除了 {} 个过期文件", folderPath, deletedCount);
            }
        } catch (Exception e) {
            log.error("清理MinIO目录 {} 中的过期文件失败", folderPath, e);
        }
    }

    @Override
    public List<Map<String, Object>> getScanDirectoryFiles() {
        List<Map<String, Object>> files = new ArrayList<>();

        try {
            // 扫描MinIO存储桶中的压缩文件
            List<com.ylzx.file.domain.FileInfo> archiveFiles = fileService.listArchiveFilesInMinIO("annotation/scan");

            for (com.ylzx.file.domain.FileInfo fileInfo : archiveFiles) {
                Map<String, Object> fileInfoMap = new HashMap<>();
                fileInfoMap.put("filename", fileInfo.getOriginalFilename());
                fileInfoMap.put("size", fileInfo.getFileSize());
                fileInfoMap.put("lastModified", fileInfo.getCreateTime().atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli());
                fileInfoMap.put("path", fileInfo.getFilePath());
                fileInfoMap.put("fileId", fileInfo.getId());
                fileInfoMap.put("md5Hash", fileInfo.getMd5Hash());
                files.add(fileInfoMap);
            }
        } catch (Exception e) {
            log.error("获取MinIO扫描目录文件列表失败", e);
        }

        return files;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AnnotationImageSources processScanFile(String filename, Long categoryId) throws IOException {
        try {
            log.info("手动处理MinIO扫描文件: {}, 分类ID: {}", filename, categoryId);

            // 1. 从MinIO扫描目录中查找文件
            List<FileInfo> archiveFiles = fileService.listArchiveFilesInMinIO("annotation/scan");
            FileInfo targetFile = archiveFiles.stream()
                    .filter(file -> filename.equals(file.getOriginalFilename()))
                    .findFirst()
                    .orElseThrow(() -> new IllegalArgumentException("文件不存在: " + filename));

            if (!isSupportedArchiveFile(filename)) {
                throw new IllegalArgumentException("不支持的文件格式: " + filename);
            }

            // 2. 检查文件哈希并验证重复
            String hash = targetFile.getMd5Hash();
            if (hash == null) {
                throw new IllegalArgumentException("文件 " + filename + " 没有MD5哈希值");
            }
            checkDuplicateHash(hash);

            // 3. 使用BatchFileService解压并上传文件
            List<FileUploadResponse> uploadResults = processMinIOArchiveFile(targetFile);

            if (uploadResults.isEmpty()) {
                throw new IOException("文件 " + filename + " 解压失败，没有返回结果");
            }

            // 4. 保存数据源记录
            AnnotationImageSources source = saveSourceRecordWithExtractedFiles(
                    targetFile.getId(), filename, "manual_import", hash, uploadResults);

            // 5. 如果指定了分类ID，自动处理图片
            if (categoryId != null) {
                try {
                    log.info("开始自动处理数据源 {} 中的图片到分类 {}", source.getSourceId(), categoryId);
                    // 创建分类与数据源的关联
                    categoriesSourcesService.associateCategoryWithSource(categoryId, source.getSourceId(), SecurityUtils.getUserId());
                    // 处理解压后的图片并关联到指定分类
                    processExtractedImagesFromMinIO(source.getSourceId(), categoryId, uploadResults);
                    log.info("自动处理图片完成，数据源ID: {}, 分类ID: {}", source.getSourceId(), categoryId);
                } catch (Exception e) {
                    log.error("自动处理图片失败，数据源ID: {}, 分类ID: {} - 错误: {}",
                            source.getSourceId(), categoryId, e.getMessage(), e);
                    // 不抛出异常，允许数据源记录保存成功，图片处理可以后续手动执行
                }
            } else {
                log.info("未指定分类ID，仅创建数据源记录，sourceId: {}", source.getSourceId());
            }

            log.info("成功处理MinIO文件: {}, 数据源ID: {}", filename, source.getSourceId());
            return source;

        } catch (IllegalStateException e) {
            log.warn("跳过文件（内容重复）: {} - {}", filename, e.getMessage());
            throw e; // 重新抛出，让调用者知道是重复文件
        } catch (Exception e) {
            log.error("处理MinIO文件失败: {} - 错误: {}", filename, e.getMessage(), e);
            throw new IOException("处理文件失败: " + filename, e);
        }
    }


}

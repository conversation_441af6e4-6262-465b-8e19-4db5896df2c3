#!/bin/bash

echo "========================================"
echo "Static Build for Portable Deployment"
echo "========================================"

# 强制静态编译
export BUILD_STATIC=ON

# 检查是否在MSYS2环境中
if [ -z "$MSYSTEM" ]; then
    echo "❌ Error: This script should be run in MSYS2 MinGW64 environment"
    echo "Please open MSYS2 MinGW 64-bit terminal and run this script"
    exit 1
fi

echo "✓ MSYS2 environment detected: $MSYSTEM"
echo "✓ Build mode: Static (portable)"

# 检查必要的静态库
echo ""
echo "Checking static libraries..."

check_static_lib() {
    local lib_name=$1
    local lib_path="/mingw64/lib/lib${lib_name}.a"
    
    if [ -f "$lib_path" ]; then
        echo "✓ $lib_name static library found"
        return 0
    else
        echo "❌ $lib_name static library not found at $lib_path"
        return 1
    fi
}

# 检查关键的静态库
MISSING_STATIC_LIBS=()

if ! check_static_lib "opencv_core"; then
    MISSING_STATIC_LIBS+=("opencv static libraries")
fi

if ! check_static_lib "curl"; then
    MISSING_STATIC_LIBS+=("curl static library")
fi

if ! check_static_lib "ssl"; then
    MISSING_STATIC_LIBS+=("openssl static libraries")
fi

# 如果缺少静态库，提示安装
if [ ${#MISSING_STATIC_LIBS[@]} -gt 0 ]; then
    echo ""
    echo "⚠ Some static libraries are missing:"
    for lib in "${MISSING_STATIC_LIBS[@]}"; do
        echo "  - $lib"
    done
    echo ""
    echo "This may result in dynamic dependencies. To install static versions:"
    echo "  pacman -S mingw-w64-x86_64-opencv-static"
    echo "  pacman -S mingw-w64-x86_64-curl-static"
    echo "  pacman -S mingw-w64-x86_64-openssl-static"
    echo ""
    echo "Continuing with available libraries..."
fi

# 设置vcpkg（仅用于MinIO）
if [ -z "$VCPKG_ROOT" ]; then
    if [ -d "$HOME/vcpkg" ]; then
        export VCPKG_ROOT="$HOME/vcpkg"
        echo "✓ Found vcpkg: $VCPKG_ROOT"
    elif [ -d "/c/vcpkg" ]; then
        export VCPKG_ROOT="/c/vcpkg"
        echo "✓ Found vcpkg: $VCPKG_ROOT"
    else
        echo "⚠ vcpkg not found. Building without MinIO support."
        VCPKG_ROOT=""
    fi
fi

# 检查MinIO
MINIO_FOUND=false
if [ -n "$VCPKG_ROOT" ] && [ -f "$VCPKG_ROOT/vcpkg.exe" ]; then
    if $VCPKG_ROOT/vcpkg.exe list | grep -q "minio"; then
        echo "✓ MinIO found in vcpkg"
        MINIO_FOUND=true
        export CMAKE_TOOLCHAIN_FILE="$VCPKG_ROOT/scripts/buildsystems/vcpkg.cmake"
        export VCPKG_TARGET_TRIPLET="x64-mingw-static"  # 使用静态triplet
    fi
fi

# 创建构建目录
mkdir -p build-static
cd build-static

# 清理之前的构建
rm -f CMakeCache.txt
rm -rf CMakeFiles

echo ""
echo "========================================"
echo "Configuring for static build..."
echo "========================================"

# CMake配置参数
CMAKE_ARGS=(
    -G "MinGW Makefiles"
    -DCMAKE_BUILD_TYPE=Release
    -DBUILD_STATIC=ON
    -DCMAKE_C_COMPILER=gcc
    -DCMAKE_CXX_COMPILER=g++
    -DCMAKE_FIND_LIBRARY_SUFFIXES=".a"
    -DBUILD_SHARED_LIBS=OFF
)

# 静态链接标志
CMAKE_ARGS+=(
    -DCMAKE_EXE_LINKER_FLAGS="-static-libgcc -static-libstdc++ -static"
    -DCMAKE_SHARED_LINKER_FLAGS="-static-libgcc -static-libstdc++ -Wl,-Bstatic"
)

# 如果有MinIO支持
if [ "$MINIO_FOUND" = true ]; then
    CMAKE_ARGS+=(
        -DCMAKE_TOOLCHAIN_FILE="$CMAKE_TOOLCHAIN_FILE"
        -DVCPKG_TARGET_TRIPLET="$VCPKG_TARGET_TRIPLET"
    )
    echo "✓ Including MinIO support (static)"
fi

# 配置CMake
cmake .. "${CMAKE_ARGS[@]}"

if [ $? -ne 0 ]; then
    echo "❌ CMake configuration failed!"
    exit 1
fi

echo ""
echo "========================================"
echo "Building static library..."
echo "========================================"

# 编译
make -j$(nproc) VERBOSE=1

if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    exit 1
fi

echo ""
echo "========================================"
echo "Analyzing build results..."
echo "========================================"

# 查找生成的库文件
LIBRARY_FOUND=false
LIBRARY_FILE=""

for lib in libdataset_export.dll dataset_export.dll; do
    if [ -f "$lib" ]; then
        echo "✓ Found library: $lib"
        LIBRARY_FOUND=true
        LIBRARY_FILE="$lib"
        
        # 显示文件大小
        ls -lh "$lib"
        
        # 分析依赖
        echo ""
        echo "Analyzing dependencies:"
        if command -v ldd >/dev/null 2>&1; then
            echo "Dynamic dependencies:"
            ldd "$lib" | grep -v "not found" | head -10
            
            # 检查是否有MinGW运行时依赖
            if ldd "$lib" | grep -q "libgcc_s_seh-1.dll\|libstdc++-6.dll"; then
                echo ""
                echo "⚠ Warning: Still has MinGW runtime dependencies"
                echo "This may require MinGW DLLs on target machines"
            else
                echo ""
                echo "✓ No MinGW runtime dependencies detected"
            fi
        fi
        
        break
    fi
done

if [ "$LIBRARY_FOUND" = false ]; then
    echo "❌ No library file found!"
    echo "Available files:"
    ls -la
    exit 1
fi

# 复制到Java项目
JAVA_RESOURCES_DIR="../ylzx-annotation/src/main/resources/native"
if [ -d "$JAVA_RESOURCES_DIR" ]; then
    echo ""
    echo "Copying to Java resources directory..."
    cp "$LIBRARY_FILE" "$JAVA_RESOURCES_DIR/"
    if [ $? -eq 0 ]; then
        echo "✓ Library copied to: $JAVA_RESOURCES_DIR/$LIBRARY_FILE"
    else
        echo "❌ Failed to copy library"
    fi
else
    echo "⚠ Java resources directory not found: $JAVA_RESOURCES_DIR"
fi

# 创建部署包
echo ""
echo "Creating deployment package..."
DEPLOY_DIR="../deploy-static"
mkdir -p "$DEPLOY_DIR"
cp "$LIBRARY_FILE" "$DEPLOY_DIR/"

# 创建部署说明
cat > "$DEPLOY_DIR/README.txt" << EOF
Static Build Deployment Package
===============================

Library: $LIBRARY_FILE
Build date: $(date)
Build environment: $MSYSTEM

This library was built with static linking to minimize dependencies.

Dependencies analysis:
$(ldd "$LIBRARY_FILE" 2>/dev/null | head -10 || echo "Could not analyze dependencies")

Installation:
1. Copy $LIBRARY_FILE to your Java project's resources/native/ directory
2. Ensure your Java application loads this library
3. Test on target machines

Note: This library should work on Windows machines without requiring
MinGW or additional DLL files (except standard Windows system libraries).
EOF

echo "✓ Deployment package created in: $DEPLOY_DIR"

echo ""
echo "========================================"
echo "Static Build Summary"
echo "========================================"
echo "✓ Library: $LIBRARY_FILE"
echo "✓ Size: $(ls -lh "$LIBRARY_FILE" | awk '{print $5}')"
echo "✓ Location: $(pwd)/$LIBRARY_FILE"
echo "✓ Deployment: $DEPLOY_DIR"
echo ""
echo "This library should be portable and work on other Windows machines"
echo "without requiring MinGW or additional runtime libraries."
echo ""
echo "Next steps:"
echo "1. Test the library on your development machine"
echo "2. Copy to target machines and test"
echo "3. If there are still dependencies, check the analysis above"

@echo off
echo 开始诊断Redis连接问题...
echo.

echo 1. 测试网络连通性...
ping -n 2 192.168.101.121
if %errorlevel% neq 0 (
    echo ❌ 无法ping通Redis服务器 192.168.101.121
    echo    请检查网络连接或服务器状态
    goto :end
) else (
    echo ✅ 网络连接正常
)

echo.
echo 2. 测试Redis端口连通性...
powershell "Test-NetConnection -ComputerName 192.168.101.121 -Port 6379" | findstr "TcpTestSucceeded"
if %errorlevel% neq 0 (
    echo ❌ 无法连接到Redis端口 6379
    echo    可能的原因:
    echo    - Redis服务未启动
    echo    - 防火墙阻止了6379端口
    echo    - Redis配置不允许远程连接
    goto :end
) else (
    echo ✅ Redis端口连接正常
)

echo.
echo 3. 如果有redis-cli，尝试连接测试...
redis-cli -h 192.168.101.121 -p 6379 -a "Ylzx@2024!+9821*" ping 2>nul
if %errorlevel% equ 0 (
    echo ✅ Redis认证和连接测试成功
) else (
    echo ⚠️  Redis连接测试失败或redis-cli未安装
    echo    这可能是认证问题或Redis配置问题
)

echo.
echo 4. 建议检查项目:
echo    - 确认Redis服务在192.168.101.121上正在运行
echo    - 检查Redis配置是否允许远程连接 (bind 0.0.0.0)
echo    - 检查防火墙设置 (端口6379)
echo    - 验证Redis密码是否正确: Ylzx@2024!+9821*
echo    - 确认数据库索引7是否存在且可用

:end
echo.
echo 诊断完成。如果网络和端口都正常但仍无法连接，
echo 请联系Redis服务器管理员检查服务状态。
echo.
pause
package com.ylzx.annotation.controller;

import java.util.List;

import com.ylzx.annotation.domain.enums.AnnotationStatus;
import com.ylzx.annotation.service.AnnotationReviewsService;
import com.ylzx.common.utils.SecurityUtils;
import com.ylzx.common.utils.StringUtils;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.ylzx.common.annotation.Log;
import com.ylzx.common.core.controller.BaseController;
import com.ylzx.common.core.domain.AjaxResult;
import com.ylzx.common.enums.BusinessType;
import com.ylzx.annotation.domain.AnnotationReviews;
import com.ylzx.common.utils.poi.ExcelUtil;
import com.ylzx.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 标注审核Controller
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Api(tags = "标注审核管理")
@RestController
@RequestMapping("/annotation/reviews")
public class AnnotationReviewsController extends BaseController
{
    @Resource
    private AnnotationReviewsService annotationReviewsService;

    /**
     * 查询标注审核列表
     */
    @ApiOperation("查询标注审核列表")
    @PreAuthorize("@ss.hasPermi('system:reviews:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") AnnotationReviews annotationReviews, @RequestParam(name = "status", required = false) String status)
    {
        if (StringUtils.isEmpty(status))
            annotationReviews.setStatus(null);
        startPage();
        List<AnnotationReviews> list = annotationReviewsService.selectAnnotationReviewsList(annotationReviews);
        return getDataTable(list);
    }


    /**
     * 获取当前用户不通过的审核记录
     * @return
     */
    @GetMapping("/getRejectList")
    public AjaxResult getRejectList()
    {
        AnnotationReviews annotationReviews = new AnnotationReviews();
        annotationReviews.setStatus(AnnotationStatus.REJECTED);
//        annotationReviews.setRejectNum(1);
        annotationReviews.setAnnotatorId(SecurityUtils.getLoginUser().getUserId());

        List<AnnotationReviews> list = annotationReviewsService.selectAnnotationReviewsList(annotationReviews);
        return AjaxResult.success(list);
    }



    /**
     * 导出标注审核列表
     */
    @PreAuthorize("@ss.hasPermi('system:reviews:export')")
    @Log(title = "标注审核", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AnnotationReviews annotationReviews)
    {
        List<AnnotationReviews> list = annotationReviewsService.selectAnnotationReviewsList(annotationReviews);
        ExcelUtil<AnnotationReviews> util = new ExcelUtil<AnnotationReviews>(AnnotationReviews.class);
        util.exportExcel(response, list, "标注审核数据");
    }

    /**
     * 获取标注审核详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:reviews:query')")
    @GetMapping(value = "/{reviewId}")
    public AjaxResult getInfo(@PathVariable("reviewId") Long reviewId)
    {
        return success(annotationReviewsService.selectAnnotationReviewsByReviewId(reviewId));
    }

    /**
     * 新增标注审核
     */
    @ApiOperation("新增标注审核")
    @PreAuthorize("@ss.hasPermi('system:reviews:add')")
    @Log(title = "标注审核", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("标注审核信息") @RequestBody AnnotationReviews annotationReviews)
    {
        return toAjax(annotationReviewsService.insertAnnotationReviews(annotationReviews));
    }

    /**
     * 修改标注审核
     */
    @PreAuthorize("@ss.hasPermi('system:reviews:edit')")
    @Log(title = "标注审核", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AnnotationReviews annotationReviews)
    {
        return toAjax(annotationReviewsService.updateAnnotationReviews(annotationReviews));
    }

    /**
     * 删除标注审核
     */
    @ApiOperation("删除标注审核")
    @PreAuthorize("@ss.hasPermi('system:reviews:remove')")
    @Log(title = "标注审核", businessType = BusinessType.DELETE)
	@DeleteMapping("/{reviewIds}")
    public AjaxResult remove(@ApiParam("审核ID数组") @PathVariable("reviewIds") Long[] reviewIds)
    {
        return toAjax(annotationReviewsService.deleteAnnotationReviewsByReviewIds(reviewIds));
    }

    /**
     * 根据标注人ID查询审核记录
     */
    @ApiOperation("根据标注人ID查询审核记录")
    @PreAuthorize("@ss.hasPermi('system:reviews:list')")
    @GetMapping("/annotator/{annotatorId}")
    public AjaxResult getReviewsByAnnotatorId(@ApiParam("标注人ID") @PathVariable("annotatorId") Long annotatorId)
    {
        List<AnnotationReviews> reviews = annotationReviewsService.selectAnnotationReviewsByAnnotatorId(annotatorId);
        return success(reviews);
    }

    /**
     * 根据图片ID查询审核记录
     */
    @ApiOperation("根据图片ID查询审核记录")
    @PreAuthorize("@ss.hasPermi('system:reviews:list')")
    @GetMapping("/image/{imageId}")
    public AjaxResult getReviewsByImageId(@ApiParam("图片ID") @PathVariable("imageId") Long imageId)
    {
        List<AnnotationReviews> reviews = annotationReviewsService.selectAnnotationReviewsByImageId(imageId);
        return success(reviews);
    }

    /**
     * 统计标注人的审核情况
     */
    @ApiOperation("统计标注人的审核情况")
    @PreAuthorize("@ss.hasPermi('system:reviews:list')")
    @GetMapping("/stats/annotator/{annotatorId}")
    public AjaxResult getAnnotatorStats(@ApiParam("标注人ID") @PathVariable("annotatorId") Long annotatorId)
    {
        java.util.Map<String, Object> stats = annotationReviewsService.countReviewStatsByAnnotatorId(annotatorId);
        return success(stats);
    }
}

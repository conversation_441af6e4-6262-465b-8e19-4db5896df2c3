-- 1. 创建表结构（移除了所有内联的 COMMENT）
CREATE TABLE file_info (
                           id VARCHAR(32) NOT NULL,
                           file_name VARCHAR(500) NOT NULL,
                           original_filename VARCHAR(500) NOT NULL,
                           file_size BIGINT NOT NULL,
                           content_type VARCHAR(100),
                           file_extension VARCHAR(20),
                           file_path VARCHAR(1000) NOT NULL,
                           file_url VARCHAR(1000),
                           md5_hash VARCHAR(32),

    -- 缩略图相关
                           thumbnail_path VARCHAR(1000),
                           thumbnail_url VARCHAR(1000),
                           thumbnail_size BIGINT,

    -- 图片尺寸信息
                           image_width INTEGER,
                           image_height INTEGER,

    -- 文件状态
                           status SMALLINT DEFAULT 1,

    -- 审计字段
                           create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                           update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                           create_by VARCHAR(64),
                           update_by VARCHAR(64),
                           remark TEXT,

                           PRIMARY KEY (id)
);

-- 2. 添加表和所有字段的注释
COMMENT ON TABLE file_info IS '文件信息表 - 基于MinIO的文件存储';
COMMENT ON COLUMN file_info.id IS '文件唯一标识，使用UUID';
COMMENT ON COLUMN file_info.file_name IS '存储在MinIO中的文件名，通常包含时间戳等唯一标识';
COMMENT ON COLUMN file_info.original_filename IS '用户上传时的原始文件名';
COMMENT ON COLUMN file_info.file_size IS '文件大小（字节）';
COMMENT ON COLUMN file_info.content_type IS '文件MIME类型';
COMMENT ON COLUMN file_info.file_extension IS '文件扩展名';
COMMENT ON COLUMN file_info.file_path IS 'MinIO中的完整路径，如：images/2024/01/01/filename.jpg';
COMMENT ON COLUMN file_info.file_url IS '文件的访问URL，可以是MinIO的预签名URL或代理URL';
COMMENT ON COLUMN file_info.md5_hash IS '文件内容的MD5哈希值，用于文件去重和完整性校验';
COMMENT ON COLUMN file_info.thumbnail_path IS '缩略图路径';
COMMENT ON COLUMN file_info.thumbnail_url IS '缩略图访问URL';
COMMENT ON COLUMN file_info.thumbnail_size IS '缩略图大小（字节）';
COMMENT ON COLUMN file_info.image_width IS '图片宽度（像素），仅图片文件有效';
COMMENT ON COLUMN file_info.image_height IS '图片高度（像素），仅图片文件有效';
COMMENT ON COLUMN file_info.status IS '文件状态：1-正常可用，0-已删除（软删除）';
COMMENT ON COLUMN file_info.create_time IS '创建时间';
COMMENT ON COLUMN file_info.update_time IS '更新时间';
COMMENT ON COLUMN file_info.create_by IS '创建者';
COMMENT ON COLUMN file_info.update_by IS '更新者';
COMMENT ON COLUMN file_info.remark IS '备注';

-- 3. 创建索引（这部分是正确的，无需修改）
CREATE INDEX idx_file_info_md5 ON file_info(md5_hash);
CREATE INDEX idx_file_info_path ON file_info(file_path);
CREATE INDEX idx_file_info_extension ON file_info(file_extension);
CREATE INDEX idx_file_info_create_time ON file_info(create_time);
CREATE INDEX idx_file_info_status ON file_info(status);

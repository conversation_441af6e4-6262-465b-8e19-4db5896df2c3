package com.ylzx.file.util;

import cn.hutool.core.util.StrUtil;
import com.ylzx.file.config.MinioProperties;
import io.minio.*;
import io.minio.http.Method;
import io.minio.messages.DeleteObject;
import io.minio.messages.Item;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * MinIO工具类
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class MinioUtil {

    private final MinioClient minioClient;
    private final MinioProperties minioProperties;

    /**
     * 检查存储桶是否存在
     */
    public boolean bucketExists(String bucketName) {
        try {
            return minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucketName).build());
        } catch (Exception e) {
            log.error("检查存储桶是否存在失败：{}", e.getMessage());
            return false;
        }
    }

    /**
     * 创建存储桶
     */
    public boolean createBucket(String bucketName) {
        try {
            if (!bucketExists(bucketName)) {
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
                log.info("创建存储桶成功：{}", bucketName);
                return true;
            }
            return true;
        } catch (Exception e) {
            log.error("创建存储桶失败：{}", e.getMessage());
            return false;
        }
    }

    /**
     * 上传文件
     */
    public boolean uploadFile(String bucketName, String objectName, MultipartFile file) {
        try {
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .stream(file.getInputStream(), file.getSize(), -1)
                            .contentType(file.getContentType())
                            .build()
            );
            log.info("文件上传成功：{}/{}", bucketName, objectName);
            return true;
        } catch (Exception e) {
            log.error("文件上传失败：{}", e.getMessage());
            return false;
        }
    }

    /**
     * 上传文件流
     */
    public boolean uploadFile(String bucketName, String objectName, InputStream inputStream, 
                             long size, String contentType) {
        try {
            minioClient.putObject(
                    PutObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .stream(inputStream, size, -1)
                            .contentType(contentType)
                            .build()
            );
            log.info("文件流上传成功：{}/{}", bucketName, objectName);
            return true;
        } catch (Exception e) {
            log.error("文件流上传失败：{}", e.getMessage());
            return false;
        }
    }

    /**
     * 下载文件
     */
    public InputStream downloadFile(String bucketName, String objectName) {
        try {
            return minioClient.getObject(
                    GetObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build()
            );
        } catch (Exception e) {
            log.error("文件下载失败：{}", e.getMessage());
            return null;
        }
    }

    /**
     * 删除文件
     */
    public boolean deleteFile(String bucketName, String objectName) {
        try {
            minioClient.removeObject(
                    RemoveObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build()
            );
            log.info("文件删除成功：{}/{}", bucketName, objectName);
            return true;
        } catch (Exception e) {
            log.error("文件删除失败：{}", e.getMessage());
            return false;
        }
    }

    /**
     * 批量删除文件
     */
    public boolean deleteFiles(String bucketName, List<String> objectNames) {
        try {
            List<DeleteObject> deleteObjects = new ArrayList<>();
            for (String objectName : objectNames) {
                deleteObjects.add(new DeleteObject(objectName));
            }
            
            Iterable<Result<io.minio.messages.DeleteError>> results = minioClient.removeObjects(
                    RemoveObjectsArgs.builder()
                            .bucket(bucketName)
                            .objects(deleteObjects)
                            .build()
            );

            for (Result<io.minio.messages.DeleteError> result : results) {
                io.minio.messages.DeleteError error = result.get();
                log.error("删除文件失败：{}", error.message());
            }
            
            log.info("批量删除文件成功，数量：{}", objectNames.size());
            return true;
        } catch (Exception e) {
            log.error("批量删除文件失败：{}", e.getMessage());
            return false;
        }
    }

    /**
     * 获取文件预签名URL
     */
    public String getPresignedUrl(String bucketName, String objectName) {
        try {
            return minioClient.getPresignedObjectUrl(
                    GetPresignedObjectUrlArgs.builder()
                            .method(Method.GET)
                            .bucket(bucketName)
                            .object(objectName)
                            .expiry(minioProperties.getPresignedUrlExpiry(), TimeUnit.SECONDS)
                            .build()
            );
        } catch (Exception e) {
            log.error("获取预签名URL失败：{}", e.getMessage());
            return null;
        }
    }

    /**
     * 列出文件夹中的文件
     */
    public List<String> listFiles(String bucketName, String prefix) {
        List<String> files = new ArrayList<>();
        try {
            Iterable<Result<Item>> results = minioClient.listObjects(
                    ListObjectsArgs.builder()
                            .bucket(bucketName)
                            .prefix(StrUtil.isNotBlank(prefix) ? prefix : "")
                            .recursive(false)
                            .build()
            );
            
            for (Result<Item> result : results) {
                Item item = result.get();
                if (!item.isDir()) {
                    files.add(item.objectName());
                }
            }
        } catch (Exception e) {
            log.error("列出文件失败：{}", e.getMessage());
        }
        return files;
    }

    /**
     * 复制文件
     */
    public boolean copyFile(String sourceBucket, String sourceObject, 
                           String targetBucket, String targetObject) {
        try {
            minioClient.copyObject(
                    CopyObjectArgs.builder()
                            .bucket(targetBucket)
                            .object(targetObject)
                            .source(CopySource.builder()
                                    .bucket(sourceBucket)
                                    .object(sourceObject)
                                    .build())
                            .build()
            );
            log.info("文件复制成功：{}/{} -> {}/{}", sourceBucket, sourceObject, targetBucket, targetObject);
            return true;
        } catch (Exception e) {
            log.error("文件复制失败：{}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查文件是否存在
     */
    public boolean fileExists(String bucketName, String objectName) {
        try {
            minioClient.statObject(
                    StatObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build()
            );
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取文件信息
     */
    public StatObjectResponse getFileInfo(String bucketName, String objectName) {
        try {
            return minioClient.statObject(
                    StatObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build()
            );
        } catch (Exception e) {
            log.error("获取文件信息失败：{}", e.getMessage());
            return null;
        }
    }
}

package com.ruoyi.web.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.utils.bean.BeanUtils;
import com.ruoyi.system.api.domain.SysFile;
import com.ruoyi.system.api.domain.vo.SysFileSign;
import com.ruoyi.system.api.domain.vo.SysFileSearchVO;
import com.ruoyi.web.domain.SysFileInfoSign;
import com.ruoyi.web.domain.vo.FileInfoSignVO;
import com.ruoyi.web.mapper.SysFileInfoSignMapper;
import com.ruoyi.web.service.FileInfoService;
import com.ruoyi.web.service.SysFileInfoSignService;
import com.ruoyi.web.service.SysFileService;
import com.ruoyi.web.service.TextImageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("sysFileInfoSignService")
@Slf4j
public class SysFileInfoSignServiceImpl extends ServiceImpl<SysFileInfoSignMapper, SysFileInfoSign>
        implements SysFileInfoSignService {
    
    @Autowired
    @Lazy
    private TextImageService textImageService;
    
    @Autowired
    private SysFileService sysFileService;
    
    @Autowired
    private FileInfoService fileInfoService;
    
    @Override
    public List<SysFileInfoSign> selectAssetBaseData(SysFileInfoSign sysFileInfoSign) {
        return baseMapper.selectAssetBaseData(sysFileInfoSign);
    }

    @Override
    public int countAssetBaseData(SysFileInfoSign sysFileInfoSign) {
        return baseMapper.countAssetBaseData(sysFileInfoSign);
    }

    /**
     * 生成签名图片并上传
     *
     * @param name 签名名称
     * @return 上传成功后的文件ID，失败则返回null
     */
    private SysFile generateAndUploadSignImage(String name) {
        try {
            // 1. 生成签名图片
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            // 使用默认参数生成图片
            int fontSize = 120;
            int width = 549;
            int height = 489;
            boolean forceDoubleLines = false;
            String textColor = "#000000";
            String backgroundColor = "transparent";
            String fontName = "楷体";
            boolean addShadow = false;
            
            textImageService.generateTextImage(
                name, fontSize, width, height, forceDoubleLines, 
                textColor, backgroundColor, fontName, addShadow, outputStream
            );
            
            // 2. 将生成的图片转换为MultipartFile并上传
            byte[] imageBytes = outputStream.toByteArray();
            
            // 创建文件参数
            SysFileSearchVO sysFileSearchVO = new SysFileSearchVO();
            sysFileSearchVO.setIsStatic(false);
            sysFileSearchVO.setPlatform("system");
            sysFileSearchVO.setStoragePath("/sign");
            
            // 3. 上传签名图片
            String fileName = name + ".png";
            MultipartFile file = new CustomMultipartFile(imageBytes, fileName, "image/png");
            R<SysFile> uploadResult = sysFileService.upload(file, sysFileSearchVO);
            
            if (uploadResult.getCode() == 200 && uploadResult.getData() != null) {
                // 4. 获取ownerId作为fileId
                return uploadResult.getData();
            } else {
                log.error("上传签名图片失败: {}", uploadResult.getMsg());
                return null;
            }
        } catch (IOException e) {
            log.error("生成或上传签名图片失败", e);
            return null;
        }
    }

    //实现接口定义的insert方法，接收SysFileInfoSign对象
    @Override
    public SysFileSign insert(SysFileInfoSign sysFileInfoSign) {
        String signName = sysFileInfoSign.getSignName();
        SysFileSign sysFileSignR = selectFileInfoWithSign(signName);
        if (sysFileSignR != null) {
            return sysFileSignR;
        }
        SysFileSign sysFileSign = new SysFileSign();
        SysFile sysFile = generateAndUploadSignImage(signName);
        if (sysFile != null) {
            sysFileInfoSign.setFileId(sysFile.getId());
            if (baseMapper.insert(sysFileInfoSign) > 0) {
                BeanUtils.copyProperties(sysFile, sysFileSign);
                sysFileSign.setSignName(signName);
                return sysFileSign;
            }
        }
        return null;
    }

    //实现接口定义的insert方法，接收String参数
    @Override
    public SysFileSign insert(String name) {
        SysFileSign sysFileSignR = selectFileInfoWithSign(name);
        if (sysFileSignR != null) {
            return sysFileSignR;
        }
        SysFileSign sysFileSign = new SysFileSign();
        SysFile sysFile = generateAndUploadSignImage(name);
        if (sysFile != null) {
            SysFileInfoSign sysFileInfoSign = new SysFileInfoSign();
            sysFileInfoSign.setSignName(name);
            sysFileInfoSign.setFileId(sysFile.getId());
            if (baseMapper.insert(sysFileInfoSign) > 0) {
                BeanUtils.copyProperties(sysFile, sysFileSign);
                sysFileSign.setSignName(name);
                return sysFileSign;
            }
        }
        return null;
    }

    @Override
    public int insertBatch(List<SysFileInfoSign> sysFileInfoSignList) {
        return baseMapper.insertBatch(sysFileInfoSignList);
    }

    @Override
    public int insertOrUpdateBatch(List<SysFileInfoSign> sysFileInfoSignList) {
        return baseMapper.insertOrUpdateBatch(sysFileInfoSignList);
    }

    @Override
    public SysFileSign selectFileInfoWithSign(String signName) {
        SysFileInfoSign sysFileInfoSign = baseMapper.selectFileInfoBySignName(signName);
        if (sysFileInfoSign == null || sysFileInfoSign.getFileId() == null) {
            return null;
        }
        SysFileSign sysFileSign = new SysFileSign();
        R<SysFile> sysFileR = sysFileService.getFileByFileId(sysFileInfoSign.getFileId());
        if (sysFileR.getCode() != 200 || sysFileR.getData() == null) {
            return null;
        }
        SysFile sysFile = sysFileR.getData();
        BeanUtils.copyProperties(sysFile, sysFileSign);
        sysFileSign.setSignName(signName);
        return sysFileSign;
    }

    @Override
    public List<SysFileSign> selectFileInfoWithSignBatch(List<String> list) {
        List<SysFileInfoSign> sysFileInfoSignList = baseMapper.selectFileInfoBySignNameList(list);
        if (sysFileInfoSignList == null || sysFileInfoSignList.isEmpty()) {
            return null;
        }
        Map<String,String> fileIdMap = new HashMap<>();
        for(SysFileInfoSign sysFileInfoSign : sysFileInfoSignList) {
            fileIdMap.put(sysFileInfoSign.getFileId(), sysFileInfoSign.getSignName());
        }
        List<SysFileSign> sysFileSignList = new ArrayList<>();
        R<List<SysFile>> sysFileR = sysFileService.findFilesByFileIdList(list);
        if (sysFileR.getCode() != 200 || sysFileR.getData() == null) {
            return null;
        }
        List<SysFile> sysFiles = sysFileR.getData();
        for(SysFile sysFile : sysFiles) {
            SysFileSign sysFileSign = new SysFileSign();
            BeanUtils.copyProperties(sysFile, sysFileSign);
            sysFileSign.setSignName(fileIdMap.get(sysFile.getId()));
            sysFileSignList.add(sysFileSign);
        }
        return sysFileSignList;
    }
    
    /**
     * 自定义MultipartFile实现，用于从字节数组创建文件
     */
    private static class CustomMultipartFile implements MultipartFile {
        private final byte[] content;
        private final String name;
        private final String contentType;

        public CustomMultipartFile(byte[] content, String name, String contentType) {
            this.content = content;
            this.name = name;
            this.contentType = contentType;
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public String getOriginalFilename() {
            return name;
        }

        @Override
        public String getContentType() {
            return contentType;
        }

        @Override
        public boolean isEmpty() {
            return content == null || content.length == 0;
        }

        @Override
        public long getSize() {
            return content.length;
        }

        @Override
        public byte[] getBytes() throws IOException {
            return content;
        }

        @Override
        public java.io.InputStream getInputStream() throws IOException {
            return new java.io.ByteArrayInputStream(content);
        }

        @Override
        public void transferTo(java.io.File dest) throws IOException, IllegalStateException {
            try (java.io.FileOutputStream fos = new java.io.FileOutputStream(dest)) {
                fos.write(content);
            }
        }
    }
}

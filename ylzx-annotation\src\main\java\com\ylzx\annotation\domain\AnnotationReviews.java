package com.ylzx.annotation.domain;

import java.io.Serial;
import java.time.LocalDateTime;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.ylzx.annotation.domain.enums.AnnotationStatus;
import com.ylzx.common.annotation.Excel;
import com.ylzx.common.core.domain.BaseEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 标注审核对象 annotation_reviews
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class AnnotationReviews extends BaseEntity {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 标注审核主键
     */
    private Long reviewId;

    /**
     * 标注主键
     */
    @Excel(name = "标注主键")
    private Long annotationId;

    /**
     * 标注人ID
     */
    @Excel(name = "标注人ID")
    private Long annotatorId;

    /**
     * 图片ID
     */
    @Excel(name = "图片ID")
    private Long imageId;

    /**
     * 审核状态 0:未标注 1:标注中 2:待审核 3:审核通过 4:审核不通过
     */
    @Excel(name = "审核状态", readConverterExp = "0=未标注,1=标注中,2=待审核,3=审核通过,4=审核不通过")
    @Builder.Default
    private AnnotationStatus status = AnnotationStatus.PENDING_REVIEW;


    /**
     * 审核人
     */
    private String auditor;

    /**
     * 审核时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime auditTime;

    /**
     * 审核意见
     */
    @Excel(name = "审核意见")
    private String comments;

    private Integer rejectNum;
}

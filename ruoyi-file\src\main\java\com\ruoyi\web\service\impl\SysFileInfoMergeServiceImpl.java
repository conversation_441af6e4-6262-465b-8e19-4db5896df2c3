package com.ruoyi.web.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.web.domain.SysFileInfoMerge;
import com.ruoyi.web.mapper.SysFileInfoMergeMapper;
import com.ruoyi.web.service.SysFileInfoMergeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysFileInfoMergeServiceImpl extends ServiceImpl<SysFileInfoMergeMapper, SysFileInfoMerge>
        implements SysFileInfoMergeService {
        
    @Autowired
    private SysFileInfoMergeMapper sysFileInfoMergeMapper;
    
    @Override
    public SysFileInfoMerge selectByOwnerIdAndSignName(String ownerId, String signName) {
        return sysFileInfoMergeMapper.selectByOwnerIdAndSignName(ownerId, signName);
    }
    
    @Override
    public List<SysFileInfoMerge> selectByOwnerId(String ownerId) {
        return sysFileInfoMergeMapper.selectByOwnerId(ownerId);
    }
    
    @Override
    public int insert(SysFileInfoMerge sysFileInfoMerge) {
        return sysFileInfoMergeMapper.insert(sysFileInfoMerge);
    }
    
    @Override
    public int updateByPrimaryKey(SysFileInfoMerge sysFileInfoMerge) {
        return sysFileInfoMergeMapper.updateByPrimaryKey(sysFileInfoMerge);
    }
}

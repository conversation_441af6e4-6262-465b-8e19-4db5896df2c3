package com.ruoyi.web.utils;

import cn.hutool.core.date.DateUtil;
import cn.hutool.jwt.JWTUtil;
import com.ruoyi.common.datasource.annotation.Slave;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class TokenUtil {

    public static String getToken() {
        try {
            // 设置过期时间为3小时
            Map<String, Object> map = new HashMap<>();
            map.put("expDate", DateUtil.offsetHour(new Date(), 3).toString("yyyy-MM-dd HH:mm:ss"));
            return JWTUtil.createToken(map, "ylzx@2025".getBytes());
        } catch (Exception e) {
            log.error("文件token创建异常：{}", e.getMessage());
        }
        return null;
    }
}

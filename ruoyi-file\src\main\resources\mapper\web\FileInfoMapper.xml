<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.web.mapper.FileInfoMapper">

    <update id="batchUpdateFileInfo" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE sys_file_info
            <set>
                <if test="item.ownerId != null">owner_id = #{item.ownerId},</if>
                <if test="item.platform != null">platform = #{item.platform},</if>
                <if test="item.storagePath != null">storage_path = #{item.storagePath},</if>
                <if test="item.fileName != null">file_name = #{item.fileName},</if>
                <if test="item.originalFilename != null">original_filename = #{item.originalFilename},</if>
                <if test="item.fileSize != null">file_size = #{item.fileSize},</if>
                <if test="item.ext != null">ext = #{item.ext},</if>
                <if test="item.url != null">url = #{item.url},</if>
                <if test="item.thumbUrl != null">thumb_url = #{item.thumbUrl},</if>
                <if test="item.contentType != null">content_type = #{item.contentType},</if>
                <if test="item.thFilename != null">th_filename = #{item.thFilename},</if>
                <if test="item.thSize != null">th_size = #{item.thSize},</if>
                <if test="item.isStatic != null">is_static = #{item.isStatic},</if>
                <if test="item.sort != null">sort = #{item.sort},</if>
                <if test="item.remark != null">remark = #{item.remark},</if>
                <if test="item.updateTime != null">update_time = #{item.updateTime},</if>
                <if test="item.updateBy != null">update_by = #{item.updateBy}</if>
            </set>
            WHERE id = #{item.id}
        </foreach>
    </update>

</mapper>

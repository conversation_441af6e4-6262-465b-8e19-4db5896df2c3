package com.ylzx.annotation.controller;

import java.util.List;
import java.util.Map;

import com.ylzx.annotation.service.AnnotationProjectCategoriesService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.ylzx.common.annotation.Log;
import com.ylzx.common.core.controller.BaseController;
import com.ylzx.common.core.domain.AjaxResult;
import com.ylzx.common.enums.BusinessType;
import com.ylzx.annotation.domain.AnnotationProjectCategories;
import com.ylzx.common.utils.poi.ExcelUtil;
import com.ylzx.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 标注项目分类关联Controller
 *
 * <AUTHOR>
 * @date 2025-07-28
 */
@Api(tags = "标注项目分类关联管理")
@RestController
@RequestMapping("/annotation/projectCategories")
public class AnnotationProjectCategoriesController extends BaseController
{
    @Resource
    private AnnotationProjectCategoriesService annotationProjectCategoriesService;

    /**
     * 查询标注项目分类关联列表
     */
    @ApiOperation("查询标注项目分类关联列表")
    @PreAuthorize("@ss.hasPermi('system:projectCategories:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ApiParam("查询条件") AnnotationProjectCategories annotationProjectCategories)
    {
        startPage();
        List<AnnotationProjectCategories> list = annotationProjectCategoriesService.list();
        return getDataTable(list);
    }

    /**
     * 导出标注项目分类关联列表
     */
    @PreAuthorize("@ss.hasPermi('system:projectCategories:export')")
    @Log(title = "标注项目分类关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AnnotationProjectCategories annotationProjectCategories)
    {
        List<AnnotationProjectCategories> list = annotationProjectCategoriesService.list();
        ExcelUtil<AnnotationProjectCategories> util = new ExcelUtil<AnnotationProjectCategories>(AnnotationProjectCategories.class);
        util.exportExcel(response, list, "标注项目分类关联数据");
    }

    /**
     * 获取标注项目分类关联详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:projectCategories:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(annotationProjectCategoriesService.getById(id));
    }

    /**
     * 根据项目ID查询关联的分类ID列表
     */
    @ApiOperation("根据项目ID查询关联的分类ID列表")
    @PreAuthorize("@ss.hasPermi('system:projectCategories:query')")
    @GetMapping("/project/{projectId}/categories")
    public AjaxResult getCategoriesByProjectId(@PathVariable("projectId") Long projectId)
    {
        List<Long> categoryIds = annotationProjectCategoriesService.selectCategoryIdsByProjectId(projectId);
        return success(categoryIds);
    }

    /**
     * 根据分类ID查询关联的项目ID列表
     */
    @ApiOperation("根据分类ID查询关联的项目ID列表")
    @PreAuthorize("@ss.hasPermi('system:projectCategories:query')")
    @GetMapping("/category/{categoryId}/projects")
    public AjaxResult getProjectsByCategoryId(@PathVariable("categoryId") Long categoryId)
    {
        List<Long> projectIds = annotationProjectCategoriesService.selectProjectIdsByCategoryId(categoryId);
        return success(projectIds);
    }

    /**
     * 检查项目和分类的关联关系是否存在
     */
    @ApiOperation("检查项目和分类的关联关系是否存在")
    @PreAuthorize("@ss.hasPermi('system:projectCategories:query')")
    @GetMapping("/exists")
    public AjaxResult existsRelation(@RequestParam("projectId") Long projectId, 
                                   @RequestParam("categoryId") Long categoryId)
    {
        boolean exists = annotationProjectCategoriesService.existsRelation(projectId, categoryId);
        return success(exists);
    }

    /**
     * 新增标注项目分类关联
     */
    @PreAuthorize("@ss.hasPermi('system:projectCategories:add')")
    @Log(title = "标注项目分类关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AnnotationProjectCategories annotationProjectCategories)
    {
        return toAjax(annotationProjectCategoriesService.save(annotationProjectCategories));
    }

    /**
     * 批量新增标注项目分类关联
     */
    @ApiOperation("批量新增标注项目分类关联")
    @PreAuthorize("@ss.hasPermi('system:projectCategories:add')")
    @Log(title = "标注项目分类关联", businessType = BusinessType.INSERT)
    @PostMapping("/batch")
    public AjaxResult addBatch(@RequestBody List<AnnotationProjectCategories> projectCategoriesList)
    {
        int result = annotationProjectCategoriesService.insertBatch(projectCategoriesList);
        return toAjax(result);
    }

    /**
     * 修改标注项目分类关联
     */
    @PreAuthorize("@ss.hasPermi('system:projectCategories:edit')")
    @Log(title = "标注项目分类关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AnnotationProjectCategories annotationProjectCategories)
    {
        return toAjax(annotationProjectCategoriesService.updateById(annotationProjectCategories));
    }

    /**
     * 更新项目的分类关联关系
     */
    @ApiOperation("更新项目的分类关联关系")
    @PreAuthorize("@ss.hasPermi('system:projectCategories:edit')")
    @Log(title = "标注项目分类关联", businessType = BusinessType.UPDATE)
    @PutMapping("/project/{projectId}/categories")
    public AjaxResult updateProjectCategories(@PathVariable("projectId") Long projectId, 
                                            @RequestBody List<Long> categoryIds)
    {
        boolean result = annotationProjectCategoriesService.updateProjectCategories(projectId, categoryIds);
        return toAjax(result);
    }

    /**
     * 批量更新多个项目的分类关联关系
     */
    @ApiOperation("批量更新多个项目的分类关联关系")
    @PreAuthorize("@ss.hasPermi('system:projectCategories:edit')")
    @Log(title = "标注项目分类关联", businessType = BusinessType.UPDATE)
    @PutMapping("/batch")
    public AjaxResult batchUpdateProjectCategories(@RequestBody Map<Long, List<Long>> projectCategoryMap)
    {
        int result = annotationProjectCategoriesService.batchUpdateProjectCategories(projectCategoryMap);
        return success("成功更新 " + result + " 个项目的分类关联关系");
    }

    /**
     * 删除标注项目分类关联
     */
    @PreAuthorize("@ss.hasPermi('system:projectCategories:remove')")
    @Log(title = "标注项目分类关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable("ids") Long[] ids)
    {
        return toAjax(annotationProjectCategoriesService.removeByIds(List.of(ids)));
    }

    /**
     * 根据项目ID删除所有关联关系
     */
    @ApiOperation("根据项目ID删除所有关联关系")
    @PreAuthorize("@ss.hasPermi('system:projectCategories:remove')")
    @Log(title = "标注项目分类关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/project/{projectId}")
    public AjaxResult removeByProjectId(@PathVariable("projectId") Long projectId)
    {
        int result = annotationProjectCategoriesService.deleteByProjectId(projectId);
        return toAjax(result);
    }

    /**
     * 根据分类ID删除所有关联关系
     */
    @ApiOperation("根据分类ID删除所有关联关系")
    @PreAuthorize("@ss.hasPermi('system:projectCategories:remove')")
    @Log(title = "标注项目分类关联", businessType = BusinessType.DELETE)
    @DeleteMapping("/category/{categoryId}")
    public AjaxResult removeByCategoryId(@PathVariable("categoryId") Long categoryId)
    {
        int result = annotationProjectCategoriesService.deleteByCategoryId(categoryId);
        return toAjax(result);
    }
}

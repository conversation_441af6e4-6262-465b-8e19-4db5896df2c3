# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# The generator used is:
set(CMAKE_DEPENDS_GENERATOR "MinGW Makefiles")

# The top level Makefile was generated from the following files:
set(CMAKE_MAKEFILE_DEPENDS
  "CMakeCache.txt"
  "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-dynamic/share/curl/CURLConfig.cmake"
  "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-dynamic/share/curl/CURLConfigVersion.cmake"
  "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-dynamic/share/curl/CURLTargets-debug.cmake"
  "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-dynamic/share/curl/CURLTargets-release.cmake"
  "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-dynamic/share/curl/CURLTargets.cmake"
  "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-dynamic/share/curl/vcpkg-cmake-wrapper.cmake"
  "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-dynamic/share/miniocpp/miniocpp-config.cmake"
  "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-dynamic/share/miniocpp/miniocpp-targets-debug.cmake"
  "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-dynamic/share/miniocpp/miniocpp-targets-release.cmake"
  "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-dynamic/share/miniocpp/miniocpp-targets.cmake"
  "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-dynamic/share/nlohmann_json/nlohmann_jsonConfig.cmake"
  "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-dynamic/share/nlohmann_json/nlohmann_jsonConfigVersion.cmake"
  "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-dynamic/share/nlohmann_json/nlohmann_jsonTargets.cmake"
  "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-dynamic/share/openssl/vcpkg-cmake-wrapper.cmake"
  "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-dynamic/share/pugixml/pugixml-config-version.cmake"
  "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-dynamic/share/pugixml/pugixml-config.cmake"
  "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-dynamic/share/pugixml/pugixml-targets-debug.cmake"
  "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-dynamic/share/pugixml/pugixml-targets-release.cmake"
  "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-dynamic/share/pugixml/pugixml-targets.cmake"
  "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-dynamic/share/unofficial-curlpp/unofficial-curlpp-config-debug.cmake"
  "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-dynamic/share/unofficial-curlpp/unofficial-curlpp-config-release.cmake"
  "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-dynamic/share/unofficial-curlpp/unofficial-curlpp-config.cmake"
  "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-dynamic/share/unofficial-inih/unofficial-inihConfig.cmake"
  "C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-dynamic/share/zlib/vcpkg-cmake-wrapper.cmake"
  "C:/msys64/home/<USER>/vcpkg/scripts/buildsystems/vcpkg.cmake"
  "C:/msys64/mingw64/lib/cmake/opencv4/OpenCVConfig-version.cmake"
  "C:/msys64/mingw64/lib/cmake/opencv4/OpenCVConfig.cmake"
  "C:/msys64/mingw64/lib/cmake/opencv4/OpenCVModules-release.cmake"
  "C:/msys64/mingw64/lib/cmake/opencv4/OpenCVModules.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeCCompiler.cmake.in"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeCCompilerABI.c"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeCInformation.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeCXXCompiler.cmake.in"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeCXXCompilerABI.cpp"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeCXXInformation.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeCommonLanguageInclude.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeCompilerIdDetection.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeDependentOption.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCXXCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerABI.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerId.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineCompilerSupport.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineRCCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeDetermineSystem.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeFindBinUtils.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeFindDependencyMacro.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeGenericSystem.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeInitializeConfigs.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeLanguageInformation.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeMinGWFindMake.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeParseImplicitIncludeInfo.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeParseImplicitLinkInfo.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeParseLibraryArchitecture.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeRCCompiler.cmake.in"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeRCInformation.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeSystem.cmake.in"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInformation.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeSystemSpecificInitialize.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeTestCCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeTestCXXCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeTestCompilerCommon.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/CMakeTestRCCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/ADSP-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/ARMCC-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/ARMClang-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/AppleClang-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Borland-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Bruce-C-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/CMakeCommonCompilerMacros.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Clang-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Clang-DetermineCompilerInternal.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Compaq-C-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Cray-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/CrayClang-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Diab-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Embarcadero-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Fujitsu-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/GHS-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-C-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-C.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-CXX.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU-FindBinUtils.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/GNU.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/HP-C-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/HP-CXX-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/IAR-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Intel-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/LCC-C-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/MSVC-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/NVHPC-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/NVIDIA-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/OrangeC-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/PGI-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/PathScale-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Renesas-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/SCO-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/SDCC-C-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/SunPro-C-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/TI-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/TIClang-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Tasking-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/Watcom-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/XL-C-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/XL-CXX-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/XLClang-C-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/zOS-C-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/FindOpenSSL.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/FindPackageHandleStandardArgs.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/FindPackageMessage.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/FindZLIB.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCLinkerInformation.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCXXLinkerInformation.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeCommonLinkerInformation.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeDetermineLinkerId.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeInspectCLinker.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Internal/CMakeInspectCXXLinker.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Internal/FeatureTesting.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Linker/GNU-C.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Linker/GNU-CXX.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Linker/GNU.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/GNU.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU-C.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU-CXX.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Linker/Windows-GNU.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-Determine-CXX.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU-C-ABI.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU-C.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU-CXX-ABI.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU-CXX.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-GNU.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-Initialize.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Windows-windres.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/Windows.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/Platform/WindowsPaths.cmake"
  "C:/msys64/mingw64/share/cmake/Modules/SelectLibraryConfigurations.cmake"
  "C:/work/code/java/data-annotation-platform/native/CMakeLists.txt"
  "CMakeFiles/4.1.0/CMakeCCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeRCCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeSystem.cmake"
  )

# The corresponding makefile is:
set(CMAKE_MAKEFILE_OUTPUTS
  "Makefile"
  "CMakeFiles/cmake.check_cache"
  )

# Byproducts of CMake generate step:
set(CMAKE_MAKEFILE_PRODUCTS
  "CMakeFiles/4.1.0/CMakeSystem.cmake"
  "CMakeFiles/4.1.0/CMakeCCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeRCCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeCCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeCCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeCXXCompiler.cmake"
  "CMakeFiles/4.1.0/CMakeCXXCompiler.cmake"
  "CMakeFiles/CMakeDirectoryInformation.cmake"
  )

# Dependency information for all targets:
set(CMAKE_DEPEND_INFO_FILES
  "CMakeFiles/dataset_export.dir/DependInfo.cmake"
  "CMakeFiles/generate_jni_headers.dir/DependInfo.cmake"
  "CMakeFiles/test_minio.dir/DependInfo.cmake"
  )

package com.ruoyi.web.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.web.domain.SysFileInfoSign;
import com.ruoyi.web.domain.vo.FileInfoSignVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 签名文件信息Mapper接口
 */
@Mapper
public interface SysFileInfoSignMapper extends BaseMapper<SysFileInfoSign> {

    /**
     * 查询签名信息基础数据
     *
     * @param request 请求参数
     * @return 签名信息列表
     */
    List<SysFileInfoSign> selectAssetBaseData(@Param("request") SysFileInfoSign request);

    /**
     * 计算签名信息数量
     *
     * @param request 请求参数
     * @return 签名信息数量
     */
    int countAssetBaseData(@Param("request") SysFileInfoSign request);
    
    /**
     * 批量插入签名信息
     *
     * @param entities 签名信息列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<SysFileInfoSign> entities);
    
    /**
     * 批量插入或更新签名信息
     *
     * @param entities 签名信息列表
     * @return 影响行数
     */
    int insertOrUpdateBatch(@Param("entities") List<SysFileInfoSign> entities);
    
    /**
     * 联合查询文件信息和签名信息
     * 
     * @param signName 签名名称
     * @return 文件和签名组合信息
     */
    FileInfoSignVO selectFileInfoWithSign(@Param("signName") String signName);
    
    /**
     * 批量联合查询文件信息和签名信息
     * 
     * @param list 文件ID列表
     * @return 文件和签名组合信息列表
     */
    List<FileInfoSignVO> selectFileInfoWithSignBatch(@Param("list") List<String> list);

    SysFileInfoSign selectFileInfoBySignName(@Param("signName") String signName);

    List<SysFileInfoSign> selectFileInfoBySignNameList(@Param("list") List<String> list);
}

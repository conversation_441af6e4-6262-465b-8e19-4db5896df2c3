package com.ylzx.file.service;

import com.ylzx.file.domain.dto.FileUploadRequest;
import com.ylzx.file.domain.dto.FileUploadResponse;
import com.ylzx.file.util.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.ActiveProfiles;

import jakarta.annotation.Resource;
import java.nio.charset.StandardCharsets;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 文件服务测试类
 * 
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest
@ActiveProfiles("test")
class FileServiceTest {

    @Resource
    private FileService fileService;

    @Test
    void testFileNameGeneration() {
        String originalFilename = "test.jpg";
        String generatedName = FileUtil.generateFileName(originalFilename);
        
        assertNotNull(generatedName);
        assertTrue(generatedName.endsWith(".jpg"));
        assertTrue(generatedName.contains("_"));
        
        log.info("原始文件名: {}, 生成文件名: {}", originalFilename, generatedName);
    }

    @Test
    void testFilePathGeneration() {
        String folderPath = "images/avatars";
        String fileName = "test.jpg";
        String filePath = FileUtil.generateFilePath(folderPath, fileName);
        
        assertNotNull(filePath);
        assertTrue(filePath.contains(folderPath));
        assertTrue(filePath.contains(fileName));
        
        log.info("文件夹路径: {}, 文件名: {}, 生成路径: {}", folderPath, fileName, filePath);
    }

    @Test
    void testFileExtensionExtraction() {
        assertEquals("jpg", FileUtil.getFileExtension("test.jpg"));
        assertEquals("pdf", FileUtil.getFileExtension("document.pdf"));
        assertEquals("", FileUtil.getFileExtension("noextension"));
        assertEquals("", FileUtil.getFileExtension(""));
        assertEquals("", FileUtil.getFileExtension(null));
    }

    @Test
    void testImageFileDetection() {
        assertTrue(FileUtil.isImageFile("test.jpg"));
        assertTrue(FileUtil.isImageFile("test.png"));
        assertTrue(FileUtil.isImageFile("test.gif"));
        assertFalse(FileUtil.isImageFile("test.pdf"));
        assertFalse(FileUtil.isImageFile("test.txt"));
    }

    @Test
    void testFileSizeFormatting() {
        assertEquals("1024 B", FileUtil.formatFileSize(1024));
        assertEquals("1.00 KB", FileUtil.formatFileSize(1024));
        assertEquals("1.00 MB", FileUtil.formatFileSize(1024 * 1024));
        assertEquals("1.00 GB", FileUtil.formatFileSize(1024L * 1024 * 1024));
    }

    @Test
    void testPathCleaning() {
        assertEquals("path/to/file", FileUtil.cleanPath("/path//to///file/"));
        assertEquals("path/to/file", FileUtil.cleanPath("\\path\\to\\file"));
        assertEquals("", FileUtil.cleanPath(""));
        assertEquals("", FileUtil.cleanPath(null));
    }

    // 注意：以下测试需要MinIO服务运行，可以根据实际情况启用
    /*
    @Test
    void testFileUpload() {
        // 创建模拟文件
        MockMultipartFile file = new MockMultipartFile(
            "file", 
            "test.txt", 
            "text/plain", 
            "Hello, World!".getBytes(StandardCharsets.UTF_8)
        );

        // 创建上传请求
        FileUploadRequest request = new FileUploadRequest();
        request.setFolderPath("test");
        request.setCreateBy("test-user");
        request.setRemark("测试文件上传");

        // 执行上传
        FileUploadResponse response = fileService.uploadFile(file, request);

        // 验证结果
        assertNotNull(response);
        assertNotNull(response.getId());
        assertEquals("test.txt", response.getOriginalFilename());
        assertEquals("text/plain", response.getContentType());
        assertEquals(13L, response.getFileSize());

        log.info("文件上传测试成功: {}", response);
    }
    */
}

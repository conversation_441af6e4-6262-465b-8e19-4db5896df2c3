package io.minio;

import lombok.SneakyThrows;

import java.net.URI;

public class MinioUtil {

    private static final String MINIO_PREFIX = "/mini";

    /**
     * 移除URL中的 /mini 前缀（用于签名前处理）
     */
    @SneakyThrows
    public static String removeMiniPrefix(String url) {
        URI uri = new URI(url);
        String path = uri.getPath();

        if (path == null || !path.startsWith(MINIO_PREFIX)) {
            return url; // 若路径不包含 /mini 前缀，直接返回原始URL
        }

        // 构建新路径（移除 /mini 前缀）
        String newPath = path.substring(MINIO_PREFIX.length());
        if (newPath.isEmpty()) {
            newPath = "/"; // 确保路径至少有一个斜杠
        }

        // 重构URL
        return new URI(
                uri.getScheme(),
                uri.getUserInfo(),
                uri.getHost(),
                uri.getPort(),
                newPath,
                uri.getQuery(),
                uri.getFragment()
        ).toString();
    }

    /**
     * 添加 /mini 前缀到URL（用于签名后处理）
     */
    @SneakyThrows
    public static String addMiniPrefix(String url) {
        URI uri = new URI(url);
        String path = uri.getPath();

        // 确保路径以斜杠开头
        if (path == null) {
            path = MINIO_PREFIX;
        } else if (!path.startsWith("/")) {
            path = "/" + path;
        }

        // 构建新路径（添加 /mini 前缀）
        String newPath;
        if (path.equals("/")) {
            newPath = MINIO_PREFIX;
        } else {
            newPath = MINIO_PREFIX + path;
        }

        // 重构URL
        return new URI(
                uri.getScheme(),
                uri.getUserInfo(),
                uri.getHost(),
                uri.getPort(),
                newPath,
                uri.getQuery(),
                uri.getFragment()
        ).toString();
    }
}

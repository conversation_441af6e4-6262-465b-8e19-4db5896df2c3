<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.web.mapper.SysFileInfoMergeMapper">
    <resultMap type="com.ruoyi.web.domain.SysFileInfoMerge" id="SysFileInfoMergeMap">
        <id property="ownerId" column="owner_id" jdbcType="VARCHAR"/>
        <id property="signName" column="sign_name" jdbcType="VARCHAR"/>
        <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 定义基础列列表 -->
    <sql id="Base_Column_List">
        owner_id,
        sign_name,
        file_name
    </sql>

    <!-- 定义公共的 WHERE 条件 -->
    <sql id="Common_Where_Clause">
        <where>
            <if test="request.ownerId != null and request.ownerId != ''">
                AND owner_id = #{request.ownerId}
            </if>
            <if test="request.signName != null and request.signName != ''">
                AND sign_name = #{request.signName}
            </if>
            <if test="request.fileName != null and request.fileName != ''">
                AND file_name = #{request.fileName}
            </if>
            <if test="request.ownerIdList != null and request.ownerIdList.size() > 0">
                AND owner_id IN
                <foreach collection="request.ownerIdList" item="ownerId" open="(" separator="," close=")">
                    #{ownerId}
                </foreach>
            </if>
            <if test="request.signNameList != null and request.signNameList.size() > 0">
                AND sign_name IN
                <foreach collection="request.signNameList" item="signName" open="(" separator="," close=")">
                    #{signName}
                </foreach>
            </if>
        </where>
    </sql>

    <!-- 查询总数 -->
    <select id="countMergeData" resultType="int">
        SELECT COUNT(1)
        FROM sys_file_info_merge
        <include refid="Common_Where_Clause"/>
    </select>

    <!-- 查询数据 -->
    <select id="selectMergeData" resultMap="SysFileInfoMergeMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_file_info_merge
        <include refid="Common_Where_Clause"/>
        ORDER BY sign_name ASC
        <if test="offset != null and pageSize != null">
            LIMIT #{offset}, #{pageSize}
        </if>
    </select>

    <!-- 根据主键查询 - 需要同时提供owner_id和sign_name -->
    <select id="selectByOwnerIdAndSignName" resultMap="SysFileInfoMergeMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_file_info_merge
        WHERE owner_id = #{ownerId} AND sign_name = #{signName}
    </select>

    <!-- 根据owner_id查询 - 现在返回可能有多条记录 -->
    <select id="selectByOwnerId" resultMap="SysFileInfoMergeMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_file_info_merge
        WHERE owner_id = #{ownerId}
    </select>

    <!-- 根据signName查询 -->
    <select id="selectBySignName" resultMap="SysFileInfoMergeMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM sys_file_info_merge
        WHERE sign_name = #{signName}
    </select>

    <!-- 单条插入 -->
    <insert id="insert" parameterType="com.ruoyi.web.domain.SysFileInfoMerge">
        INSERT INTO sys_file_info_merge(
            owner_id,
            sign_name,
            file_name
        ) VALUES (
            #{ownerId},
            #{signName},
            #{fileName}
        )
    </insert>

    <!-- 批量插入 -->
    <insert id="insertBatch">
        INSERT INTO sys_file_info_merge(owner_id, sign_name, file_name)
        VALUES
        <foreach collection="list" item="entity" separator=",">
            (#{entity.ownerId}, #{entity.signName}, #{entity.fileName})
        </foreach>
    </insert>

    <!-- 批量插入或按联合主键更新 -->
    <insert id="insertOrUpdateBatch">
        INSERT INTO sys_file_info_merge(owner_id, sign_name, file_name)
        VALUES
        <foreach collection="list" item="entity" separator=",">
            (#{entity.ownerId}, #{entity.signName}, #{entity.fileName})
        </foreach>
        ON DUPLICATE KEY UPDATE
        file_name = VALUES(file_name)
    </insert>

    <!-- 根据联合主键更新 -->
    <update id="updateByPrimaryKey" parameterType="com.ruoyi.web.domain.SysFileInfoMerge">
        UPDATE sys_file_info_merge
        SET file_name = #{fileName}
        WHERE owner_id = #{ownerId} AND sign_name = #{signName}
    </update>

    <!-- 根据owner_id和sign_name删除 -->
    <delete id="deleteByPrimaryKey">
        DELETE FROM sys_file_info_merge
        WHERE owner_id = #{ownerId} AND sign_name = #{signName}
    </delete>

    <!-- 根据owner_id删除所有关联记录 -->
    <delete id="deleteByOwnerId">
        DELETE FROM sys_file_info_merge
        WHERE owner_id = #{ownerId}
    </delete>

    <!-- 批量删除 -->
    <delete id="deleteBatchByOwnerIds">
        DELETE FROM sys_file_info_merge
        WHERE owner_id IN
        <foreach collection="list" item="ownerId" open="(" separator="," close=")">
            #{ownerId}
        </foreach>
    </delete>
</mapper>

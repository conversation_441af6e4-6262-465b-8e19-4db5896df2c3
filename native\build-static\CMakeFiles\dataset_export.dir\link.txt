C:\msys64\mingw64\bin\cmake.exe -E rm -f CMakeFiles\dataset_export.dir/objects.a
C:\msys64\mingw64\bin\ar.exe qc CMakeFiles\dataset_export.dir/objects.a @CMakeFiles\dataset_export.dir\objects1.rsp
C:\msys64\mingw64\bin\g++.exe  -Wall -Wextra -O2 -D_WIN32_WINNT=0x0601 -static-libgcc -static-libstdc++ -Wall -Wextra -O2 -O3 -DNDEBUG -shared -static-libgcc -static-libstdc++ -Wl,-Bstatic -static-libgcc -static-libstdc++ -Wl,-Bstatic -o bin\dataset_export.dll -Wl,--out-implib,libdataset_export.dll.a -Wl,--major-image-version,0,--minor-image-version,0 -Wl,--whole-archive CMakeFiles\dataset_export.dir/objects.a -Wl,--no-whole-archive @CMakeFiles\dataset_export.dir\linkLibs.rsp

package com.ylzx.file.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

/**
 * 文件上传请求DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "文件上传请求")
public class FileUploadRequest {

    /**
     * 文件夹路径（可选）
     * 如：images/avatars、documents/reports
     */
    @Schema(description = "文件夹路径，如：images/avatars", example = "images/avatars")
    @Pattern(regexp = "^[a-zA-Z0-9/_-]*$", message = "文件夹路径只能包含字母、数字、下划线、中划线和斜杠")
    private String folderPath;

    /**
     * 是否生成缩略图
     */
    @Schema(description = "是否生成缩略图（仅对图片文件有效）", example = "true")
    private Boolean generateThumbnail = false;

    /**
     * 缩略图宽度
     */
    @Schema(description = "缩略图宽度（像素）", example = "200")
    private Integer thumbnailWidth = 200;

    /**
     * 缩略图高度
     */
    @Schema(description = "缩略图高度（像素）", example = "200")
    private Integer thumbnailHeight = 200;

    /**
     * 是否覆盖同名文件
     */
    @Schema(description = "是否覆盖同名文件", example = "false")
    private Boolean overwrite = false;

    /**
     * 备注信息
     */
    @Schema(description = "备注信息", example = "用户头像")
    private String remark;

    /**
     * 创建者
     */
    @Schema(description = "创建者", example = "admin")
    private String createBy;
}

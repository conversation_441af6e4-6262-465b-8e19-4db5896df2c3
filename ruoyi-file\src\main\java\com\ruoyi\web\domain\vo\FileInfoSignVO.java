package com.ruoyi.web.domain.vo;

import com.ruoyi.web.domain.FileInfoEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 文件信息和签名信息的组合视图对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class FileInfoSignVO extends FileInfoEntity {
    
    /**
     * 签名名称
     */
    private String signName;
    
    /**
     * 从FileInfoEntity和签名名称创建组合对象
     */
    public FileInfoSignVO(FileInfoEntity fileInfo, String signName) {
        // 复制FileInfoEntity的所有属性
        if (fileInfo != null) {
            this.setId(fileInfo.getId());
            this.setOwnerId(fileInfo.getOwnerId());
            this.setPlatform(fileInfo.getPlatform());
            this.setStoragePath(fileInfo.getStoragePath());
            this.setFileName(fileInfo.getFileName());
            this.setOriginalFilename(fileInfo.getOriginalFilename());
            this.setFileSize(fileInfo.getFileSize());
            this.setExt(fileInfo.getExt());
            this.setUrl(fileInfo.getUrl());
            this.setThumbUrl(fileInfo.getThumbUrl());
            this.setContentType(fileInfo.getContentType());
            this.setThFilename(fileInfo.getThFilename());
            this.setThSize(fileInfo.getThSize());
            this.setIsStatic(fileInfo.getIsStatic());
            this.setSort(fileInfo.getSort());
            this.setRemark(fileInfo.getRemark());
            this.setDelFlag(fileInfo.getDelFlag());
            // 设置BaseTableEntity的其他属性
            this.setCreateBy(fileInfo.getCreateBy());
            this.setCreateTime(fileInfo.getCreateTime());
            this.setUpdateBy(fileInfo.getUpdateBy());
            this.setUpdateTime(fileInfo.getUpdateTime());
        }
        
        // 设置签名名称
        this.signName = signName;
    }
} 
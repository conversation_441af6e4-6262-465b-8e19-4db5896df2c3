package com.ylzx.annotation.domain;

import java.io.Serial;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ylzx.common.annotation.Excel;
import com.ylzx.common.core.domain.BaseEntity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 标注图片来源对象 annotation_image_sources
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@TableName("annotation_image_sources") // 表名注解
public class AnnotationImageSources extends BaseEntity
{
    @Serial
    private static final long serialVersionUID = 1L;

    /** 标注图片来源主键 */
    @TableId(type = IdType.AUTO)
    private Long sourceId;

    /** 图片来源名称 */
    @Excel(name = "图片来源名称")
    private String sourceName;

    /** 上传类型 */
    @Excel(name = "上传类型")
    private String uploadType;

    /** 上传用户主键 */
    @Excel(name = "上传用户主键")
    private Long uploadedByUserId;

    /** 上传时间 */
    @Excel(name = "上传时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime uploadedAt;

    /** 元数据 */
    @Excel(name = "元数据")
    private String metadata;

    /** 文件ID（MinIO中的文件标识） */
    @Excel(name = "文件ID")
    private String fileId;

    /** 内容哈希 */
    @Excel(name = "内容哈希")
    private String contentHash;

}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylzx.annotation.mapper.AnnotationCategoriesSourcesMapper">
    
    <resultMap type="AnnotationCategoriesSources" id="AnnotationCategoriesSourcesResult">
        <result property="id"                    column="id"                    />
        <result property="categoryId"            column="category_id"           />
        <result property="sourceId"              column="source_id"             />
        <result property="associatedAt"          column="associated_at"         />
        <result property="associatedByUserId"    column="associated_by_user_id" />
        <result property="status"                column="status"                />
        <result property="remark"                column="remark"                />
        <result property="createBy"              column="create_by"             />
        <result property="createTime"            column="create_time"           />
        <result property="updateBy"              column="update_by"             />
        <result property="updateTime"            column="update_time"           />
    </resultMap>

    <sql id="selectAnnotationCategoriesSourcesVo">
        select id, category_id, source_id, associated_at, associated_by_user_id, status, remark, create_by, create_time, update_by, update_time from annotation_categories_sources
    </sql>

    <select id="selectAnnotationCategoriesSourcesList" parameterType="AnnotationCategoriesSources" resultMap="AnnotationCategoriesSourcesResult">
        <include refid="selectAnnotationCategoriesSourcesVo"/>
        <where>  
            <if test="categoryId != null "> and category_id = #{categoryId}</if>
            <if test="sourceId != null "> and source_id = #{sourceId}</if>
            <if test="associatedAt != null "> and associated_at = #{associatedAt}</if>
            <if test="associatedByUserId != null "> and associated_by_user_id = #{associatedByUserId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>

    <select id="countByCategoryIdAndSourceId" resultType="int">
        select count(1) from annotation_categories_sources 
        where category_id = #{categoryId} and source_id = #{sourceId}
    </select>

    <select id="selectSourceIdsByCategoryId" resultType="Long">
        select source_id from annotation_categories_sources 
        where category_id = #{categoryId}
        order by associated_at desc
    </select>

<!--    <select id="selectSourceIdsByCategoryId" resultType="Long">-->
<!--        select source_id from annotation_categories_sources-->
<!--        where category_id = #{categoryId} and status = 'active'-->
<!--        order by associated_at desc-->
<!--    </select>-->

    <select id="selectCategoryIdsBySourceId" resultType="Long">
        select category_id from annotation_categories_sources 
        where source_id = #{sourceId} and status = 'active'
        order by associated_at desc
    </select>

    <insert id="insertBatch" parameterType="java.util.List">
        insert into annotation_categories_sources(category_id, source_id, associated_at, associated_by_user_id, status, remark, create_by, create_time)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.categoryId}, #{item.sourceId}, #{item.associatedAt}, #{item.associatedByUserId}, #{item.status}, #{item.remark}, #{item.createBy}, #{item.createTime})
        </foreach>
    </insert>

    <delete id="deleteByCategoryId">
        delete from annotation_categories_sources where category_id = #{categoryId}
    </delete>

    <delete id="deleteBySourceId">
        delete from annotation_categories_sources where source_id = #{sourceId}
    </delete>
    <delete id="deleteByWrapper">

        delete from annotation_categories_sources   ${ew.customSqlSegment}
    </delete>

</mapper>

package com.ylzx.annotation.service;

import java.util.List;
import com.ylzx.annotation.domain.AnnotationCategoriesImages;
import com.ylzx.annotation.domain.vo.ImageWithAnnotationsVo;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 标注项目图片Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface AnnotationCategoriesImagesService extends IService<AnnotationCategoriesImages>
{
    /**
     * 查询标注项目图片
     * 
     * @param categoryId 标注项目图片主键
     * @return 标注项目图片
     */
    AnnotationCategoriesImages selectAnnotationCategoriesImagesByCategoryId(Long categoryId);

    /**
     * 查询标注项目图片列表
     * 
     * @param annotationCategoriesImages 标注项目图片
     * @return 标注项目图片集合
     */
    List<AnnotationCategoriesImages> selectAnnotationCategoriesImagesList(AnnotationCategoriesImages annotationCategoriesImages);

    /**
     * 新增标注项目图片
     * 
     * @param annotationCategoriesImages 标注项目图片
     * @return 结果
     */
    int insertAnnotationCategoriesImages(AnnotationCategoriesImages annotationCategoriesImages);

    /**
     * 修改标注项目图片
     * 
     * @param annotationCategoriesImages 标注项目图片
     * @return 结果
     */
    int updateAnnotationCategoriesImages(AnnotationCategoriesImages annotationCategoriesImages);

    /**
     * 批量删除标注项目图片
     * 
     * @param categoryIds 需要删除的标注项目图片主键集合
     * @return 结果
     */
    int deleteAnnotationCategoriesImagesByCategoryIds(Long[] categoryIds);

    /**
     * 删除标注项目图片信息
     * 
     * @param categoryId 标注项目图片主键
     * @return 结果
     */
    int deleteAnnotationCategoriesImagesByCategoryId(Long categoryId);

    int deleteAnnotationCategoriesImagesById(Long id);

    /**
     * 关联图片源中的所有图片到指定的标注分类中
     *
     * @param sourceId 图片来源ID
     * @param categoryId 标注分类ID
     * @return 关联的图片数量
     */
    int associateSourceImagesToCategory(Long sourceId, Long categoryId);

    /**
     * 为用户领取指定数量的标注任务。
     *
     * @param categoryId 类目ID
     * @param userId     用户ID
     * @param limit      需要领取的数量
     * @return 领取到的任务列表，如果没有可领取的则返回空列表。
     */
    public List<AnnotationCategoriesImages> claimImagesForAnnotation(Long categoryId, Long userId, int limit);

    /**
     * 为用户领取指定数量的标注任务，返回图片和标注信息的组合。
     *
     * @param categoryId 类目ID
     * @param userId     用户ID
     * @param limit      需要领取的数量
     * @return 领取到的任务列表（包含图片和标注信息），如果没有可领取的则返回空列表。
     */
    List<ImageWithAnnotationsVo> claimImagesForAnnotationWithDetails(Long categoryId, Long userId, int limit);

    /**
     * 获取当前用户在指定分类下未完成的标注任务
     *
     * @param categoryId 分类ID
     * @return 任务列表
     */
    List<AnnotationCategoriesImages> getMyUnfinishedTasks(Long categoryId);

    /**
     * 获取当前用户的任务，支持多种状态和可选分类ID
     *
     * @param categoryId 分类ID，可为null表示查询所有分类
     * @param statuses 状态列表
     * @return 任务列表
     */
    List<AnnotationCategoriesImages> getMyTasksByStatuses(Long categoryId, List<com.ylzx.annotation.domain.enums.AnnotationStatus> statuses);

    /**
     * 释放当前用户在指定分类下的所有进行中的标注任务
     *
     * @param categoryId 分类ID
     * @return 释放的任务数量
     */
    int releaseMyTasks(Long categoryId);

    /**
     * 释放当前用户的标注任务，支持可选分类ID
     *
     * @param categoryId 分类ID，可为null表示释放所有分类下的任务
     * @return 释放的任务数量
     */
    int releaseMyTasksOptional(Long categoryId);

    /**
     * 根据状态列表查询用户的任务，返回图片和标注信息的组合。
     * @param categoryId 类目ID，如果为null则查询所有类目
     * @param statuses 状态列表
     * @return 任务列表（包含图片和标注信息）
     */
    List<ImageWithAnnotationsVo> getMyTasksByStatusesWithDetails(Long categoryId, List<com.ylzx.annotation.domain.enums.AnnotationStatus> statuses);

    /**
     * 获取单个任务的详细信息（图片+标注）
     * @param taskId 任务ID
     * @return 任务详细信息
     */
    ImageWithAnnotationsVo getTaskWithDetails(Long taskId);
}

package com.ylzx.annotation.service.impl;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylzx.annotation.domain.AnnotationCategories;
import com.ylzx.annotation.mapper.AnnotationCategoriesMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ylzx.annotation.mapper.AnnotationLabelsMapper;
import com.ylzx.annotation.domain.AnnotationLabels;
import com.ylzx.annotation.service.AnnotationLabelsService;

/**
 * 标注标签Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
public class AnnotationLabelsServiceImpl extends ServiceImpl<AnnotationLabelsMapper,AnnotationLabels> implements AnnotationLabelsService
{
    @Autowired
    private AnnotationLabelsMapper annotationLabelsMapper;


    @Autowired
    private AnnotationCategoriesMapper annotationCategoriesMapper;

    /**
     * 查询标注标签
     * 
     * @param labelId 标注标签主键
     * @return 标注标签
     */
    @Override
    public AnnotationLabels selectAnnotationLabelsByLabelId(Long labelId)
    {
        return annotationLabelsMapper.selectAnnotationLabelsByLabelId(labelId);
    }

    /**
     * 查询标注标签列表
     * 
     * @param annotationLabels 标注标签
     * @return 标注标签集合
     */
    @Override
    public List<AnnotationLabels> selectAnnotationLabelsList(AnnotationLabels annotationLabels)
    {
        List<AnnotationLabels> annotationLabelsList = annotationLabelsMapper.selectAnnotationLabelsList(annotationLabels);

        // 查询分类名称
        List<AnnotationCategories> annotationCategoriesList = annotationCategoriesMapper.selectAnnotationCategoriesList(new AnnotationCategories());
        for(AnnotationLabels label : annotationLabelsList){
            for(AnnotationCategories category : annotationCategoriesList){
                if(label.getCategoryId().equals(category.getCategoryId())){
                    label.setCategoryName(category.getName());
                }
            }
        }
        return annotationLabelsList;
    }

    /**
     * 新增标注标签
     * 
     * @param annotationLabels 标注标签
     * @return 结果
     */
    @Override
    public int insertAnnotationLabels(AnnotationLabels annotationLabels)
    {
        return annotationLabelsMapper.insertAnnotationLabels(annotationLabels);
    }

    /**
     * 修改标注标签
     * 
     * @param annotationLabels 标注标签
     * @return 结果
     */
    @Override
    public int updateAnnotationLabels(AnnotationLabels annotationLabels)
    {
        return annotationLabelsMapper.updateAnnotationLabels(annotationLabels);
    }

    /**
     * 批量删除标注标签
     * 
     * @param labelIds 需要删除的标注标签主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationLabelsByLabelIds(Long[] labelIds)
    {
        return annotationLabelsMapper.deleteAnnotationLabelsByLabelIds(labelIds);
    }

    /**
     * 删除标注标签信息
     * 
     * @param labelId 标注标签主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationLabelsByLabelId(Long labelId)
    {
        return annotationLabelsMapper.deleteAnnotationLabelsByLabelId(labelId);
    }
}

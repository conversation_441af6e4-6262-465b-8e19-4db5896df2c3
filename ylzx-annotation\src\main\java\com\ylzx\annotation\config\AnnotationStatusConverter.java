package com.ylzx.annotation.config;

import com.ylzx.annotation.domain.enums.AnnotationStatus;
import org.springframework.core.convert.converter.Converter;
import org.springframework.stereotype.Component;

@Component
public class AnnotationStatusConverter implements Converter<String, AnnotationStatus> {
    @Override
    public AnnotationStatus convert(String source) {
        return AnnotationStatus.fromCode(source);
    }
}
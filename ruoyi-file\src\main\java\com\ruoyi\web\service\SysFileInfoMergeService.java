package com.ruoyi.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.web.domain.SysFileInfoMerge;

import java.util.List;

public interface SysFileInfoMergeService extends IService<SysFileInfoMerge> {
    
    /**
     * 根据ownerId和signName查询合并文件信息
     *
     * @param ownerId 拥有者ID
     * @param signName 签名名称
     * @return 合并文件信息
     */
    SysFileInfoMerge selectByOwnerIdAndSignName(String ownerId, String signName);
    
    /**
     * 根据ownerId查询合并文件信息
     *
     * @param ownerId 拥有者ID
     * @return 合并文件信息列表
     */
    List<SysFileInfoMerge> selectByOwnerId(String ownerId);
    
    /**
     * 插入合并文件信息
     *
     * @param sysFileInfoMerge 合并文件信息
     * @return 影响行数
     */
    int insert(SysFileInfoMerge sysFileInfoMerge);
    
    /**
     * 更新合并文件信息
     *
     * @param sysFileInfoMerge 合并文件信息
     * @return 影响行数
     */
    int updateByPrimaryKey(SysFileInfoMerge sysFileInfoMerge);
}

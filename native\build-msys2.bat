@echo off
echo ========================================
echo Building with MSYS2 MinGW and MinIO
echo ========================================

REM 检查MSYS2是否安装
set MSYS2_ROOT=C:\msys64
if not exist "%MSYS2_ROOT%" (
    echo Error: MSYS2 not found at %MSYS2_ROOT%
    echo Please install MSYS2 or update the path
    pause
    exit /b 1
)

REM 检查vcpkg路径
if "%VCPKG_ROOT%"=="" (
    if exist "%USERPROFILE%\vcpkg" (
        set VCPKG_ROOT=%USERPROFILE%\vcpkg
    ) else if exist "C:\vcpkg" (
        set VCPKG_ROOT=C:\vcpkg
    ) else (
        echo Error: VCPKG_ROOT not set and vcpkg not found in default locations
        echo Please set VCPKG_ROOT environment variable
        echo Example: set VCPKG_ROOT=C:\path\to\your\vcpkg
        pause
        exit /b 1
    )
)

echo Using vcpkg at: %VCPKG_ROOT%

REM 检查vcpkg是否存在
if not exist "%VCPKG_ROOT%\vcpkg.exe" (
    echo Error: vcpkg.exe not found at %VCPKG_ROOT%
    pause
    exit /b 1
)

REM 启动MSYS2 MinGW64环境并运行构建脚本
echo Starting MSYS2 MinGW64 environment...
echo.
echo This will open MSYS2 terminal. In that terminal, run:
echo   cd /c/work/code/java/data-annotation-platform/native
echo   export VCPKG_ROOT=%VCPKG_ROOT%
echo   ./build-simple.sh
echo.

REM 转换Windows路径到MSYS2路径
set CURRENT_DIR=%CD%
set MSYS2_PATH=%CURRENT_DIR:\=/%
set MSYS2_PATH=%MSYS2_PATH:C:=/c%

REM 启动MSYS2 MinGW64
"%MSYS2_ROOT%\msys2_shell.cmd" -mingw64 -here -c "export VCPKG_ROOT='%VCPKG_ROOT%'; cd '%MSYS2_PATH%'; echo 'Current directory: $(pwd)'; echo 'VCPKG_ROOT: $VCPKG_ROOT'; echo 'Run: ./build-simple.sh'; bash"

echo.
echo MSYS2 terminal closed.
echo If build was successful, the library should be in the build directory.
pause

package com.ylzx.annotation.mapper;

import java.util.List;
import com.ylzx.annotation.domain.AnnotationAnnotations;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * 标注Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface AnnotationAnnotationsMapper extends BaseMapper<AnnotationAnnotations>
{
    /**
     * 查询标注
     * 
     * @param annotationId 标注主键
     * @return 标注
     */
    public AnnotationAnnotations selectAnnotationAnnotationsByAnnotationId(Long annotationId);

    /**
     * 查询标注列表
     * 
     * @param annotationAnnotations 标注
     * @return 标注集合
     */
    public List<AnnotationAnnotations> selectAnnotationAnnotationsList(AnnotationAnnotations annotationAnnotations);

    /**
     * 新增标注
     * 
     * @param annotationAnnotations 标注
     * @return 结果
     */
    public int insertAnnotationAnnotations(AnnotationAnnotations annotationAnnotations);



    /**
     * 新增标注*(批量)
     *
     * @param annotationAnnotations 标注
     * @return 结果
     */
    public int insertAnnotationAnnotationsBatch(List<AnnotationAnnotations> annotationAnnotations);

    /**
     * 修改标注
     * 
     * @param annotationAnnotations 标注
     * @return 结果
     */
    public int updateAnnotationAnnotations(AnnotationAnnotations annotationAnnotations);

    /**
     * 修改标注状态
     *
     * @param annotationAnnotations 标注
     * @return 结果
     */
    public int updateAnnotationAnnotationsStatus(AnnotationAnnotations annotationAnnotations);

    /**
     * 批量逻辑删除标注
     *
     * @param annotationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int logicDeleteAnnotationAnnotationsByAnnotationIds(@Param("annotationIds") Long[] annotationIds);

    /**
     * 逻辑删除标注
     *
     * @param annotationId 标注主键
     * @return 结果
     */
    public int logicDeleteAnnotationAnnotationsByAnnotationId(Long annotationId);

    /**
     * 【SQL优化】根据单个标注ID，一次性查询同一个图片和类别下的所有标注的整体状态
     *
     * @param annotationId 标注ID
     * @return 包含 categoryId, imageId, 和整体 status 的标注实体
     */
    public AnnotationAnnotations checkOverallStatusInSql(@Param("annotationId") Long annotationId);

    /**
     *
     * @param annotationAnnotations
     * @return
     */

    int deleteAnnotationAnnotations(AnnotationAnnotations annotationAnnotations);
}

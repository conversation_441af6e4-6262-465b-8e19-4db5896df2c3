package com.ruoyi.web.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

@TableName("sys_file_info_sign")
@EqualsAndHashCode(callSuper = false)
@ToString
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SysFileInfoSign implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId("sign_name")
    private String signName;
    
    @TableField("file_id")
    private String fileId;

    @TableField(exist = false)
    private List<String> fileIdList;

    @TableField(exist = false)
    private List<String> signNameList;
}

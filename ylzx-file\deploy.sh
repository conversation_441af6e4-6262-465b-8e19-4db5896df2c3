#!/bin/bash

# YLZX文件服务部署脚本

# 设置变量
APP_NAME="ylzx-file"
JAR_NAME="${APP_NAME}.jar"
PID_FILE="${APP_NAME}.pid"
LOG_FILE="logs/${APP_NAME}.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

print_error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

# 检查Java环境
check_java() {
    if ! command -v java &> /dev/null; then
        print_error "Java未安装或未配置到PATH中"
        exit 1
    fi
    
    JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}')
    print_message "Java版本: $JAVA_VERSION"
}

# 检查应用是否运行
is_running() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null 2>&1; then
            return 0
        else
            rm -f "$PID_FILE"
            return 1
        fi
    fi
    return 1
}

# 启动应用
start() {
    if is_running; then
        print_warning "应用已经在运行中 (PID: $(cat $PID_FILE))"
        return 1
    fi
    
    print_message "正在启动 $APP_NAME..."
    
    # 创建日志目录
    mkdir -p logs
    
    # 启动应用
    nohup java -jar "$JAR_NAME" > "$LOG_FILE" 2>&1 &
    PID=$!
    echo $PID > "$PID_FILE"
    
    # 等待启动
    sleep 3
    
    if is_running; then
        print_message "$APP_NAME 启动成功 (PID: $PID)"
        print_message "日志文件: $LOG_FILE"
        print_message "API文档: http://localhost:8083/file/swagger-ui.html"
    else
        print_error "$APP_NAME 启动失败"
        return 1
    fi
}

# 停止应用
stop() {
    if ! is_running; then
        print_warning "应用未运行"
        return 1
    fi
    
    PID=$(cat "$PID_FILE")
    print_message "正在停止 $APP_NAME (PID: $PID)..."
    
    kill "$PID"
    
    # 等待进程结束
    for i in {1..30}; do
        if ! ps -p "$PID" > /dev/null 2>&1; then
            rm -f "$PID_FILE"
            print_message "$APP_NAME 已停止"
            return 0
        fi
        sleep 1
    done
    
    # 强制杀死进程
    print_warning "强制停止 $APP_NAME..."
    kill -9 "$PID"
    rm -f "$PID_FILE"
    print_message "$APP_NAME 已强制停止"
}

# 重启应用
restart() {
    print_message "正在重启 $APP_NAME..."
    stop
    sleep 2
    start
}

# 查看状态
status() {
    if is_running; then
        PID=$(cat "$PID_FILE")
        print_message "$APP_NAME 正在运行 (PID: $PID)"
        
        # 显示内存使用情况
        if command -v ps &> /dev/null; then
            MEMORY=$(ps -p "$PID" -o rss= | awk '{print $1/1024 " MB"}')
            print_message "内存使用: $MEMORY"
        fi
    else
        print_warning "$APP_NAME 未运行"
    fi
}

# 查看日志
logs() {
    if [ -f "$LOG_FILE" ]; then
        tail -f "$LOG_FILE"
    else
        print_error "日志文件不存在: $LOG_FILE"
    fi
}

# 构建应用
build() {
    print_message "正在构建 $APP_NAME..."
    
    if command -v mvn &> /dev/null; then
        mvn clean package -DskipTests
        if [ $? -eq 0 ]; then
            print_message "构建成功"
            # 复制jar文件
            cp target/"$JAR_NAME" .
        else
            print_error "构建失败"
            exit 1
        fi
    else
        print_error "Maven未安装或未配置到PATH中"
        exit 1
    fi
}

# 主函数
main() {
    case "$1" in
        start)
            check_java
            start
            ;;
        stop)
            stop
            ;;
        restart)
            check_java
            restart
            ;;
        status)
            status
            ;;
        logs)
            logs
            ;;
        build)
            build
            ;;
        *)
            echo "使用方法: $0 {start|stop|restart|status|logs|build}"
            echo ""
            echo "命令说明:"
            echo "  start   - 启动应用"
            echo "  stop    - 停止应用"
            echo "  restart - 重启应用"
            echo "  status  - 查看应用状态"
            echo "  logs    - 查看应用日志"
            echo "  build   - 构建应用"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"

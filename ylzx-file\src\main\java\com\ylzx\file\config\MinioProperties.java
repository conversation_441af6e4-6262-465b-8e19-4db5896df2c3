package com.ylzx.file.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * MinIO配置属性
 * 
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "minio")
public class MinioProperties {

    /**
     * MinIO服务端点
     */
    private String endpoint;

    /**
     * 访问密钥
     */
    private String accessKey;

    /**
     * 秘密密钥
     */
    private String secretKey;

    /**
     * 存储桶名称
     */
    private String bucketName;

    /**
     * 是否使用HTTPS
     */
    private Boolean secure = false;

    /**
     * 连接超时时间（毫秒）
     */
    private Long connectTimeout = 10000L;

    /**
     * 写入超时时间（毫秒）
     */
    private Long writeTimeout = 60000L;

    /**
     * 读取超时时间（毫秒）
     */
    private Long readTimeout = 10000L;

    /**
     * 预签名URL过期时间（秒）
     */
    private Integer presignedUrlExpiry = 3600;
}

package com.ylzx.annotation.service.impl;

import com.ylzx.annotation.service.MinioFileService;
import com.ylzx.file.service.FileService;
import com.ylzx.file.domain.dto.FileUploadRequest;
import com.ylzx.file.domain.dto.FileUploadResponse;
import com.ylzx.file.domain.FileInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

/**
 * MinIO文件服务实现类
 * 基于ylzx-file服务的包装实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class MinioFileServiceImpl implements MinioFileService {

    @Autowired
    private FileService fileService;

    @Override
    public String uploadFile(MultipartFile file, String folderPath, String createBy) {
        try {
            // 构建上传请求
            FileUploadRequest request = new FileUploadRequest();
            request.setFolderPath(folderPath);
            request.setCreateBy(createBy);
            request.setGenerateThumbnail(false);
            request.setOverwrite(false);
            
            FileUploadResponse response = fileService.uploadFile(file, request);
            return response.getId();
        } catch (Exception e) {
            log.error("上传文件失败: {}", file.getOriginalFilename(), e);
            throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<String> uploadFiles(List<MultipartFile> files, String folderPath, String createBy) {
        try {
            // 构建上传请求
            FileUploadRequest request = new FileUploadRequest();
            request.setFolderPath(folderPath);
            request.setCreateBy(createBy);
            request.setGenerateThumbnail(false);
            request.setOverwrite(false);
            
            List<FileUploadResponse> responses = fileService.uploadFiles(files, request);
            List<String> fileIds = new ArrayList<>();
            for (FileUploadResponse response : responses) {
                fileIds.add(response.getId());
            }
            return fileIds;
        } catch (Exception e) {
            log.error("批量上传文件失败", e);
            throw new RuntimeException("批量文件上传失败: " + e.getMessage(), e);
        }
    }

    @Override
    public InputStream downloadFile(String fileId) {
        try {
            return fileService.downloadFile(fileId);
        } catch (Exception e) {
            log.error("下载文件失败: {}", fileId, e);
            return null;
        }
    }

    @Override
    public boolean deleteFile(String fileId) {
        try {
            return fileService.deleteFile(fileId);
        } catch (Exception e) {
            log.error("删除文件失败: {}", fileId, e);
            return false;
        }
    }

    @Override
    public boolean deleteFiles(List<String> fileIds) {
        try {
            return fileService.deleteFiles(fileIds);
        } catch (Exception e) {
            log.error("批量删除文件失败", e);
            return false;
        }
    }

    @Override
    public String getPresignedUrl(String fileId) {
        try {
            return fileService.getPresignedUrl(fileId);
        } catch (Exception e) {
            log.error("获取预签名URL失败: {}", fileId, e);
            return null;
        }
    }

    @Override
    public Map<String, String> getPresignedUrls(List<String> fileIds) {
        Map<String, String> urlMap = new HashMap<>();
        for (String fileId : fileIds) {
            try {
                String url = getPresignedUrl(fileId);
                if (url != null) {
                    urlMap.put(fileId, url);
                }
            } catch (Exception e) {
                log.error("批量获取预签名URL失败: {}", fileId, e);
            }
        }
        return urlMap;
    }

    @Override
    public String checkFileExists(String md5Hash) {
        try {
            FileInfo fileInfo = fileService.checkFileExists(md5Hash);
            return fileInfo != null ? fileInfo.getId() : null;
        } catch (Exception e) {
            log.error("检查文件存在性失败: {}", md5Hash, e);
            return null;
        }
    }

    @Override
    public FileInfoDto getFileInfo(String fileId) {
        try {
            FileInfo fileInfo = fileService.getFileInfo(fileId);
            if (fileInfo == null) {
                return null;
            }
            
            // 转换为MinioFileService的FileInfoDto
            FileInfoDto dto = new FileInfoDto();
            dto.setId(fileInfo.getId());
            dto.setOriginalFilename(fileInfo.getOriginalFilename());
            dto.setFileName(fileInfo.getFileName());
            dto.setFileSize(fileInfo.getFileSize());
            dto.setContentType(fileInfo.getContentType());
            dto.setFileExtension(fileInfo.getFileExtension());
            dto.setFilePath(fileInfo.getFilePath());
            dto.setFileUrl(fileInfo.getFileUrl());
            dto.setMd5Hash(fileInfo.getMd5Hash());
            dto.setThumbnailUrl(fileInfo.getThumbnailUrl());
            dto.setCreateTime(fileInfo.getCreateTime());
            
            return dto;
        } catch (Exception e) {
            log.error("获取文件信息失败: {}", fileId, e);
            return null;
        }
    }
}
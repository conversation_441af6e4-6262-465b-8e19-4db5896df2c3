package com.ruoyi.web.service.impl;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.config.ResourcesConfig;
import com.ruoyi.system.api.domain.SysFile;
import com.ruoyi.system.api.domain.vo.SysFileSign;
import com.ruoyi.web.domain.FileInfoEntity;
import com.ruoyi.system.api.domain.ImageTextRequest;
import com.ruoyi.web.service.FileInfoService;
import com.ruoyi.web.service.SysFileInfoSignService;
import com.ruoyi.web.service.SysFileService;
import com.ruoyi.web.service.TextImageService;
import com.ruoyi.web.service.SysFileInfoMergeService;
import com.ruoyi.web.domain.SysFileInfoMerge;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.time.LocalDateTime;
import java.util.*;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 文本图片生成服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class TextImageServiceImpl implements TextImageService {

    @Autowired
    private FileInfoService fileInfoService;

    @Autowired
    private SysFileService sysFileService;

    // 使用@Lazy注解标注SysFileInfoSignService依赖，解决循环依赖问题
    @Autowired
    @Lazy
    private SysFileInfoSignService sysFileInfoSignService;

    @Autowired
    private SysFileInfoMergeService sysFileInfoMergeService;

    @Autowired
    private ResourcesConfig resourcesConfig;

    /**
     * 签名目录名称常量，用于URL路径拼接
     */
    private static final String SIGN_NAME_PATH = "signName/";

    /**
     * 签名文件本地存储目录路径常量
     */
    private static final String SIGN_NAME_LOCAL_PATH = "D:\\signName\\";

    // 修改为带过期时间的图片缓存
    private static class ImageCacheEntry {
        private final BufferedImage image;
        private final LocalDateTime expiryTime;

        public ImageCacheEntry(BufferedImage image) {
            this.image = image;
            this.expiryTime = LocalDateTime.now().plusMinutes(30); // 30分钟过期
        }

        public BufferedImage getImage() {
            return image;
        }

        public boolean isExpired() {
            return LocalDateTime.now().isAfter(expiryTime);
        }
    }

    // 使用ConcurrentHashMap保证线程安全
    private static final Map<String, ImageCacheEntry> imageCache = new ConcurrentHashMap<>();

    // 添加URL缓存，缓存签名的URL，30分钟过期
    private static class CacheEntry {
        private final String url;
        private final LocalDateTime expiryTime;

        public CacheEntry(String url) {
            this.url = url;
            this.expiryTime = LocalDateTime.now().plusMinutes(30); // 30分钟过期
        }

        public String getUrl() {
            return url;
        }

        public boolean isExpired() {
            return LocalDateTime.now().isAfter(expiryTime);
        }
    }

    // 使用ConcurrentHashMap保证线程安全
    private static final Map<String, CacheEntry> urlCache = new ConcurrentHashMap<>();

    // 添加无效请求缓存，用于存储无效ownerId的映射关系
    private static class InvalidRequestCacheEntry {
        private final List<String> validOwnerIds;
        private final List<String> validNames;
        private final LocalDateTime expiryTime;

        public InvalidRequestCacheEntry(List<String> validOwnerIds, List<String> validNames) {
            this.validOwnerIds = validOwnerIds;
            this.validNames = validNames;
            this.expiryTime = LocalDateTime.now().plusMinutes(30); // 30分钟过期
        }

        public List<String> getValidOwnerIds() {
            return validOwnerIds;
        }

        public List<String> getValidNames() {
            return validNames;
        }

        public boolean isExpired() {
            return LocalDateTime.now().isAfter(expiryTime);
        }
    }

    // 使用ConcurrentHashMap存储无效请求的映射关系
    private static final Map<String, InvalidRequestCacheEntry> invalidRequestCache = new ConcurrentHashMap<>();

    // 定期清理过期缓存的定时任务
    static {
        Timer timer = new Timer(true);
        timer.schedule(new TimerTask() {
            @Override
            public void run() {
                cleanExpiredCache();
            }
        }, 5 * 60 * 1000, 5 * 60 * 1000); // 每5分钟执行一次清理
    }

    // 清理过期的缓存
    private static void cleanExpiredCache() {
        // 只有当缓存数量超过100条时，才清除过期的缓存
        if (imageCache.size() > 100) {
            imageCache.entrySet().removeIf(entry -> entry.getValue().isExpired());
        }

        // 只有当URL缓存数量超过100条时，才清除过期的缓存
        if (urlCache.size() > 100) {
            urlCache.entrySet().removeIf(entry -> entry.getValue().isExpired());
        }

        // 清理过期的无效请求缓存
        invalidRequestCache.entrySet().removeIf(entry -> entry.getValue().isExpired());
    }

    @Override
    public void generateTextImage(String text, int fontSize, int width, int height,
                                  boolean forceDoubleLines, String textColor, String backgroundColor,
                                  String fontName, boolean addShadow, OutputStream output) throws IOException {

        // 生成缓存键
        String cacheKey = text + "_" + fontSize + "_" + width + "_" + height + "_" + forceDoubleLines
                + "_" + textColor + "_" + backgroundColor + "_" + fontName + "_" + addShadow;

        // 检查缓存
        ImageCacheEntry cachedEntry = imageCache.get(cacheKey);
        if (cachedEntry != null && !cachedEntry.isExpired()) {
            ImageIO.write(cachedEntry.getImage(), "png", output);
            return;
        }

        // 生成图片
        BufferedImage image = generateImage(text, fontSize, width, height, forceDoubleLines,
                parseColor(textColor), parseColor(backgroundColor), fontName, addShadow);

        // 缓存图片
        imageCache.put(cacheKey, new ImageCacheEntry(image));

        // 输出图片
        ImageIO.write(image, "png", output);
    }

    @Override
    public Color parseColor(String colorStr) {
        if (colorStr == null) {
            return Color.BLACK;
        }

        if ("transparent".equalsIgnoreCase(colorStr)) {
            return new Color(0, 0, 0, 0); // 完全透明
        }

        try {
            return Color.decode(colorStr);
        } catch (NumberFormatException e) {
            return Color.BLACK; // 默认黑色
        }
    }

    /**
     * 生成文字图片的具体实现
     * 支持文本自动换行，优先在标点符号处换行
     */
    private BufferedImage generateImage(String text, int fontSize, int width, int height,
                                        boolean forceDoubleLines, Color textColor, Color backgroundColor, String fontName,
                                        boolean addShadow) throws IOException {

        // 创建图片 - 如果背景是透明的，使用支持透明度的图片类型
        BufferedImage image;
        if (backgroundColor.getAlpha() < 255) {
            image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
        } else {
            image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        }

        Graphics2D g = image.createGraphics();

        // 设置高质量渲染
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        g.setRenderingHint(RenderingHints.KEY_FRACTIONALMETRICS, RenderingHints.VALUE_FRACTIONALMETRICS_ON);
        g.setRenderingHint(RenderingHints.KEY_STROKE_CONTROL, RenderingHints.VALUE_STROKE_PURE);

        // 设置背景色 - 如果不是完全透明才填充背景
        if (backgroundColor.getAlpha() > 0) {
            g.setColor(backgroundColor);
            g.fillRect(0, 0, width, height);
        }

        // 如果文本为空，直接返回背景图
        if (text == null || text.trim().isEmpty()) {
            g.dispose();
            return image;
        }

        // 设置字体
        Font font;
        try {
            font = new Font(fontName, Font.BOLD, fontSize);
        } catch (Exception e) {
            log.warn("指定的字体 {} 不可用，使用默认字体", fontName, e);
            font = new Font("宋体", Font.BOLD, fontSize);
        }
        g.setFont(font);

        // 获取字体度量
        FontMetrics metrics = g.getFontMetrics(font);

        // 自动调整字体大小以适应文本
        int adjustedFontSize = findOptimalFontSize(text, fontSize, width, height, g, forceDoubleLines);
        if (adjustedFontSize != fontSize) {
            font = new Font(font.getName(), Font.BOLD, adjustedFontSize);
            g.setFont(font);
            metrics = g.getFontMetrics(font);
        }

        // 根据文本长度和可用空间拆分成多行
        List<String> lines = splitTextIntoLines(text, width - 20, metrics, forceDoubleLines);

        // 绘制文本
        g.setColor(textColor);
        if (lines.size() == 1 && !forceDoubleLines) {
            drawSingleLineText(g, lines.get(0), width, height, metrics, textColor, addShadow);
        } else {
            drawMultilineText(g, lines, width, height, metrics, textColor, addShadow);
        }

        g.dispose();
        return image;
    }

    /**
     * 绘制单行文本
     */
    private void drawSingleLineText(Graphics2D g, String text, int width, int height,
                                    FontMetrics metrics, Color textColor, boolean addShadow) {
        int x = (width - metrics.stringWidth(text)) / 2;
        int y = ((height - metrics.getHeight()) / 2) + metrics.getAscent();

        // 添加阴影效果（如果需要）
        if (addShadow) {
            g.setColor(new Color(0, 0, 0, 50));
            g.drawString(text, x + 2, y + 2);
            g.setColor(textColor);
        }

        g.drawString(text, x, y);
    }

    /**
     * 绘制多行文本
     */
    private void drawMultilineText(Graphics2D g, List<String> lines, int width, int height,
                                   FontMetrics metrics, Color textColor, boolean addShadow) {
        int lineHeight = metrics.getHeight();
        int totalTextHeight = lineHeight * lines.size();
        int startY = (height - totalTextHeight) / 2 + metrics.getAscent();

        for (int i = 0; i < lines.size(); i++) {
            String line = lines.get(i);
            int x = (width - metrics.stringWidth(line)) / 2;
            int y = startY + (i * lineHeight);

            // 添加阴影效果（如果需要）
            if (addShadow) {
                g.setColor(new Color(0, 0, 0, 50));
                g.drawString(line, x + 2, y + 2);
                g.setColor(textColor);
            }

            g.drawString(line, x, y);
        }
    }

    /**
     * 将文本分割成多行，优先在标点符号处换行
     * 改进版：考虑中英文混合情况和词组完整性
     */
    private List<String> splitTextIntoLines(String text, int maxWidth, FontMetrics metrics, boolean forceDoubleLines) {
        List<String> lines = new ArrayList<>();

        // 如果强制双行显示且文本长度适中，直接在中间分割
        if (forceDoubleLines && metrics.stringWidth(text) <= maxWidth * 1.5) {
            int bestSplitPoint = findBestSplitPoint(text);
            if (bestSplitPoint > 0) {
                lines.add(text.substring(0, bestSplitPoint + 1));
                if (bestSplitPoint + 1 < text.length()) {
                    lines.add(text.substring(bestSplitPoint + 1));
                }
                return lines;
            }
        }

        StringBuilder currentLine = new StringBuilder();

        // 标点符号：中文标点和英文标点
        char[] chinesePunctuations = {'，', '。', '、', '；', '：', '！', '？', '"', '"', '\u2018', '\u2019', '（', '）', '【', '】', '《', '》'};
        char[] englishPunctuations = {',', '.', ';', ':', '!', '?', '"', '\'', '(', ')', '[', ']', '{', '}'};

        // 单词分割标记，用于确保英文单词的完整性
        boolean inEnglishWord = false;
        StringBuilder currentWord = new StringBuilder();

        int currentWidth = 0;

        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            int charWidth = metrics.charWidth(c);

            // 检查是否是英文字符（ASCII范围内的字母和数字）
            boolean isEnglishChar = (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || (c >= '0' && c <= '9');

            // 处理英文单词
            if (isEnglishChar) {
                if (!inEnglishWord) {
                    // 开始新的英文单词
                    inEnglishWord = true;
                    currentWord.setLength(0);
                }
                currentWord.append(c);
            } else {
                // 如果遇到非英文字符，结束当前英文单词
                if (inEnglishWord) {
                    // 检查添加整个单词后是否会超出宽度
                    int wordWidth = metrics.stringWidth(currentWord.toString());

                    if (currentWidth + wordWidth <= maxWidth) {
                        // 可以添加整个单词
                        currentLine.append(currentWord);
                        currentWidth += wordWidth;
                    } else if (currentLine.length() > 0) {
                        // 当前行已有内容，添加当前行并开始新行
                        lines.add(currentLine.toString());
                        currentLine = new StringBuilder(currentWord);
                        currentWidth = wordWidth;
                    } else {
                        // 单词太长，被迫分割
                        currentLine.append(currentWord);
                        currentWidth += wordWidth;
                    }

                    inEnglishWord = false;
                    currentWord.setLength(0);
                }

                // 处理当前非英文字符
                if (currentWidth + charWidth <= maxWidth) {
                    currentLine.append(c);
                    currentWidth += charWidth;

                    // 检查是否是标点符号
                    boolean isPunctuation = false;
                    for (char p : chinesePunctuations) {
                        if (c == p) {
                            isPunctuation = true;
                            break;
                        }
                    }
                    if (!isPunctuation) {
                        for (char p : englishPunctuations) {
                            if (c == p) {
                                isPunctuation = true;
                                break;
                            }
                        }
                    }

                    // 如果是标点符号且当前行已达一定长度，考虑换行
                    if (isPunctuation && currentWidth > maxWidth / 2 && lines.size() < 1) {
                        lines.add(currentLine.toString());
                        currentLine = new StringBuilder();
                        currentWidth = 0;
                    }
                } else {
                    // 如果当前行已经有内容，添加当前行
                    if (currentLine.length() > 0) {
                        lines.add(currentLine.toString());
                        currentLine = new StringBuilder();
                        currentWidth = 0;
                    }

                    // 添加当前字符到新行
                    currentLine.append(c);
                    currentWidth = charWidth;
                }
            }
        }

        // 处理未完成的英文单词
        if (inEnglishWord && currentWord.length() > 0) {
            int wordWidth = metrics.stringWidth(currentWord.toString());

            if (currentWidth + wordWidth <= maxWidth) {
                // 可以添加整个单词
                currentLine.append(currentWord);
            } else if (currentLine.length() > 0) {
                // 当前行已有内容，添加当前行并开始新行
                lines.add(currentLine.toString());
                currentLine = new StringBuilder(currentWord);
            } else {
                // 单词太长，被迫分割
                currentLine.append(currentWord);
            }
        }

        // 添加最后一行
        if (currentLine.length() > 0) {
            lines.add(currentLine.toString());
        }

        // 如果只有一行但需要强制双行，尝试在合适位置分割
        if (lines.size() == 1 && forceDoubleLines) {
            String singleLine = lines.get(0);
            int midPoint = findBestSplitPoint(singleLine);

            if (midPoint > 0 && midPoint < singleLine.length() - 1) {
                lines.clear();
                lines.add(singleLine.substring(0, midPoint + 1));
                lines.add(singleLine.substring(midPoint + 1));
            }
        }

        return lines;
    }

    /**
     * 寻找最佳分割点，优先选择标点符号
     * 改进版：更智能地选择分割点，避免破坏语义
     */
    private int findBestSplitPoint(String text) {
        int midIndex = text.length() / 2;
        char[] punctuations = {'，', '、', '。', '；', '：', '！', '？', ',', '.', ';', ':', '!', '?'};

        // 首先检查是否有明显的分段标志（如顿号、逗号等）
        for (int i = 0; i < text.length(); i++) {
            for (char p : punctuations) {
                if (text.charAt(i) == p) {
                    // 找到标点，判断它是否在合理的分割范围内（不要太靠前或太靠后）
                    if (i >= text.length() / 3 && i <= text.length() * 2 / 3) {
                        return i;
                    }
                }
            }
        }

        // 从中间向两边搜索最近的标点符号
        int searchRange = Math.min(10, midIndex); // 限制搜索范围，避免分割点偏离中心过多
        for (int offset = 0; offset < searchRange; offset++) {
            // 向右搜索
            int rightIndex = midIndex + offset;
            if (rightIndex < text.length()) {
                for (char p : punctuations) {
                    if (text.charAt(rightIndex) == p) {
                        return rightIndex;
                    }
                }

                // 如果右侧字符是空格，也可以作为分割点
                if (text.charAt(rightIndex) == ' ') {
                    return rightIndex;
                }
            }

            // 向左搜索
            int leftIndex = midIndex - offset;
            if (leftIndex >= 0) {
                for (char p : punctuations) {
                    if (text.charAt(leftIndex) == p) {
                        return leftIndex;
                    }
                }

                // 如果左侧字符是空格，也可以作为分割点
                if (text.charAt(leftIndex) == ' ') {
                    return leftIndex;
                }
            }
        }

        // 如果没有找到标点符号或空格，尝试避开英文单词中间
        int i = midIndex;
        while (i > 0 && i < text.length() - 1) {
            char prevChar = text.charAt(i - 1);
            char nextChar = text.charAt(i);

            boolean prevIsEnglish = (prevChar >= 'a' && prevChar <= 'z') || (prevChar >= 'A' && prevChar <= 'Z');
            boolean nextIsEnglish = (nextChar >= 'a' && nextChar <= 'z') || (nextChar >= 'A' && nextChar <= 'Z');

            // 如果当前位置前后不是连续的英文字母，可以作为分割点
            if (!(prevIsEnglish && nextIsEnglish)) {
                return i - 1;
            }

            // 继续向左搜索
            i--;
        }

        // 如果依然没有找到合适的分割点，就在中间位置分割
        return midIndex;
    }

    /**
     * 使用二分查找法查找最优字体大小
     */
    private int findOptimalFontSize(String text, int initialFontSize, int maxWidth, int maxHeight,
                                    Graphics2D g, boolean forceDoubleLines) {
        int minFontSize = 12; // 最小字体大小
        int maxFontSize = initialFontSize;
        int optimalFontSize = minFontSize;

        while (minFontSize <= maxFontSize) {
            int midFontSize = (minFontSize + maxFontSize) / 2;
            Font testFont = new Font(g.getFont().getName(), Font.BOLD, midFontSize);
            g.setFont(testFont);
            FontMetrics metrics = g.getFontMetrics(testFont);

            List<String> lines = splitTextIntoLines(text, maxWidth, metrics, forceDoubleLines);
            int lineHeight = metrics.getHeight();
            int totalHeight = lines.size() * lineHeight;

            // 单行文本的宽度或多行文本的总高度
            boolean isSuitable = lines.size() <= 2 && totalHeight <= maxHeight;
            for (String line : lines) {
                if (metrics.stringWidth(line) > maxWidth) {
                    isSuitable = false;
                    break;
                }
            }

            if (isSuitable) {
                // 这个字体大小合适，尝试更大的字体
                optimalFontSize = midFontSize;
                minFontSize = midFontSize + 1;
            } else {
                // 字体太大，尝试更小的字体
                maxFontSize = midFontSize - 1;
            }
        }

        return optimalFontSize;
    }

    @Override
    public String mergeImagesAndTexts(ImageTextRequest request, int targetWidth, int targetHeight,
                                      int fontSize, String textColor, String backgroundColor, String fontName,
                                      boolean addShadow) throws IOException {

        List<String> ownerIdList = request.getOwnerIdList();
        List<String> nameList = request.getNameList();

        // 如果ownerIdList和nameList同时为null或空，则返回null
        if ((ownerIdList == null || ownerIdList.isEmpty()) &&
                (nameList == null || nameList.isEmpty())) {
            return null;
        }

        // 1. 计算总共需要处理的项数
        int totalItems = (ownerIdList != null ? ownerIdList.size() : 0) + (nameList != null ? nameList.size() : 0);

        if (totalItems == 0) {
            // 没有任何项目，返回null
            return null;
        }

        // 检查无效请求缓存
        String requestKey = generateRequestKey(ownerIdList, nameList);
        InvalidRequestCacheEntry invalidRequestEntry = invalidRequestCache.get(requestKey);
        if (invalidRequestEntry != null && !invalidRequestEntry.isExpired()) {
            // 使用缓存中的有效ID列表
            ownerIdList = invalidRequestEntry.getValidOwnerIds();
            nameList = invalidRequestEntry.getValidNames();
        }

        // 合并所有ownerId和signName，直接查询数据库
        String ownerIdStr = ownerIdList != null ? String.join(",", ownerIdList) : "";
        String signNameStr = nameList != null ? String.join(",", nameList) : "";

        // 尝试从数据库中查询已有的记录
        SysFileInfoMerge existingMerge = sysFileInfoMergeService.selectByOwnerIdAndSignName(ownerIdStr, signNameStr);
        if (existingMerge != null && existingMerge.getFileName() != null) {
            return resourcesConfig.getDomainAndAssets() + SIGN_NAME_PATH + existingMerge.getFileName();
        }

        // 处理nameList，查询已有签名，缺失的创建
        Map<String, String> nameUrlMap = new HashMap<>();
        if (nameList != null && !nameList.isEmpty()) {
            // 优先从URL缓存中获取
            List<String> uncachedNames = new ArrayList<>();
            for (String name : nameList) {
                CacheEntry entry = urlCache.get(name);
                if (entry != null && !entry.isExpired()) {
                    nameUrlMap.put(name, entry.getUrl());
                } else {
                    uncachedNames.add(name);
                }
            }

            // 查询未缓存的签名
            if (!uncachedNames.isEmpty()) {
                List<SysFileSign> existingSigns = sysFileInfoSignService.selectFileInfoWithSignBatch(uncachedNames);
                Map<String, SysFileSign> existingSignMap = new HashMap<>();

                if (existingSigns != null && !existingSigns.isEmpty()) {
                    existingSignMap = existingSigns.stream()
                            .collect(Collectors.toMap(SysFileSign::getSignName, sign -> sign));
                }

                // 处理所有未缓存的签名
                for (String name : uncachedNames) {
                    SysFileSign sign = existingSignMap.get(name);

                    // 如果签名不存在，创建新签名
                    if (sign == null) {
                        sign = sysFileInfoSignService.insert(name);
                    }

                    // 如果签名创建成功，缓存URL
                    if (sign != null && sign.getUrl() != null) {
                        String url = sign.getUrl();
                        nameUrlMap.put(name, url);
                        urlCache.put(name, new CacheEntry(url));
                    }
                }
            }
        }

        // 处理ownerIdList，缓存URL
        Map<String, String> ownerUrlMap = new HashMap<>();
        List<String> validOwnerIds = new ArrayList<>();
        if (ownerIdList != null && !ownerIdList.isEmpty()) {
            for (String ownerId : ownerIdList) {
                // 检查缓存
                CacheEntry entry = urlCache.get(ownerId);
                if (entry != null && !entry.isExpired()) {
                    ownerUrlMap.put(ownerId, entry.getUrl());
                    validOwnerIds.add(ownerId);
                } else {
                    // 查询文件信息
                    R<List<SysFile>> filesResult = sysFileService.findFiles(ownerId);
                    if (filesResult.getCode() == 200 && filesResult.getData() != null && !filesResult.getData().isEmpty()) {
                        SysFile file = filesResult.getData().get(0);
                        String fileUrl = file.getUrl();
                        if (fileUrl != null && !fileUrl.isEmpty()) {
                            ownerUrlMap.put(ownerId, fileUrl);
                            urlCache.put(ownerId, new CacheEntry(fileUrl));
                            validOwnerIds.add(ownerId);
                        }
                    }
                }
            }

            // 如果有无效的ownerId，更新缓存
            if (validOwnerIds.size() < ownerIdList.size()) {
                invalidRequestCache.put(requestKey, new InvalidRequestCacheEntry(validOwnerIds, nameList));
                // 重新计算ownerIdStr，只包含有效的ownerId
                ownerIdStr = validOwnerIds.isEmpty() ? "" : String.join(",", validOwnerIds);
            }
        }

        // 2. 确定布局（行数和每行项数）
        int rows = totalItems > 3 ? 2 : 1;
        int itemsPerRow = (int) Math.ceil((double) totalItems / rows);

        // 3. 计算合并后图片的原始尺寸
        int originalWidth = targetWidth * itemsPerRow;
        int originalHeight = targetHeight * rows;

        // 4. 计算缩放比例
        double scaleWidth = (double) targetWidth / originalWidth;
        double scaleHeight = (double) targetHeight / originalHeight;
        double scale = Math.min(scaleWidth, scaleHeight); // 取最小的缩放比例，保持宽高比

        // 5. 计算实际绘制尺寸
        int finalWidth = (int) (originalWidth * scale);
        int finalHeight = (int) (originalHeight * scale);

        // 6. 计算居中绘制的偏移量
        int offsetX = (targetWidth - finalWidth) / 2;
        int offsetY = (targetHeight - finalHeight) / 2;

        // 7. 创建目标尺寸的图片
        Color bgColor = parseColor(backgroundColor);
        BufferedImage targetImage;
        if (bgColor.getAlpha() < 255) {
            targetImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_ARGB);
        } else {
            targetImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);
        }
        Graphics2D targetG = targetImage.createGraphics();
        targetG.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        targetG.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        targetG.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        targetG.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        targetG.setRenderingHint(RenderingHints.KEY_FRACTIONALMETRICS, RenderingHints.VALUE_FRACTIONALMETRICS_ON);

        // 设置背景颜色（如果不是完全透明）
        if (bgColor.getAlpha() > 0) {
            targetG.setColor(bgColor);
            targetG.fillRect(0, 0, targetWidth, targetHeight);
        }

        // 8. 创建原始尺寸的临时图片
        BufferedImage tempImage;
        if (bgColor.getAlpha() < 255) {
            tempImage = new BufferedImage(originalWidth, originalHeight, BufferedImage.TYPE_INT_ARGB);
        } else {
            tempImage = new BufferedImage(originalWidth, originalHeight, BufferedImage.TYPE_INT_RGB);
        }
        Graphics2D g = tempImage.createGraphics();
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
        g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
        g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        g.setRenderingHint(RenderingHints.KEY_FRACTIONALMETRICS, RenderingHints.VALUE_FRACTIONALMETRICS_ON);

        if (bgColor.getAlpha() > 0) {
            g.setColor(bgColor);
            g.fillRect(0, 0, originalWidth, originalHeight);
        }

        // 9. 处理每个项目，绘制到临时图片上
        int currentIndex = 0;

        // 9.1 先处理文件图片
        if (ownerIdList != null && !ownerIdList.isEmpty()) {
            for (String ownerId : ownerIdList) {
                BufferedImage itemImage = null;
                String url = ownerUrlMap.get(ownerId);

                if (url != null) {
                    // 使用URL获取图片
                    itemImage = getImageFromUrl(url, targetWidth, targetHeight);
                } else {
                    // 回退到原有方法
                    itemImage = getOwnerImage(ownerId, targetWidth, targetHeight);
                }

                if (itemImage != null) {
                    int row = currentIndex / itemsPerRow;
                    int col = currentIndex % itemsPerRow;
                    int x = col * targetWidth;
                    int y = row * targetHeight;

                    g.drawImage(itemImage, x, y, null);
                    currentIndex++;
                }
            }
        }

        // 9.2 处理文本图片 - 优先使用缓存的签名URL获取图片
        if (nameList != null && !nameList.isEmpty()) {
            for (String name : nameList) {
                BufferedImage textImage = null;
                String url = nameUrlMap.get(name);

                if (url != null) {
                    // 使用URL获取图片
                    textImage = getImageFromUrl(url, targetWidth, targetHeight);
                }

                // 如果没有获取到图片，回退到生成文本图片
                if (textImage == null) {
                    textImage = generateTextImageForMerge(name, fontSize, targetWidth, targetHeight,
                            false, parseColor(textColor), bgColor, fontName, addShadow);
                }

                int row = currentIndex / itemsPerRow;
                int col = currentIndex % itemsPerRow;
                int x = col * targetWidth;
                int y = row * targetHeight;

                g.drawImage(textImage, x, y, null);
                currentIndex++;
            }
        }

        g.dispose();

        // 10. 将临时图片缩放并绘制到目标图片上
        targetG.drawImage(tempImage, offsetX, offsetY, finalWidth, finalHeight, null);
        targetG.dispose();

        // 生成UUID作为文件名
        String uuid = UUID.randomUUID().toString();
        String fileName = uuid + ".png";

        // 确保目标文件夹存在
        File directory = new File(SIGN_NAME_LOCAL_PATH);
        if (!directory.exists()) {
            directory.mkdirs();
        }

        // 保存图片到文件
        File outputFile = new File(directory, fileName);
        ImageIO.write(targetImage, "png", outputFile);

        // 更新数据库
        if (existingMerge != null) {
            // 更新现有记录
            existingMerge.setFileName(fileName);
            sysFileInfoMergeService.updateByPrimaryKey(existingMerge);
        } else {
            // 创建新记录
            SysFileInfoMerge newMerge = SysFileInfoMerge.builder()
                    .ownerId(ownerIdStr)
                    .signName(signNameStr)
                    .fileName(fileName)
                    .build();
            sysFileInfoMergeService.insert(newMerge);
        }

        // 返回URL
        return resourcesConfig.getDomainAndAssets() + SIGN_NAME_PATH + fileName;
    }

    /**
     * 从URL获取图片并缩放
     */
    private BufferedImage getImageFromUrl(String imageUrl, int targetWidth, int targetHeight) {
        if (imageUrl == null || imageUrl.isEmpty()) {
            return null;
        }

        // 构建缓存键
        String cacheKey = "urlImage:" + imageUrl + ":width:" + targetWidth + ":height:" + targetHeight;

        // 检查缓存
        ImageCacheEntry cachedEntry = imageCache.get(cacheKey);
        if (cachedEntry != null && !cachedEntry.isExpired()) {
            return cachedEntry.getImage();
        }

        try {
            // 读取文件
            InputStream inputStream;
            try {
                // 尝试通过URL获取文件
                inputStream = new URL(imageUrl).openStream();
            } catch (Exception e) {
                return null;
            }

            // 读取图片
            BufferedImage originalImage = ImageIO.read(inputStream);
            inputStream.close();
            if (originalImage == null) {
                return null;
            }

            // 缩放图片
            int originalWidth = originalImage.getWidth();
            int originalHeight = originalImage.getHeight();

            // 保持宽高比例
            double ratio = Math.min((double) targetWidth / originalWidth, (double) targetHeight / originalHeight);
            int scaledWidth = (int) (originalWidth * ratio);
            int scaledHeight = (int) (originalHeight * ratio);

            // 居中绘制
            int x = (targetWidth - scaledWidth) / 2;
            int y = (targetHeight - scaledHeight) / 2;

            // 创建新图片
            BufferedImage newImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);
            Graphics2D g = newImage.createGraphics();
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            // 填充背景
            g.setColor(Color.WHITE);
            g.fillRect(0, 0, targetWidth, targetHeight);

            // 绘制图片
            g.drawImage(originalImage, x, y, scaledWidth, scaledHeight, null);
            g.dispose();

            // 添加到缓存
            imageCache.put(cacheKey, new ImageCacheEntry(newImage));

            return newImage;
        } catch (Exception e) {
            log.error("从URL获取图片失败: {}", imageUrl, e);
            return null;
        }
    }

    /**
     * 根据ownerId获取图片并等比例缩放到指定大小
     */
    private BufferedImage getOwnerImage(String ownerId, int targetWidth, int targetHeight) {
        if (ownerId == null || ownerId.isEmpty()) {
            return null;
        }

        // 构建缓存键
        String cacheKey = "ownerImage:" + ownerId + ":width:" + targetWidth + ":height:" + targetHeight;

        // 检查缓存
        ImageCacheEntry cachedEntry = imageCache.get(cacheKey);
        if (cachedEntry != null && !cachedEntry.isExpired()) {
            return cachedEntry.getImage();
        }

        try {
            // 查询文件信息
            R<List<SysFile>> filesResult = sysFileService.findFiles(ownerId);
            if (filesResult.getCode() != 200 || filesResult.getData() == null || filesResult.getData().isEmpty()) {
                return null;
            }

            SysFile file = filesResult.getData().get(0);
            String fileUrl = file.getUrl();
            if (fileUrl == null || fileUrl.isEmpty()) {
                return null;
            }

            // 检查文件是否存在
            FileInfoEntity fileInfoEntity = fileInfoService.getById(file.getId());
            if (fileInfoEntity == null) {
                return null;
            }

            // 读取文件
            InputStream inputStream;
            try {
                // 尝试通过URL获取文件
                inputStream = new URL(fileUrl).openStream();
            } catch (Exception e) {
                try {
                    // 如果URL无效，尝试本地路径
                    // 根据文件实体的信息构建文件路径
                    String filePath = fileInfoEntity.getFileName();
                    if (fileInfoEntity.getStoragePath() != null) {
                        filePath = fileInfoEntity.getStoragePath() + filePath;
                    }
                    inputStream = new FileInputStream(filePath);
                } catch (Exception ex) {
                    return null;
                }
            }

            // 读取图片
            BufferedImage originalImage = ImageIO.read(inputStream);
            inputStream.close();
            if (originalImage == null) {
                return null;
            }

            // 缩放图片
            int originalWidth = originalImage.getWidth();
            int originalHeight = originalImage.getHeight();

            // 保持宽高比例
            double ratio = Math.min((double) targetWidth / originalWidth, (double) targetHeight / originalHeight);
            int scaledWidth = (int) (originalWidth * ratio);
            int scaledHeight = (int) (originalHeight * ratio);

            // 居中绘制
            int x = (targetWidth - scaledWidth) / 2;
            int y = (targetHeight - scaledHeight) / 2;

            // 创建新图片
            BufferedImage newImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);
            Graphics2D g = newImage.createGraphics();
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

            // 填充背景
            g.setColor(Color.WHITE);
            g.fillRect(0, 0, targetWidth, targetHeight);

            // 绘制图片
            g.drawImage(originalImage, x, y, scaledWidth, scaledHeight, null);
            g.dispose();

            // 添加到缓存
            imageCache.put(cacheKey, new ImageCacheEntry(newImage));

            return newImage;
        } catch (Exception e) {
            // 出现异常返回null
            return null;
        }
    }

    /**
     * 为合并生成文本图片
     */
    private BufferedImage generateTextImageForMerge(String text, int fontSize, int width, int height,
                                                    boolean forceDoubleLines, Color textColor, Color backgroundColor, String fontName,
                                                    boolean addShadow) throws IOException {

        // 构建缓存键
        String cacheKey = "text:" + text
                + ":fontSize:" + fontSize
                + ":width:" + width
                + ":height:" + height
                + ":forceDoubleLines:" + forceDoubleLines
                + ":textColor:" + textColor.getRGB()
                + ":bgColor:" + backgroundColor.getRGB()
                + ":fontName:" + fontName
                + ":shadow:" + addShadow;

        // 检查缓存
        ImageCacheEntry cachedEntry = imageCache.get(cacheKey);
        if (cachedEntry != null && !cachedEntry.isExpired()) {
            return cachedEntry.getImage();
        }

        // 使用ByteArrayOutputStream先将图片生成到内存中
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        // 将颜色转换为十六进制字符串，特殊处理透明背景
        String bgColorStr;
        if (backgroundColor.getAlpha() == 0) {
            bgColorStr = "transparent";
        } else {
            bgColorStr = "#" + Integer.toHexString(backgroundColor.getRGB() & 0xffffff);
        }

        generateTextImage(text, fontSize, width, height, forceDoubleLines,
                "#" + Integer.toHexString(textColor.getRGB() & 0xffffff),
                bgColorStr,
                fontName, addShadow, baos);

        // 从内存中读取图片
        byte[] imageData = baos.toByteArray();
        BufferedImage image = ImageIO.read(new ByteArrayInputStream(imageData));

        // 添加到缓存
        imageCache.put(cacheKey, new ImageCacheEntry(image));

        return image;
    }

    /**
     * 预生成图片并缓存
     * 用于提前生成图片并存入缓存，提高实际请求时的响应速度
     *
     * @param requests        图片生成请求列表
     * @param targetWidth     目标宽度
     * @param targetHeight    目标高度
     * @param fontSize        字体大小
     * @param textColor       文字颜色
     * @param backgroundColor 背景颜色
     * @param fontName        字体名称
     * @param addShadow       是否添加阴影
     * @return 预生成的图片数量
     */
    public int preGenerateImagesCache(List<ImageTextRequest> requests, int targetWidth, int targetHeight,
                                      int fontSize, String textColor, String backgroundColor, String fontName,
                                      boolean addShadow) {
        if (requests == null || requests.isEmpty()) {
            return 0;
        }

        int generatedCount = 0;
        for (ImageTextRequest request : requests) {
            try {
                List<String> ownerIdList = request.getOwnerIdList();
                List<String> nameList = request.getNameList();

                // 如果ownerIdList和nameList同时为null或空，则跳过
                if ((ownerIdList == null || ownerIdList.isEmpty()) &&
                        (nameList == null || nameList.isEmpty())) {
                    continue;
                }

                // 合并ownerId和signName
                String ownerIdStr = ownerIdList != null ? String.join(",", ownerIdList) : "";
                String signNameStr = nameList != null ? String.join(",", nameList) : "";

                // 检查数据库中是否已存在对应记录
                SysFileInfoMerge existingMerge = sysFileInfoMergeService.selectByOwnerIdAndSignName(ownerIdStr, signNameStr);
                if (existingMerge != null && existingMerge.getFileName() != null) {
                    continue;
                }

                // 调用合并方法，生成并保存图片
                mergeImagesAndTexts(request, targetWidth, targetHeight, fontSize, textColor,
                        backgroundColor, fontName, addShadow);

                generatedCount++;
            } catch (Exception e) {
                log.error("预生成图片缓存失败: {}", e.getMessage(), e);
            }
        }
        return generatedCount;
    }

    /**
     * 生成请求的唯一键
     */
    private String generateRequestKey(List<String> ownerIdList, List<String> nameList) {
        StringBuilder key = new StringBuilder();
        if (ownerIdList != null) {
            key.append("ownerIds:").append(String.join(",", ownerIdList));
        }
        if (nameList != null) {
            key.append(":names:").append(String.join(",", nameList));
        }
        return key.toString();
    }

    @Override
    public Map<String, String> batchGetUrls(ImageTextRequest request) {
        Map<String, String> resultMap = new HashMap<>();
        List<String> ownerIdList = request.getOwnerIdList();
        List<String> nameList = request.getNameList();

        // 如果ownerIdList和nameList同时为null或空，则返回空Map
        if ((ownerIdList == null || ownerIdList.isEmpty()) &&
                (nameList == null || nameList.isEmpty())) {
            return resultMap;
        }

        // 处理nameList，查询已有签名，缺失的创建
        if (nameList != null && !nameList.isEmpty()) {
            // 优先从URL缓存中获取
            List<String> uncachedNames = new ArrayList<>();
            for (String name : nameList) {
                CacheEntry entry = urlCache.get(name);
                if (entry != null && !entry.isExpired()) {
                    resultMap.put(name, entry.getUrl());
                } else {
                    uncachedNames.add(name);
                }
            }

            // 查询未缓存的签名
            if (!uncachedNames.isEmpty()) {
                List<SysFileSign> existingSigns = sysFileInfoSignService.selectFileInfoWithSignBatch(uncachedNames);
                Map<String, SysFileSign> existingSignMap = new HashMap<>();

                if (existingSigns != null && !existingSigns.isEmpty()) {
                    existingSignMap = existingSigns.stream()
                            .collect(Collectors.toMap(SysFileSign::getSignName, sign -> sign));
                }

                // 处理所有未缓存的签名
                for (String name : uncachedNames) {
                    SysFileSign sign = existingSignMap.get(name);

                    // 如果签名不存在，创建新签名
                    if (sign == null) {
                        sign = sysFileInfoSignService.insert(name);
                    }

                    // 如果签名创建成功，缓存URL并添加到结果Map
                    if (sign != null && sign.getUrl() != null) {
                        String url = sign.getUrl();
                        resultMap.put(name, url);
                        urlCache.put(name, new CacheEntry(url));
                    }
                }
            }
        }

        // 处理ownerIdList，查询并缓存URL
        if (ownerIdList != null && !ownerIdList.isEmpty()) {
            for (String ownerId : ownerIdList) {
                // 检查缓存
                CacheEntry entry = urlCache.get(ownerId);
                if (entry != null && !entry.isExpired()) {
                    resultMap.put(ownerId, entry.getUrl());
                } else {
                    // 查询文件信息
                    R<List<SysFile>> filesResult = sysFileService.findFiles(ownerId);
                    if (filesResult.getCode() == 200 && filesResult.getData() != null && !filesResult.getData().isEmpty()) {
                        SysFile file = filesResult.getData().get(0);
                        String fileUrl = file.getUrl();
                        if (fileUrl != null && !fileUrl.isEmpty()) {
                            resultMap.put(ownerId, fileUrl);
                            urlCache.put(ownerId, new CacheEntry(fileUrl));
                        }
                    }
                }
            }
        }

        return resultMap;
    }
} 
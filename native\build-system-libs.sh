#!/bin/bash

echo "========================================"
echo "Static build with MSYS2 system libraries"
echo "========================================"

# 构建选项
BUILD_STATIC=${BUILD_STATIC:-ON}
echo "Build mode: $([ "$BUILD_STATIC" = "ON" ] && echo "Static (portable)" || echo "Dynamic")"

# 检查是否在MSYS2环境中
if [ -z "$MSYSTEM" ]; then
    echo "Error: This script should be run in MSYS2 MinGW64 environment"
    echo "Please open MSYS2 MinGW 64-bit terminal and run this script"
    exit 1
fi

echo "✓ MSYS2 environment detected: $MSYSTEM"

# 检查系统包是否安装
echo "Checking system packages..."

check_system_package() {
    local package=$1
    if pacman -Qi $package >/dev/null 2>&1; then
        echo "✓ $package is installed (system)"
        return 0
    else
        echo "✗ $package not found"
        return 1
    fi
}

# 检查必要的系统包
MISSING_PACKAGES=()

if ! check_system_package "mingw-w64-x86_64-opencv"; then
    MISSING_PACKAGES+=("mingw-w64-x86_64-opencv")
fi

if ! check_system_package "mingw-w64-x86_64-curl"; then
    MISSING_PACKAGES+=("mingw-w64-x86_64-curl")
fi

if ! check_system_package "mingw-w64-x86_64-openssl"; then
    MISSING_PACKAGES+=("mingw-w64-x86_64-openssl")
fi

if ! check_system_package "mingw-w64-x86_64-nlohmann-json"; then
    MISSING_PACKAGES+=("mingw-w64-x86_64-nlohmann-json")
fi

# 安装缺失的包
if [ ${#MISSING_PACKAGES[@]} -gt 0 ]; then
    echo "Installing missing packages: ${MISSING_PACKAGES[*]}"
    pacman -S --noconfirm "${MISSING_PACKAGES[@]}"
fi

# 检查vcpkg（仅用于MinIO）
if [ -z "$VCPKG_ROOT" ]; then
    if [ -d "$HOME/vcpkg" ]; then
        export VCPKG_ROOT="$HOME/vcpkg"
        echo "✓ Found vcpkg in home directory: $VCPKG_ROOT"
    elif [ -d "/c/vcpkg" ]; then
        export VCPKG_ROOT="/c/vcpkg"
        echo "✓ Found vcpkg in C:/vcpkg: $VCPKG_ROOT"
    else
        echo "⚠ vcpkg not found. MinIO support may be limited."
        echo "If you need MinIO support, please install vcpkg and set VCPKG_ROOT"
        VCPKG_ROOT=""
    fi
fi

# 检查MinIO（仅从vcpkg）
MINIO_FOUND=false
if [ -n "$VCPKG_ROOT" ] && [ -f "$VCPKG_ROOT/vcpkg.exe" ]; then
    echo "Checking MinIO in vcpkg..."
    if $VCPKG_ROOT/vcpkg.exe list | grep -q "minio"; then
        echo "✓ MinIO found in vcpkg:"
        $VCPKG_ROOT/vcpkg.exe list | grep minio
        MINIO_FOUND=true
        export CMAKE_TOOLCHAIN_FILE="$VCPKG_ROOT/scripts/buildsystems/vcpkg.cmake"
        export VCPKG_TARGET_TRIPLET="x64-mingw-dynamic"
    else
        echo "✗ MinIO not found in vcpkg"
        echo "To install MinIO: $VCPKG_ROOT/vcpkg.exe install minio-cpp:x64-mingw-dynamic"
    fi
fi

# 创建并进入构建目录
mkdir -p build
cd build

# 清理之前的构建
rm -f CMakeCache.txt
rm -rf CMakeFiles

echo "========================================"
echo "Configuring with CMake..."
echo "========================================"

# 设置CMake参数
CMAKE_ARGS=(
    -G "MinGW Makefiles"
    -DCMAKE_BUILD_TYPE=Release
    -DBUILD_STATIC="$BUILD_STATIC"
    -DCMAKE_C_COMPILER=gcc
    -DCMAKE_CXX_COMPILER=g++
)

# 静态编译特殊配置
if [ "$BUILD_STATIC" = "ON" ]; then
    CMAKE_ARGS+=(
        -DCMAKE_FIND_LIBRARY_SUFFIXES=".a"
        -DBUILD_SHARED_LIBS=OFF
    )
    echo "✓ Configuring for static linking (portable)"
fi

# 如果有vcpkg和MinIO，添加vcpkg支持
if [ "$MINIO_FOUND" = true ]; then
    CMAKE_ARGS+=(
        -DCMAKE_TOOLCHAIN_FILE="$CMAKE_TOOLCHAIN_FILE"
        -DVCPKG_TARGET_TRIPLET="$VCPKG_TARGET_TRIPLET"
    )
    echo "✓ Using vcpkg for MinIO support"
else
    echo "⚠ Building without MinIO support"
fi

# 配置CMake
cmake .. "${CMAKE_ARGS[@]}"

if [ $? -ne 0 ]; then
    echo "❌ CMake configuration failed!"
    echo "Please check the error messages above"
    exit 1
fi

echo "========================================"
echo "Building..."
echo "========================================"

# 构建
make -j$(nproc)

if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    echo "Please check the error messages above"
    exit 1
fi

echo "========================================"
echo "Build completed successfully! ✓"
echo "========================================"

# 查找生成的库文件
echo "Looking for generated library files..."
LIBRARY_FOUND=false

for lib in libdataset_export.dll dataset_export.dll libdataset_export.so; do
    if [ -f "$lib" ]; then
        echo "✓ Found: $lib"
        LIBRARY_FOUND=true
        
        # 显示文件信息
        ls -lh "$lib"
        
        # 复制到Java resources目录
        JAVA_RESOURCES_DIR="../ylzx-annotation/src/main/resources/native"
        if [ -d "$JAVA_RESOURCES_DIR" ]; then
            echo "Copying to Java resources directory..."
            cp "$lib" "$JAVA_RESOURCES_DIR/"
            if [ $? -eq 0 ]; then
                echo "✓ Library copied to: $JAVA_RESOURCES_DIR/$lib"
            else
                echo "❌ Failed to copy library"
            fi
        else
            echo "⚠ Java resources directory not found: $JAVA_RESOURCES_DIR"
            echo "Please copy $lib manually to your Java project"
        fi
        break
    fi
done

if [ "$LIBRARY_FOUND" = false ]; then
    echo "❌ No library file found!"
    echo "Available files:"
    ls -la
fi

# 显示依赖信息
echo ""
echo "Library dependencies:"
if [ "$LIBRARY_FOUND" = true ] && command -v ldd >/dev/null 2>&1; then
    for lib in libdataset_export.dll dataset_export.dll; do
        if [ -f "$lib" ]; then
            echo "Dependencies for $lib:"
            ldd "$lib" | head -15
            break
        fi
    done
fi

echo ""
echo "========================================"
echo "Build Summary"
echo "========================================"
echo "✓ Environment: $MSYSTEM"
echo "✓ Compiler: $(gcc --version | head -1)"
echo "✓ OpenCV: System package (pacman)"
echo "✓ Other libs: System packages"
if [ "$MINIO_FOUND" = true ]; then
    echo "✓ MinIO: vcpkg ($VCPKG_ROOT)"
else
    echo "⚠ MinIO: Not available"
fi
echo ""
echo "Next steps:"
echo "1. Restart your Java application"
echo "2. Test the image processing functionality"
if [ "$MINIO_FOUND" = false ]; then
    echo "3. Install MinIO via vcpkg if needed"
fi
echo ""
echo "If you encounter issues:"
echo "1. Check that the library is in the correct location"
echo "2. Verify all DLL dependencies are available"
echo "3. Check the Java application logs"

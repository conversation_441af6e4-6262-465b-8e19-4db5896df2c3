package com.ruoyi.web.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.file.MiniOStorage;
import com.ruoyi.common.core.utils.StringUtils;
import com.ruoyi.common.core.utils.file.FileTypeUtils;
import com.ruoyi.common.core.utils.file.MimeTypeUtils;
import com.ruoyi.config.ResourcesConfig;
import com.ruoyi.system.api.domain.SysFile;
import com.ruoyi.system.api.domain.vo.SysFileSearchVO;
import com.ruoyi.web.domain.FileInfoEntity;
import com.ruoyi.web.service.FileInfoService;
import com.ruoyi.web.service.SysFileService;
import com.ruoyi.web.utils.ImageUtils;
import com.ruoyi.web.utils.TokenUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import net.coobird.thumbnailator.geometry.Positions;
import org.dromara.x.file.storage.core.FileInfo;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统文件服务实现类
 * <p>
 * 该服务提供文件上传、下载、查询和管理的功能，支持本地存储和对象存储两种模式。
 * 用于处理系统中的文件操作，包括文件的上传、获取、删除等功能。
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysFileServiceImpl implements SysFileService {

    /**
     * 文件信息服务，用于操作文件元数据
     */
    private final FileInfoService fileInfoService;

    /**
     * 文件存储服务，用于实际的文件存储操作
     */
    private final FileStorageService fileStorageService;

    /**
     * 资源配置，包含域名、路径等配置信息
     */
    private final ResourcesConfig resourcesConfig;

    /**
     * 排序间隔常量，用于文件排序
     */
    private static final Long GAP = 1000L;


    /**
     * 根据拥有者ID获取单个文件
     * <p>
     * 处理流程：
     * 1. 根据拥有者ID查询文件信息
     * 2. 构建文件信息并处理URL（区分静态和非静态资源）
     * 3. 返回第一个文件（如果存在）
     * </p>
     *
     * @param ownerId 文件拥有者ID
     * @return 包含文件信息的响应对象
     */
    @Override
    public R<SysFile> getFile(String ownerId) {
        // 查询文件列表
        List<FileInfoEntity> fileEntities = queryFilesByOwnerId(ownerId);
        List<SysFile> sysFiles = convertToSysFiles(fileEntities);

        // 返回第一个文件或空结果
        if (sysFiles.isEmpty()) {
            return R.ok();
        }
        return R.ok(sysFiles.get(0));
    }

    /**
     * 根据拥有者ID获取所有文件
     * <p>
     * 处理流程：
     * 1. 根据拥有者ID查询文件信息
     * 2. 构建文件信息并处理URL（区分静态和非静态资源）
     * 3. 返回所有文件列表
     * </p>
     *
     * @param ownerId 文件拥有者ID
     * @return 包含文件列表的响应对象
     */
    @Override
    public R<List<SysFile>> findFiles(String ownerId) {
        // 查询文件列表
        List<FileInfoEntity> fileEntities = queryFilesByOwnerId(ownerId);
        List<SysFile> sysFiles = convertToSysFiles(fileEntities);

        return R.ok(sysFiles);
    }

    /**
     * 根据fileId获取文件
     *
     * @param fileId
     * @return
     */
    @Override
    public R<SysFile> getFileByFileId(String fileId) {
        QueryWrapper<FileInfoEntity> qw = new QueryWrapper<>();
        qw.eq("id", fileId);
        qw.eq("del_flag", 0);
        FileInfoEntity fileInfoEntity = fileInfoService.getOne(qw);
        if (fileInfoEntity == null) {
            return R.ok();
        }
        List<SysFile> sysFiles = convertToSysFiles(Collections.singletonList(fileInfoEntity));
        if (sysFiles.isEmpty()) {
            return R.ok();
        }
        return R.ok(sysFiles.get(0));
    }

    /**
     * 传入fileIdList，返回fileIdList对应的文件列表
     *
     * @param fileIdList
     * @return
     */
    @Override
    public R<List<SysFile>> findFilesByFileIdList(List<String> fileIdList) {
        if (fileIdList.isEmpty()) {
            return R.ok(new ArrayList<>());
        }
        QueryWrapper<FileInfoEntity> qw = new QueryWrapper<>();
        qw.in("id", fileIdList);
        qw.eq("del_flag", 0);
        List<FileInfoEntity> fileEntities = fileInfoService.list(qw);
        List<SysFile> sysFiles = convertToSysFiles(fileEntities);
        return R.ok(sysFiles);
    }

    /**
     * 根据多个拥有者ID获取所有文件
     * <p>
     * 处理流程：
     * 1. 检查拥有者ID列表是否为空
     * 2. 根据拥有者ID列表查询文件信息
     * 3. 构建文件信息并处理URL
     * 4. 返回所有文件列表
     * </p>
     *
     * @param ownerIds 文件拥有者ID列表
     * @return 包含文件列表的响应对象
     */
    @Override
    public R<List<SysFile>> findFilesByOwnerIds(List<String> ownerIds) {
        if (ownerIds.isEmpty()) {
            return R.ok(new ArrayList<>());
        }

        // 构建查询条件
        QueryWrapper<FileInfoEntity> qw = new QueryWrapper<>();
        qw.in("owner_id", ownerIds);
        qw.eq("del_flag", 0);
        qw.orderByAsc("sort");

        // 查询并转换文件信息
        List<FileInfoEntity> fileEntities = fileInfoService.list(qw);
        List<SysFile> sysFiles = convertToSysFiles(fileEntities);

        return R.ok(sysFiles);
    }

    /**
     * 构造文件URL
     * <p>
     * 处理流程：
     * 1. 检查域名和文件URL是否为空
     * 2. 提取协议部分
     * 3. 规范化路径，确保正确的斜杠格式
     * 4. 拼接完整URL
     * </p>
     * <p>
     * 示例：
     * - 输入: domainHost="http://example.com", fileUrl="path/to/file.jpg"
     * - 输出: "http://example.com/path/to/file.jpg"
     *
     * @param domainHost 域名主机
     * @param fileUrl    文件URL
     * @return 完整的文件URL
     */
    @Override
    public String constructFileUrl(String domainHost, String fileUrl) {
        // 如果域名为空，返回空字符串
        if (StringUtils.isEmpty(domainHost)) {
            return "";
        }

        domainHost = domainHost + resourcesConfig.getPrefix().get(0);

        // 如果文件URL为空，返回域名
        if (StringUtils.isEmpty(fileUrl)) {
            return domainHost;
        }

        // 提取协议部分（如果有）
        String protocol = "";
        int protocolEndIndex = domainHost.indexOf("://");
        if (protocolEndIndex != -1) {
            protocol = domainHost.substring(0, protocolEndIndex + 3);
            domainHost = domainHost.substring(protocolEndIndex + 3);
        }

        // 确保域名以单个斜杠结尾
        if (!domainHost.endsWith("/")) {
            domainHost += "/";
        }

        // 确保文件URL不以斜杠开头
        if (fileUrl.startsWith("/")) {
            fileUrl = fileUrl.substring(1);
        }

        // 拼接URL并规范化斜杠
        String fullUrl = domainHost + fileUrl;
        fullUrl = fullUrl.replaceAll("[/\\\\]+", "/");

        // 重新加上协议部分
        return protocol + fullUrl;
    }

    /**
     * 上传文件
     * <p>
     * 处理流程：
     * 1. 准备上传参数，设置默认值
     * 2. 根据是否为静态资源选择不同的处理策略
     * 3. 存储文件并生成URL
     * 4. 保存文件元数据信息
     * </p>
     * <p>
     * 策略：
     * - 静态资源：保存在本地文件系统
     * - 非静态资源：保存在对象存储系统
     *
     * @param file            上传的文件
     * @param sysFileSearchVO 文件参数
     * @return 文件信息响应
     * @throws IOException IO异常
     */
    @Override
    public R<SysFile> upload(MultipartFile file, SysFileSearchVO sysFileSearchVO) throws IOException {
        log.info("文件上传接收到的参数:{}", sysFileSearchVO);

        // 参数预处理
        prepareUploadParams(sysFileSearchVO);

        FileInfoEntity fileInfoEntity = new FileInfoEntity();
        FileInfo fileInfo;
        String thumbUrl = null;

        try {
            // 根据是否为静态资源选择不同处理策略
            if (Boolean.TRUE.equals(sysFileSearchVO.getIsStatic())) {
                // 处理静态资源
                fileInfo = handleStaticResource(file, sysFileSearchVO);
            } else {
                // 处理非静态资源
                fileInfo = handleNonStaticResource(file, sysFileSearchVO);
                // 如果是图片，生成缩略图URL
                if (isImage(file)) {
                    thumbUrl = fileStorageService.generateThPresignedUrl(fileInfo, DateUtil.offsetHour(new Date(), 10));
                }
            }

            // 设置文件URL
            String url = generateFileUrl(fileInfo, sysFileSearchVO);

            // 填充并保存文件信息
            fillFileInfoEntity(fileInfoEntity, fileInfo, sysFileSearchVO, url, thumbUrl);
            fileInfoService.save(fileInfoEntity);

            // 创建返回对象
            SysFile sysFile = new SysFile();
            BeanUtil.copyProperties(fileInfoEntity, sysFile);

            // 对静态资源处理URL
            if (Boolean.TRUE.equals(sysFileSearchVO.getIsStatic())) {
                sysFile.setUrl(constructFileUrl(resourcesConfig.getDomainAndAssets(), sysFile.getUrl()));
                sysFile.setThumbUrl(constructFileUrl(resourcesConfig.getDomainAndAssets(), sysFile.getThumbUrl()));
            }

            return R.ok(sysFile);
        } catch (IOException e) {
            log.error("文件保存失败", e);
            return R.fail("文件保存失败");
        }
    }

    /**
     * 生成唯一文件名
     * <p>
     * 使用UUID生成唯一文件名，保留原文件扩展名
     * </p>
     *
     * @param file 文件
     * @return 唯一文件名
     */
    @Override
    public String generateUniqueFilename(MultipartFile file) {
        String extension = FileTypeUtils.getExtension(file);
        return UUID.randomUUID() + "." + extension;
    }

    /**
     * 处理非静态资源
     * <p>
     * 处理流程：
     * 1. 检查文件是否为图片
     * 2. 根据不同类型选择不同的上传策略
     * 3. 上传到对象存储
     * </p>
     *
     * @param file            文件
     * @param sysFileSearchVO 文件参数
     * @return 文件信息
     */
    @Override
    public FileInfo handleNonStaticResource(MultipartFile file, SysFileSearchVO sysFileSearchVO) {
        FileInfo fileInfo;
        // 图片文件需要生成缩略图
        if (isImage(file)) {
            try {
                //获取缩略图
                java.io.InputStream thInputStream = getThumbW400InputStream(file);
                System.out.println("++++++++++"+sysFileSearchVO);

                fileInfo = fileStorageService.of(file)
                        .setPlatform(sysFileSearchVO.getPlatform())
                        .thumbnailOf(thInputStream) // 生成缩略图
                        .setPath(sysFileSearchVO.getStoragePath())
                        .upload();
            } catch (IOException e) {
                e.printStackTrace();
                log.error("缩略图生成失败，使用默认方式生成：{}", e.getMessage());
                fileInfo = fileStorageService.of(file)
                        .setPlatform(sysFileSearchVO.getPlatform())
                        .thumbnail()  // 生成缩略图
                        .setPath(sysFileSearchVO.getStoragePath())
                        .upload();
            }

        } else {
            fileInfo = fileStorageService.of(file)
                    .setPlatform(sysFileSearchVO.getPlatform())
                    .setPath(sysFileSearchVO.getStoragePath())
                    .upload();
        }
        log.info("文件上传信息: {}", fileInfo);
        return fileInfo;
    }

    private static final int THUMBNAIL_SIZE = 400;

    public static InputStream getThumbW400InputStream(MultipartFile multipartFile) throws IOException {
        // 读取图片
        BufferedImage originalImage = ImageIO.read(multipartFile.getInputStream());
        if (originalImage == null) {
            throw new IOException("无法读取原始图片");
        }
//
//        int width = originalImage.getWidth();
//        int height = originalImage.getHeight();

        BufferedImage resizedImage;
        resizedImage = Thumbnails.of(originalImage)
                .width(THUMBNAIL_SIZE)
                .asBufferedImage();

        // 将缩略图写入字节数组输出流
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        boolean success = ImageIO.write(resizedImage, "jpg", outputStream);
        if (!success) {
            // 尝试使用其他格式
            success = ImageIO.write(resizedImage, "png", outputStream);
            if (!success) {
                throw new IOException("无法写入缩略图，尝试了 jpg 和 png 格式均失败");
            }
        }

        // 从字节数组输出流创建字节数组输入流
        return new ByteArrayInputStream(outputStream.toByteArray());
    }

    public static InputStream getThumbInputStream(MultipartFile multipartFile) throws IOException {
        // 读取图片
        BufferedImage originalImage = ImageIO.read(multipartFile.getInputStream());
        if (originalImage == null) {
            throw new IOException("无法读取原始图片");
        }

        int width = originalImage.getWidth();
        int height = originalImage.getHeight();

        BufferedImage resizedImage;
        if (width > height) {
            // 宽大于高，按高缩放到 400 尺寸
            if (height < THUMBNAIL_SIZE) {
                // 如果原始高度小于缩略图尺寸，不进行缩放
                resizedImage = originalImage;
            } else {
                resizedImage = Thumbnails.of(originalImage)
                        .height(THUMBNAIL_SIZE)
                        .asBufferedImage();
            }
        } else {
            // 宽小于等于高，按宽缩放到 400 尺寸
            if (width < THUMBNAIL_SIZE) {
                // 如果原始宽度小于缩略图尺寸，不进行缩放
                resizedImage = originalImage;
            } else {
                resizedImage = Thumbnails.of(originalImage)
                        .width(THUMBNAIL_SIZE)
                        .asBufferedImage();
            }
        }

        // 中间截图获取 400x400 缩略图
        if (resizedImage.getWidth() < THUMBNAIL_SIZE || resizedImage.getHeight() < THUMBNAIL_SIZE) {
            // 如果缩放后的图片尺寸小于缩略图尺寸，不进行截图
            resizedImage = Thumbnails.of(resizedImage)
                    .size(resizedImage.getWidth(), resizedImage.getHeight())
                    .asBufferedImage();
        } else {
            resizedImage = Thumbnails.of(resizedImage)
                    .sourceRegion(Positions.CENTER, THUMBNAIL_SIZE, THUMBNAIL_SIZE)
                    .size(THUMBNAIL_SIZE, THUMBNAIL_SIZE)
                    .asBufferedImage();
        }

        // 将缩略图写入字节数组输出流
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        boolean success = ImageIO.write(resizedImage, "jpg", outputStream);
        if (!success) {
            // 尝试使用其他格式
            success = ImageIO.write(resizedImage, "png", outputStream);
            if (!success) {
                throw new IOException("无法写入缩略图，尝试了 jpg 和 png 格式均失败");
            }
        }

        // 从字节数组输出流创建字节数组输入流
        return new ByteArrayInputStream(outputStream.toByteArray());
    }

    /**
     * 生成文件URL
     * <p>
     * 处理流程：
     * 1. 检查存储平台类型
     * 2. 根据不同平台类型生成对应的URL
     * </p>
     *
     * @param fileInfo        文件信息
     * @param sysFileSearchVO 文件参数
     * @return 文件URL
     */
    @Override
    public String generateFileUrl(FileInfo fileInfo, SysFileSearchVO sysFileSearchVO) {
        if ("local".equals(fileInfo.getPlatform())) {
            // 本地存储，生成相对路径
            return Paths.get(sysFileSearchVO.getStoragePath(), fileInfo.getFilename()).toString();
        } else {
            // 对象存储，生成预签名URL
            return fileStorageService.generatePresignedUrl(fileInfo, DateUtil.offsetHour(new Date(), 10));
        }
    }

    /**
     * 创建文件信息对象
     * <p>
     * 从上传文件中提取信息，创建FileInfo对象
     * </p>
     *
     * @param file     文件
     * @param platform 平台
     * @param path     路径
     * @param filename 文件名
     * @return 文件信息对象
     */
    @Override
    public FileInfo createFileInfo(MultipartFile file, String platform, String path, String filename) {
        FileInfo fileInfo = new FileInfo();
        fileInfo.setPlatform(platform);
        fileInfo.setPath(path);
        fileInfo.setFilename(filename);
        fileInfo.setOriginalFilename(file.getOriginalFilename());
        fileInfo.setExt(FileTypeUtils.getExtension(file));
        fileInfo.setContentType(file.getContentType());
        fileInfo.setSize(file.getSize());
        return fileInfo;
    }

    /**
     * 判断文件是否为图片
     * <p>
     * 通过文件扩展名判断是否为图片类型
     * </p>
     *
     * @param file 文件
     * @return 是否为图片
     */
    @Override
    public boolean isImage(MultipartFile file) {
        return StringUtils.equalsAnyIgnoreCase(FileTypeUtils.getExtension(file), MimeTypeUtils.IMAGE_EXTENSION);
    }

    /**
     * 填充文件信息实体
     * <p>
     * 将文件信息填充到数据库实体对象中
     * </p>
     *
     * @param fileInfoEntity  文件信息实体
     * @param fileInfo        文件信息
     * @param sysFileSearchVO 文件参数
     * @param url             文件URL
     * @param thumbUrl        缩略图URL
     */
    @Override
    public void fillFileInfoEntity(FileInfoEntity fileInfoEntity, FileInfo fileInfo, SysFileSearchVO sysFileSearchVO, String url, String thumbUrl) {
        // 基本信息
        fileInfoEntity.setOwnerId(sysFileSearchVO.getOwnerId());
        fileInfoEntity.setPlatform(fileInfo.getPlatform());
        fileInfoEntity.setStoragePath(fileInfo.getPath());
        fileInfoEntity.setFileSize(fileInfo.getSize());
        fileInfoEntity.setThSize(fileInfo.getThSize());
        fileInfoEntity.setFileName(fileInfo.getFilename());
        fileInfoEntity.setThFilename(fileInfo.getThFilename());
        fileInfoEntity.setOriginalFilename(fileInfo.getOriginalFilename());
        fileInfoEntity.setExt(fileInfo.getExt());
        fileInfoEntity.setContentType(fileInfo.getContentType());

        // 标识和URL
        fileInfoEntity.setIsStatic(sysFileSearchVO.getIsStatic());
        fileInfoEntity.setUrl(url);
        fileInfoEntity.setThumbUrl(thumbUrl);
        fileInfoEntity.setDelFlag(false);

        // 排序处理
        long sort = Optional.ofNullable(sysFileSearchVO.getSort()).orElse(0L);
        if (sort < GAP) {
            sort *= GAP;
        }
        sort = (sort + GAP) / GAP * GAP;
        fileInfoEntity.setSort(sort);

        // 备注信息
        fileInfoEntity.setRemark(sysFileSearchVO.getRemark());
    }

    /**
     * 根据ID获取文件并写入响应流
     * <p>
     * 处理流程：
     * 1. 根据ID查询文件信息
     * 2. 验证文件存在且为静态资源
     * 3. 读取文件内容并写入响应流
     * </p>
     *
     * @param response HTTP响应对象
     * @param id       文件ID
     * @throws Exception 文件读取异常
     */
    @Override
    @SneakyThrows
    public void getFileById(HttpServletResponse response, String id) throws Exception {
        // 查询文件信息
        FileInfoEntity fileInfoEntity = fileInfoService.getById(id);

        // 验证文件存在
        if (fileInfoEntity == null) {
            throw new RuntimeException("未找到文件");
        }

        // 验证文件类型
        if (Boolean.FALSE.equals(fileInfoEntity.getIsStatic())) {
            throw new RuntimeException("仅静态文件支持此方法调取");
        }

        // 获取文件路径
        String path = FileUtil.normalize(resourcesConfig.getPathByPrefix(fileInfoEntity.getPlatform())
                + "/" + fileInfoEntity.getStoragePath() + fileInfoEntity.getFileName());
        log.info("读取文件：{}", path);

        // 验证文件存在
        File file = new File(path);
        if (!file.exists()) {
            throw new RuntimeException("当前读取的文件不存在，请检查路径是否正确：" + path);
        }

        // 设置响应头
        response.reset();
        response.setCharacterEncoding("UTF-8");

        // 非图片类型设置为附件下载
        if (!StringUtils.equalsAnyIgnoreCase(fileInfoEntity.getExt(), MimeTypeUtils.IMAGE_EXTENSION)) {
            response.addHeader("Content-Disposition", "attachment;filename=" +
                    URLEncoder.encode(fileInfoEntity.getFileName(), "UTF-8"));
            response.setContentType("application/**");
        }

        // 读取文件并写入响应
        try (InputStream is = new BufferedInputStream(Files.newInputStream(file.toPath()))) {
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());

            byte[] buffer = new byte[1024];
            int len;
            while ((len = is.read(buffer)) > 0) {
                outputStream.write(buffer, 0, len);
            }
            outputStream.close();
        } catch (IOException e) {
            throw new RuntimeException("文件读取失败", e);
        }
    }

    /**
     * 根据拥有者ID删除文件
     * <p>
     * 处理流程：
     * 1. 根据拥有者ID查询文件信息
     * 2. 检查是否有文件需要删除
     * 3. 调用批量删除方法
     * </p>
     *
     * @param ownerId 文件拥有者ID
     * @return 操作结果
     */
    @Override
    public R<?> removeFilesByOwner(String ownerId) {
        // 查询文件列表
        QueryWrapper<FileInfoEntity> qw = new QueryWrapper<>();
        qw.eq("owner_id", ownerId);
        List<FileInfoEntity> fileList = fileInfoService.list(qw);

        // 检查是否有文件
        if (fileList.isEmpty()) {
            return R.fail("没有找到对应的文件");
        }

        // 批量删除文件
        List<String> fileIds = fileList.stream().map(FileInfoEntity::getId).toList();
        boolean isRemoved = fileInfoService.deleteFileInfo(fileIds);

        return isRemoved ? R.ok("所有文件删除成功") : R.fail("文件删除失败");
    }

    /**
     * 获取并合并多张图片
     * <p>
     * 处理流程：
     * 1. 获取指定拥有者的所有文件
     * 2. 过滤出图片文件并下载
     * 3. 合并图片并压缩
     * 4. 输出合并后的图片到响应流
     * </p>
     *
     * @param req     HTTP请求对象
     * @param res     HTTP响应对象
     * @param ownerId 文件拥有者ID
     * @param width   图片宽度
     * @param height  图片高度
     * @param columns 列数
     * @param gutter  间隔
     * @param padding 内边距
     * @param quality 图片质量
     * @throws IOException IO异常
     */
    @Override
    public void images(HttpServletRequest req, HttpServletResponse res, String ownerId,
                       Integer width, Integer height, Integer columns, Integer gutter,
                       Integer padding, Float quality) throws IOException {
        // 获取文件列表
        R<List<SysFile>> rFiles = this.findFiles(ownerId);

        if (rFiles != null && R.isSuccess(rFiles) && !rFiles.getData().isEmpty()) {
            // 并行下载所有图片
            List<BufferedImage> imageList = rFiles.getData().parallelStream()
                    .map(sysFile -> {
                        String url = sysFile.getUrl();
                        if (StringUtils.isNotEmpty(url)) {
                            // 提取URL和文件类型
                            url = url.substring(0, url.indexOf("?"));
                            String suffix = url.substring(url.lastIndexOf(".") + 1);
                            // 只处理图片文件
                            if (ImageUtils.isImageType(suffix)) {
                                try {
                                    return ImageIO.read(URLUtil.url(sysFile.getUrl()));
                                } catch (IOException e) {
                                    log.error("读取图片失败: {}", url, e);
                                    return null;
                                }
                            }
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 设置默认参数
            int w = (width == null) ? 640 : width;
            int h = (height == null) ? 480 : height;
            int c = (columns == null) ? 5 : columns;
            int g = (gutter == null) ? 4 : gutter;
            int p = (padding == null) ? 4 : padding;
            float q = (quality == null) ? 0.4f : quality;

            // 合并图片并输出
            byte[] imageBytes = ImageUtils.mergeAndCompress(imageList, w, h, c, g, p, q);
            res.setContentType(MediaType.IMAGE_JPEG_VALUE);
            res.getOutputStream().write(imageBytes);
            res.flushBuffer();
        }
    }

    // ----------------- 私有辅助方法 -----------------

    /**
     * 处理静态资源文件
     * <p>
     * 将文件保存到本地文件系统
     * </p>
     *
     * @param file            文件
     * @param sysFileSearchVO 文件参数
     * @return 文件信息
     * @throws IOException IO异常
     */
    private FileInfo handleStaticResource(MultipartFile file, SysFileSearchVO sysFileSearchVO) throws IOException {
        // 生成唯一文件名
        String uniqueFilename = generateUniqueFilename(file);

        // 构建本地文件路径
        Path localFilePath = Paths.get(resourcesConfig.getDefaultPath(),
                sysFileSearchVO.getStoragePath(), uniqueFilename);
        File localFile = localFilePath.toFile();

        // 确保目录存在
        if (!localFile.getParentFile().exists()) {
            localFile.getParentFile().mkdirs();
        }

        // 将文件保存到本地
        file.transferTo(localFile);

        // 创建并返回文件信息
        return createFileInfo(file, "local", sysFileSearchVO.getStoragePath(), localFile.getName());
    }

    /**
     * 准备上传参数
     * <p>
     * 设置默认值和处理特殊字符
     * </p>
     *
     * @param sysFileSearchVO 文件参数
     */
    private void prepareUploadParams(SysFileSearchVO sysFileSearchVO) {
        // 如果未传拥有者id，则创建一个
        if (StrUtil.isBlank(sysFileSearchVO.getOwnerId())) {
            sysFileSearchVO.setOwnerId(com.baomidou.mybatisplus.core.toolkit.IdWorker.getIdStr());
        }

        // 如果未传平台，则使用默认存储桶
        if (StrUtil.isBlank(sysFileSearchVO.getPlatform())) {
            sysFileSearchVO.setPlatform(MiniOStorage.MINIO.getStorage());
        }

        // 处理存储路径中的特殊字符
        if (StrUtil.isNotBlank(sysFileSearchVO.getStoragePath())) {
            sysFileSearchVO.setStoragePath(
                    StringUtils.replace(sysFileSearchVO.getStoragePath(), "[", "-")
                            .replace("]", "-")
                            .replace(" ", ""));
        }

        // 如果为静态资源则设置平台为local
        if (Boolean.TRUE.equals(sysFileSearchVO.getIsStatic())) {
            sysFileSearchVO.setPlatform("local");
        }
    }

    /**
     * 根据拥有者ID查询文件
     *
     * @param ownerId 拥有者ID
     * @return 文件列表
     */
    private List<FileInfoEntity> queryFilesByOwnerId(String ownerId) {
        QueryWrapper<FileInfoEntity> qw = new QueryWrapper<>();
        qw.eq("owner_id", ownerId);
        qw.eq("del_flag", 0);
        qw.orderByAsc("sort");
        return fileInfoService.list(qw);
    }

    /**
     * 将文件实体列表转换为SysFile对象列表
     *
     * @param fileEntities 文件实体列表
     * @return SysFile对象列表
     */
    private List<SysFile> convertToSysFiles(List<FileInfoEntity> fileEntities) {
        List<SysFile> sysFiles = new ArrayList<>();

        fileEntities.forEach(f -> {
            // 创建文件信息对象
            FileInfo fileInfo = new FileInfo();
            fileInfo.setPlatform(f.getPlatform());
            fileInfo.setFilename(f.getFileName());
            fileInfo.setThFilename(f.getThFilename());
            fileInfo.setPath(f.getStoragePath());

            // 创建SysFile并复制属性
            SysFile sysFile = new SysFile();
            BeanUtil.copyProperties(f, sysFile);

            // 根据文件类型处理URL
            processFileUrl(f, fileInfo, sysFile);

            sysFiles.add(sysFile);
        });

        return sysFiles;
    }

    /**
     * 处理文件URL
     * <p>
     * 根据文件是否为静态资源，生成不同的URL
     * </p>
     *
     * @param fileEntity 文件实体
     * @param fileInfo   文件信息
     * @param sysFile    系统文件对象
     */
    private void processFileUrl(FileInfoEntity fileEntity, FileInfo fileInfo, SysFile sysFile) {
        if (!fileEntity.getIsStatic()) {
            // 非静态资源：生成预签名URL
            try {
                sysFile.setUrl(fileStorageService.generatePresignedUrl(fileInfo, DateUtil.offsetHour(new Date(), 10)));
                sysFile.setThumbUrl(fileStorageService.generateThPresignedUrl(fileInfo, DateUtil.offsetHour(new Date(), 10)));
            } catch (Exception e) {
                log.error("取文件失败：{}", e.getMessage());
                sysFile.setUrl(null);
                sysFile.setThumbUrl(null);
            }
        } else {
            // 静态资源：生成访问URL
            if (StrUtil.isBlank(sysFile.getUrl())) {
                processStaticResourceUrl(fileEntity, sysFile);
            } else {
                // 原文件，拼接上文件路径返回
                sysFile.setUrl(constructFileUrl(resourcesConfig.getDomainAndAssets(), sysFile.getUrl()));
                sysFile.setThumbUrl(constructFileUrl(resourcesConfig.getDomainAndAssets(), sysFile.getThumbUrl()));
            }
        }
    }

    /**
     * 处理静态资源URL
     * <p>
     * 根据文件名和路径特点生成不同格式的URL
     * </p>
     *
     * @param fileEntity 文件实体
     * @param sysFile    系统文件对象
     */
    private void processStaticResourceUrl(FileInfoEntity fileEntity, SysFile sysFile) {
        // 如果包含特殊字符，使用getFileById接口获取
        if (fileEntity.getFileName() != null &&
                (fileEntity.getFileName().contains("[") ||
                        fileEntity.getFileName().contains("]") ||
                        fileEntity.getStoragePath().contains("%"))) {

            String url = resourcesConfig.getDomainNoAssets() + "/getFileById?id=" +
                    fileEntity.getId() + "&token=" + TokenUtil.getToken();
            url = URLUtil.normalize(url, false, true);
            sysFile.setUrl(url);

            // 如果是图片，缩略图使用相同URL
            if (StringUtils.equalsAnyIgnoreCase(fileEntity.getExt(), MimeTypeUtils.IMAGE_EXTENSION)) {
                sysFile.setThumbUrl(url);
            }
        } else {
            // 直接拼接URL
            String url = org.apache.commons.lang3.StringUtils.join(
                    resourcesConfig.getDomainAndAssets(), "/",
                    fileEntity.getPlatform(), "/",
                    fileEntity.getStoragePath(), "/",
                    fileEntity.getFileName(), "?token=", TokenUtil.getToken());
            url = URLUtil.normalize(url, false, true);
            sysFile.setUrl(url);

            // 如果是图片，缩略图使用相同URL
            if (StringUtils.equalsAnyIgnoreCase(fileEntity.getExt(), MimeTypeUtils.IMAGE_EXTENSION)) {
                sysFile.setThumbUrl(url);
            }
        }
    }
} 
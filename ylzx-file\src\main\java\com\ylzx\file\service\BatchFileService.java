package com.ylzx.file.service;

import com.ylzx.file.domain.FileInfo;
import com.ylzx.file.domain.dto.FileUploadRequest;
import com.ylzx.file.domain.dto.FileUploadResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 批量文件处理服务接口
 * 使用虚线程优化批量操作性能
 * 
 * <AUTHOR>
 */
public interface BatchFileService {

    /**
     * 批量上传文件（异步）
     * 
     * @param files 文件列表
     * @param request 上传请求参数
     * @return 异步上传结果
     */
    CompletableFuture<List<FileUploadResponse>> uploadFilesAsync(List<MultipartFile> files, FileUploadRequest request);

    /**
     * 批量获取预签名URL
     * 
     * @param fileIds 文件ID列表
     * @return 文件ID到预签名URL的映射
     */
    Map<String, String> getPresignedUrls(List<String> fileIds);

    /**
     * 批量获取预签名URL（异步）
     * 
     * @param fileIds 文件ID列表
     * @return 异步结果
     */
    CompletableFuture<Map<String, String>> getPresignedUrlsAsync(List<String> fileIds);

    /**
     * 批量下载文件
     * 
     * @param fileIds 文件ID列表
     * @return 文件ID到输入流的映射
     */
    Map<String, InputStream> downloadFiles(List<String> fileIds);

    /**
     * 批量下载文件（异步）
     * 
     * @param fileIds 文件ID列表
     * @return 异步结果
     */
    CompletableFuture<Map<String, InputStream>> downloadFilesAsync(List<String> fileIds);

    /**
     * 批量检查文件是否存在
     * 
     * @param md5Hashes MD5哈希值列表
     * @return MD5哈希值到文件信息的映射
     */
    Map<String, FileInfo> checkFilesExist(List<String> md5Hashes);

    /**
     * 批量检查文件是否存在（异步）
     * 
     * @param md5Hashes MD5哈希值列表
     * @return 异步结果
     */
    CompletableFuture<Map<String, FileInfo>> checkFilesExistAsync(List<String> md5Hashes);

    /**
     * 从压缩文件中提取并批量上传文件
     * 
     * @param archiveFile 压缩文件
     * @param folderPath 目标文件夹路径
     * @param createBy 创建者
     * @return 上传结果列表
     */
    List<FileUploadResponse> extractAndUploadFromArchive(MultipartFile archiveFile, String folderPath, String createBy);

    /**
     * 从压缩文件中提取并批量上传文件（异步）
     * 
     * @param archiveFile 压缩文件
     * @param folderPath 目标文件夹路径
     * @param createBy 创建者
     * @return 异步上传结果
     */
    CompletableFuture<List<FileUploadResponse>> extractAndUploadFromArchiveAsync(MultipartFile archiveFile, String folderPath, String createBy);

    /**
     * 批量获取文件信息
     * 
     * @param fileIds 文件ID列表
     * @return 文件信息列表
     */
    List<FileInfo> getFileInfos(List<String> fileIds);

    /**
     * 批量获取文件信息（异步）
     * 
     * @param fileIds 文件ID列表
     * @return 异步结果
     */
    CompletableFuture<List<FileInfo>> getFileInfosAsync(List<String> fileIds);
}

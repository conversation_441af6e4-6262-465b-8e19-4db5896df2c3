#!/bin/bash

echo "========================================"
echo "Building dataset_export with MinIO support (MSYS2/MinGW)"
echo "========================================"

# 设置环境变量
export CMAKE_BUILD_TYPE=Release

# 自动检测MSYS2和vcpkg路径
if [ -n "$MSYSTEM_PREFIX" ]; then
    export MSYS2_ROOT="$(cygpath -w "$MSYSTEM_PREFIX" | sed 's|\\|/|g' | sed 's|^|/|' | sed 's|:|/|')"
else
    export MSYS2_ROOT="/c/msys64"
fi

# 检测vcpkg路径
if [ -n "$VCPKG_ROOT" ]; then
    echo "Using VCPKG_ROOT from environment: $VCPKG_ROOT"
elif [ -d "$HOME/vcpkg" ]; then
    export VCPKG_ROOT="$HOME/vcpkg"
    echo "Found vcpkg in home directory: $VCPKG_ROOT"
elif [ -d "/c/vcpkg" ]; then
    export VCPKG_ROOT="/c/vcpkg"
    echo "Found vcpkg in C:/vcpkg: $VCPKG_ROOT"
else
    echo "Please set VCPKG_ROOT environment variable or install vcpkg"
    echo "Example: export VCPKG_ROOT=/path/to/your/vcpkg"
    exit 1
fi

export CMAKE_TOOLCHAIN_FILE="$VCPKG_ROOT/scripts/buildsystems/vcpkg.cmake"

# 检查MSYS2环境
if [ ! -d "$MSYS2_ROOT" ]; then
    echo "Error: MSYS2 not found at $MSYS2_ROOT"
    echo "Please update MSYS2_ROOT path"
    exit 1
fi

# 检查vcpkg是否存在
if [ ! -d "$VCPKG_ROOT" ]; then
    echo "Error: vcpkg not found at $VCPKG_ROOT"
    echo "Please install vcpkg in MSYS2 or update VCPKG_ROOT path"
    echo "To install vcpkg in MSYS2:"
    echo "  cd ~"
    echo "  git clone https://github.com/Microsoft/vcpkg.git"
    echo "  cd vcpkg"
    echo "  ./bootstrap-vcpkg.sh"
    exit 1
fi

# 检查必要的包是否已安装
echo "Checking required packages..."

check_package() {
    local package=$1
    if ! $VCPKG_ROOT/vcpkg list | grep -q "$package:x64-mingw-dynamic"; then
        echo "Installing $package..."
        $VCPKG_ROOT/vcpkg install $package:x64-mingw-dynamic
        if [ $? -ne 0 ]; then
            echo "Failed to install $package"
            exit 1
        fi
    else
        echo "$package is already installed"
    fi
}

# 安装必要的依赖
check_package "curl"
check_package "openssl"
check_package "pugixml"
check_package "opencv4"
check_package "nlohmann-json"

# 检查MinIO C++ SDK
echo "Checking MinIO C++ SDK..."
if $VCPKG_ROOT/vcpkg list | grep -q "minio"; then
    echo "MinIO C++ SDK found in vcpkg!"
    $VCPKG_ROOT/vcpkg list | grep minio
    export USE_MINIO=ON
else
    echo "MinIO C++ SDK not found in vcpkg."
    echo "Please install it with:"
    echo "  $VCPKG_ROOT/vcpkg install minio-cpp:x64-mingw-dynamic"
    echo "For now, we'll try to build with MinIO support anyway..."
    export USE_MINIO=ON
fi

# 创建构建目录
mkdir -p build
cd build

# 清理之前的构建
rm -f CMakeCache.txt
rm -rf CMakeFiles

echo "========================================"
echo "Configuring CMake with MinGW..."
echo "========================================"

# 配置CMake使用MinGW
cmake .. \
    -DCMAKE_TOOLCHAIN_FILE="$CMAKE_TOOLCHAIN_FILE" \
    -DVCPKG_TARGET_TRIPLET=x64-mingw-dynamic \
    -DCMAKE_BUILD_TYPE=$CMAKE_BUILD_TYPE \
    -DCMAKE_C_COMPILER=gcc \
    -DCMAKE_CXX_COMPILER=g++ \
    -G "MinGW Makefiles"

if [ $? -ne 0 ]; then
    echo "CMake configuration failed!"
    exit 1
fi

echo "========================================"
echo "Building project..."
echo "========================================"

# 构建项目
make -j$(nproc)

if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

echo "========================================"
echo "Build completed successfully!"
echo "========================================"

# 检查生成的库文件
if [ -f "libdataset_export.dll" ]; then
    echo "Library file generated: libdataset_export.dll"
    
    # 复制到Java项目的resources目录
    JAVA_RESOURCES_DIR="../ylzx-annotation/src/main/resources/native"
    if [ -d "$JAVA_RESOURCES_DIR" ]; then
        echo "Copying library to Java resources directory..."
        cp libdataset_export.dll "$JAVA_RESOURCES_DIR/"
        if [ $? -eq 0 ]; then
            echo "Library copied successfully!"
        else
            echo "Failed to copy library to Java resources directory"
        fi
    else
        echo "Java resources directory not found: $JAVA_RESOURCES_DIR"
    fi
else
    echo "Warning: Library file not found!"
    echo "Looking for alternative names..."
    ls -la *.dll 2>/dev/null || echo "No DLL files found"
fi

echo "========================================"
echo "Build process completed!"
echo "========================================"

echo "Build information:"
echo "- Build type: $CMAKE_BUILD_TYPE"
echo "- Toolchain: MinGW"
echo "- vcpkg root: $VCPKG_ROOT"
echo "- Target triplet: x64-mingw-dynamic"
echo ""
echo "Next steps:"
echo "1. Test the Java application with the new library"
echo "2. If MinIO support is needed, install minio-cpp manually"
echo "3. Check the logs for any runtime issues"
echo ""

# 显示依赖信息
echo "Library dependencies:"
if command -v ldd >/dev/null 2>&1; then
    ldd libdataset_export.dll 2>/dev/null || echo "Could not analyze dependencies"
fi

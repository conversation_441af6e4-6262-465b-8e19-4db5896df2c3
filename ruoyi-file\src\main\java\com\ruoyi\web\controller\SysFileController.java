package com.ruoyi.web.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTHeader;
import cn.hutool.jwt.JWTUtil;
import com.ruoyi.common.core.domain.R;
import com.ruoyi.common.core.web.domain.AjaxResult;
import com.ruoyi.config.ResourcesConfig;
import com.ruoyi.system.api.domain.ImageTextRequest;
import com.ruoyi.system.api.domain.SignGenerateRequest;
import com.ruoyi.system.api.domain.SysFile;
import com.ruoyi.system.api.domain.vo.SysFileSearchVO;
import com.ruoyi.system.api.domain.vo.SysFileSign;
import com.ruoyi.web.service.FileInfoService;
import com.ruoyi.web.service.SysFileInfoSignService;
import com.ruoyi.web.service.SysFileService;
import com.ruoyi.web.service.TextImageService;
import com.ruoyi.web.utils.TokenUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.dromara.x.file.storage.core.FileStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.*;
import java.util.List;


/**
 * 文件请求处理
 *
 * <AUTHOR>
 */
@Api(tags = "文件上传")
@Slf4j
@RestController()
public class SysFileController {

    @Autowired
    private FileInfoService fileInfoService;

    @Autowired
    private FileStorageService fileStorageService;

    @Autowired
    private ResourcesConfig resourcesConfig;

    @Autowired
    @Lazy
    private TextImageService textImageService;

    @Autowired
    private SysFileService sysFileService;

    @Autowired
    @Lazy
    private SysFileInfoSignService sysFileInfoSignService;

    private static final Long GAP = 1000L;

    /**
     * 上传文件，只有{ "bmp", "gif", "jpg", "jpeg", "png" }才能生成缩略图
     *
     * @param file            文件
     * @param sysFileSearchVO 附带参数
     * @return 返回文件信息
     */
    @ApiOperation(value = "附件上传")
    @PostMapping("/upload")
    public R<SysFile> upload(MultipartFile file, SysFileSearchVO sysFileSearchVO) {
        if(StrUtil.isNotBlank(sysFileSearchVO.getStoragePath())){
            sysFileSearchVO.setStoragePath(sysFileSearchVO.getStoragePath().replaceFirst("^/", ""));
        }
        try {
            return sysFileService.upload(file, sysFileSearchVO);
        } catch (IOException e) {
            log.error("文件上传失败", e);
            return R.fail("文件上传失败");
        }
    }

    /**
     * 根据文字生成图片
     *
     * @param text             要生成的文字
     * @param fontSize         字体大小（可选，默认60）
     * @param width            图片宽度（可选，默认549）
     * @param height           图片高度（可选，默认489）
     * @param forceDoubleLines 强制双行显示（可选，默认false）
     * @param textColor        文本颜色（可选，默认黑色）
     * @param backgroundColor  背景颜色（可选，默认透明）
     * @param fontName         字体名称（可选，默认宋体）
     * @param addShadow        是否添加阴影（可选，默认false）
     */
    @ApiOperation(value = "根据文字生成图片")
    @GetMapping(value = "/generateImage", produces = MediaType.IMAGE_PNG_VALUE)
    public void generateImage(HttpServletResponse response, @RequestParam String text, @RequestParam(required = false, defaultValue = "120") Integer fontSize, @RequestParam(required = false, defaultValue = "549") Integer width, @RequestParam(required = false, defaultValue = "489") Integer height, @RequestParam(required = false, defaultValue = "false") Boolean forceDoubleLines, @RequestParam(required = false, defaultValue = "#000000") String textColor, @RequestParam(required = false, defaultValue = "transparent") String backgroundColor, @RequestParam(required = false, defaultValue = "楷体") String fontName, @RequestParam(required = false, defaultValue = "false") Boolean addShadow) {
        try {
            response.setContentType(MediaType.IMAGE_PNG_VALUE);
            // 调用服务实现生成图片
            textImageService.generateTextImage(text, fontSize, width, height, forceDoubleLines, textColor, backgroundColor, fontName, addShadow, response.getOutputStream());
        } catch (IOException e) {
            log.error("生成图片失败", e);
        }
    }

    /**
     * 交换顺序接口
     *
     * @param
     * @return
     */
    @ApiOperation(value = "交换顺序")
    @PostMapping("/updateFileSort")
    public AjaxResult updateFileSort(@RequestBody List<SysFileSearchVO> fileList) {
        return AjaxResult.success(fileInfoService.processFileSort(fileList));
    }

    /**
     * 保存备注
     *
     * @param file 文件
     * @return 结果
     */
    @ApiOperation(value = "保存备注")
    @PostMapping("/updateFileRemark")
    public AjaxResult updateFileRemark(@RequestBody SysFileSearchVO file) {
        return AjaxResult.success(fileInfoService.saveFileInfoRemark(file));
    }

    /**
     * 批量保存备注
     *
     * @param fileList 文件列表
     * @return 结果
     */
    @ApiOperation(value = "批量保存备注")
    @PostMapping("/updateFileRemarks")
    public AjaxResult updateFileRemarks(@RequestBody List<SysFileSearchVO> fileList) {
        return AjaxResult.success(fileInfoService.saveFileInfoRemark(fileList));
    }

    /**
     * 根据文件id读取文件流
     *
     * @param response HTTP响应
     * @param id       文件ID
     */
    @ApiOperation(value = "根据文件id读取文件流")
    @SneakyThrows
    @GetMapping("getFileById")
    public void getFileById(HttpServletResponse response, @RequestParam("id") String id, @RequestParam("token") String token) {
        try {
            boolean isv = JWTUtil.verify(token, "ylzx@2025".getBytes());
            if (!isv) {
                throw new RuntimeException("token解析失败");
            }
            final JWT jwt = JWTUtil.parseToken(token);
            String expDateStr = (String) jwt.getPayload("expDate");
            if (!DateUtil.date().isBefore(DateUtil.parseDateTime(expDateStr))) {
                throw new RuntimeException("token失效");
            }
        } catch (Exception e) {
            throw new RuntimeException("token解析失败");
        }

        sysFileService.getFileById(response, id);
    }

    /**
     * 查询文件
     *
     * @param ownerId 文件拥有者id
     * @return 返回文件，没有则为null
     */
    @ApiOperation(value = "根据文件拥有者id获取文件（单个文件，取获取到的第一条）")
    @ApiImplicitParam(name = "ownerId", value = "文件拥有者的id")
    @GetMapping("/getFile")
    public R<SysFile> getFile(String ownerId) {
        return sysFileService.getFile(ownerId);
    }

    /**
     * 查询文件列表
     *
     * @param ownerId 文件拥有者id
     * @return 返回文件list，没有则为空list
     */
    @ApiOperation(value = "根据文件拥有者id获取文件列表")
    @ApiImplicitParam(name = "ownerId", value = "文件拥有者id")
    @GetMapping("/findFiles")
    public R<List<SysFile>> findFiles(String ownerId) {
        return sysFileService.findFiles(ownerId);
    }

    /**
     * 查询文件列表
     *
     * @param ownerIds 文件拥有者id
     * @return 返回文件list，没有则为空list
     */
    @ApiOperation(value = "根据文件拥有者id获取文件列表")
    @ApiImplicitParam(name = "ownerIds", value = "文件拥有者id列表", dataType = "List", paramType = "body")
    @PostMapping("/findFilesByOwnerIds")
    public R<List<SysFile>> findFilesByOwnerIds(@RequestBody List<String> ownerIds) {
        return sysFileService.findFilesByOwnerIds(ownerIds);
    }

    /**
     * 删除文件
     *
     * @param fileId 文件id
     * @return 返回状态
     */
    @ApiOperation(value = "根据文件id，删除文件")
    @ApiImplicitParam(name = "fileId", value = "文件id")
    @GetMapping("/removeFile")
    public R<?> removeFile(String fileId) {
        return Optional.ofNullable(fileInfoService.getById(fileId)).map(fileInfoEntity -> {
            return fileInfoService.deleteFileInfo(fileId) ? R.ok("删除成功") : R.fail("删除失败");
        }).orElseGet(() -> R.fail("文件不存在"));
    }

    /**
     * 根据 ownerId 删除文件
     *
     * @param ownerId 文件所有者id
     * @return 返回状态
     */
    @ApiOperation(value = "根据 ownerId 删除文件")
    @ApiImplicitParam(name = "ownerId", value = "文件所有者id")
    @GetMapping("/removeFilesByOwner")
    public R<?> removeFilesByOwner(String ownerId) {
        return sysFileService.removeFilesByOwner(ownerId);
    }

    /**
     * 合并获取附件信息
     *
     * @param req     HTTP请求
     * @param res     HTTP响应
     * @param ownerId 文件所有者ID
     * @param width   图片宽度
     * @param height  图片高度
     * @param columns 列数
     * @param gutter  间距
     * @param padding 内边距
     * @param quality 图片质量
     * @throws IOException IO异常
     */
    @ApiOperation(value = "获取附件信息")
    @GetMapping("/images")
    public void images(HttpServletRequest req, HttpServletResponse res, String ownerId, Integer width, Integer height, Integer columns, Integer gutter, Integer padding, Float quality) throws IOException {
        sysFileService.images(req, res, ownerId, width, height, columns, gutter, padding, quality);
    }

    /**
     * 合并图片和文本生成一张图片
     */
    @ApiOperation(value = "合并图片和文本生成一张图片")
    @PostMapping(value = "/mergeImagesAndTexts")
    public R<String> mergeImagesAndTexts(@RequestBody ImageTextRequest request) {
        try {
            // 至少要有ownerIdList或nameList中的一个有值
            if ((request.getOwnerIdList() == null || request.getOwnerIdList().isEmpty()) && (request.getNameList() == null || request.getNameList().isEmpty())) {
                return R.fail("ownerIdList和nameList不能同时为空");
            }

            // 调用服务实现合并图片并返回URL
            String imageUrl = textImageService.mergeImagesAndTexts(request, 549, 489, 120, "#000000", "transparent", "楷体", false);

            return R.ok(imageUrl);
        } catch (IOException e) {
            log.error("合并图片失败", e);
            return R.fail("合并图片失败: " + e.getMessage());
        }
    }

    @ApiOperation("传入用户名称,生成签名")
    @PostMapping("/generateSignImage")
    public R<SysFileSign> generateSignImage(@RequestBody SignGenerateRequest request) {
        SysFileSign sysFile = sysFileInfoSignService.insert(request.getName());
        return sysFile != null ? R.ok(sysFile) : R.fail("生成签名图片失败");
    }

    @ApiOperation("传入用户名,返回SysFileSign对象")
    @PostMapping("/getSignImage")
    public R<SysFileSign> getSignImage(@RequestBody SignGenerateRequest request) {
        SysFileSign sysFileSign = sysFileInfoSignService.selectFileInfoWithSign(request.getName());
        return sysFileSign != null ? R.ok(sysFileSign) : R.fail("获取签名图片失败");
    }

    @ApiOperation("传入用户名List,返回SysFileSign对象")
    @PostMapping("/getSignImages")
    public R<List<SysFileSign>> getSignImages(@RequestBody SignGenerateRequest request) {
        List<SysFileSign> sysFileSigns = sysFileInfoSignService.selectFileInfoWithSignBatch(request.getNameList());
        return sysFileSigns != null ? R.ok(sysFileSigns) : R.fail("获取签名图片失败");
    }

    @ApiOperation("预生成图片缓存")
    @PostMapping("/preGenerateImagesCache")
    public R<Integer> preGenerateImagesCache(@RequestBody List<ImageTextRequest> requests) {
        try {
            int count = textImageService.preGenerateImagesCache(requests, 549, 489, 120, "#000000", "transparent", "楷体", false);
            return R.ok(count, "成功预生成 " + count + " 张图片");
        } catch (Exception e) {
            log.error("预生成图片缓存失败", e);
            return R.fail("预生成图片缓存失败: " + e.getMessage());
        }
    }

    @ApiOperation("获取用户签名")
    @PostMapping("/getUserSign")
    public R<Map<String, String>> getUserSign(@RequestBody ImageTextRequest request) {
        Map<String, String> map = textImageService.batchGetUrls(request);
        if (map == null || map.isEmpty()) {
            return R.fail("获取用户签名失败");
        }
        return R.ok(map);
    }

}
package com.ylzx.annotation.controller;

import java.util.List;

import com.ylzx.annotation.service.AnnotationAnnotationsService;
import com.ylzx.common.utils.StringUtils;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import com.ylzx.common.annotation.Log;
import com.ylzx.common.core.controller.BaseController;
import com.ylzx.common.core.domain.AjaxResult;
import com.ylzx.common.enums.BusinessType;
import com.ylzx.annotation.domain.AnnotationAnnotations;
import com.ylzx.common.utils.poi.ExcelUtil;
import com.ylzx.common.core.page.TableDataInfo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;


/**
 * 标注Controller
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Tag(name = "标注管理", description = "标注相关的API接口")
@RestController
@RequestMapping("/annotation/annotations")
public class AnnotationAnnotationsController extends BaseController
{
    @Resource
    private AnnotationAnnotationsService annotationAnnotationsService;

    /**
     * 查询标注列表
     */
    @Operation(summary = "查询标注列表", description = "分页查询标注信息列表")
    @PreAuthorize("@ss.hasPermi('system:annotations:list')")
    @GetMapping("/list")
    public TableDataInfo list(@Parameter(description = "查询条件") AnnotationAnnotations annotationAnnotations, @RequestParam(name = "status", required = false) String status)
    {
        if (StringUtils.isEmpty(status))
            annotationAnnotations.setStatus(null);

        startPage();
        List<AnnotationAnnotations> list = annotationAnnotationsService.selectAnnotationAnnotationsList(annotationAnnotations);
        return getDataTable(list);
    }

    /**
     * 导出标注列表
     */
    @ApiOperation("导出标注列表")
    @PreAuthorize("@ss.hasPermi('system:annotations:export')")
    @Log(title = "标注", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ApiParam("查询条件") AnnotationAnnotations annotationAnnotations)
    {
        List<AnnotationAnnotations> list = annotationAnnotationsService.selectAnnotationAnnotationsList(annotationAnnotations);
        ExcelUtil<AnnotationAnnotations> util = new ExcelUtil<AnnotationAnnotations>(AnnotationAnnotations.class);
        util.exportExcel(response, list, "标注数据");
    }

    /**
     * 获取标注详细信息
     */
    @ApiOperation("获取标注详细信息")
    @PreAuthorize("@ss.hasPermi('system:annotations:query')")
    @GetMapping(value = "/{annotationId}")
    public AjaxResult getInfo(@ApiParam("标注ID") @PathVariable("annotationId") Long annotationId)
    {
        return success(annotationAnnotationsService.selectAnnotationAnnotationsByAnnotationId(annotationId));
    }

    /**
     * 新增标注
     */
    @ApiOperation("新增标注")
    @PreAuthorize("@ss.hasPermi('system:annotations:add')")
    @Log(title = "标注", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@ApiParam("标注信息") @RequestBody AnnotationAnnotations annotationAnnotations)
    {
        return toAjax(annotationAnnotationsService.insertAnnotationAnnotations(annotationAnnotations));
    }



    /**
     * 新增标注批量
     */
    @ApiOperation("新增标注批量")
    @PreAuthorize("@ss.hasPermi('system:annotations:add')")
    @Log(title = "标注", businessType = BusinessType.INSERT)
    @PostMapping("/addBatch")
    public AjaxResult addBatch(@ApiParam("标注信息") @RequestBody List<AnnotationAnnotations> annotationAnnotationsList)
    {
        return toAjax(annotationAnnotationsService.insertAnnotationAnnotationsBatch(annotationAnnotationsList));
    }

    /**
     * 修改标注
     */
    @ApiOperation("修改标注")
    @PreAuthorize("@ss.hasPermi('system:annotations:edit')")
    @Log(title = "标注", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@ApiParam("标注信息") @RequestBody AnnotationAnnotations annotationAnnotations)
    {
        return toAjax(annotationAnnotationsService.updateAnnotationAnnotations(annotationAnnotations));
    }

    /**
     * 删除标注
     */
    @ApiOperation("删除标注")
    @PreAuthorize("@ss.hasPermi('system:annotations:remove')")
    @Log(title = "标注", businessType = BusinessType.DELETE)
	@DeleteMapping("/{annotationIds}")
    public AjaxResult remove(@ApiParam("标注ID数组") @PathVariable("annotationIds") Long[] annotationIds)
    {
        return toAjax(annotationAnnotationsService.deleteAnnotationAnnotationsByAnnotationIds(annotationIds));
    }


    @ApiOperation("删除标注")
    @PreAuthorize("@ss.hasPermi('system:annotations:remove')")
    @Log(title = "标注", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public AjaxResult removeByCondition(@RequestBody AnnotationAnnotations annotationAnnotations)
    {
        return toAjax(annotationAnnotationsService.deleteAnnotationAnnotations(annotationAnnotations));
    }




}

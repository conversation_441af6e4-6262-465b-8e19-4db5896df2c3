package com.ylzx.file.config;

import io.minio.MinioClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * MinIO配置类
 * 
 * <AUTHOR>
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class MinioConfig {

    private final MinioProperties minioProperties;

    /**
     * 创建MinIO客户端
     */
    @Bean
    public MinioClient minioClient() {
        try {
            // 验证必要配置
            validateMinioProperties();
            
            log.info("开始初始化MinIO客户端，端点：{}", minioProperties.getEndpoint());
            
            MinioClient client = MinioClient.builder()
                    .endpoint(minioProperties.getEndpoint())
                    .credentials(minioProperties.getAccessKey(), minioProperties.getSecretKey())
                    .build();

            // 设置超时时间
            client.setTimeout(
                    minioProperties.getConnectTimeout(),
                    minioProperties.getWriteTimeout(),
                    minioProperties.getReadTimeout()
            );

            log.info("MinIO客户端初始化成功，端点：{}", minioProperties.getEndpoint());
            return client;
        } catch (Exception e) {
            log.error("MinIO客户端初始化失败", e);
            throw new RuntimeException("MinIO客户端初始化失败", e);
        }
    }
    
    /**
     * 验证MinIO配置
     */
    private void validateMinioProperties() {
        if (minioProperties == null) {
            throw new IllegalArgumentException("MinIO配置不能为空，请检查application.yml中的minio配置");
        }
        
        if (minioProperties.getEndpoint() == null || minioProperties.getEndpoint().trim().isEmpty()) {
            throw new IllegalArgumentException("MinIO endpoint不能为空，请检查application.yml中的minio.endpoint配置");
        }
        
        if (minioProperties.getAccessKey() == null || minioProperties.getAccessKey().trim().isEmpty()) {
            throw new IllegalArgumentException("MinIO access-key不能为空，请检查application.yml中的minio.access-key配置");
        }
        
        if (minioProperties.getSecretKey() == null || minioProperties.getSecretKey().trim().isEmpty()) {
            throw new IllegalArgumentException("MinIO secret-key不能为空，请检查application.yml中的minio.secret-key配置");
        }
        
        log.debug("MinIO配置验证通过: endpoint={}, accessKey={}, bucketName={}", 
                 minioProperties.getEndpoint(), 
                 minioProperties.getAccessKey(),
                 minioProperties.getBucketName());
    }
}

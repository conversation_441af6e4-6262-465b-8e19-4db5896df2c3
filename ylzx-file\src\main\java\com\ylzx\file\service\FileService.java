package com.ylzx.file.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ylzx.file.domain.FileInfo;
import com.ylzx.file.domain.dto.*;
import com.ylzx.file.util.FileUtil;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

/**
 * 文件服务接口
 * 
 * <AUTHOR>
 */
public interface FileService {

    /**
     * 上传文件
     * 
     * @param file 上传的文件
     * @param request 上传请求参数
     * @return 文件上传响应
     */
    FileUploadResponse uploadFile(MultipartFile file, FileUploadRequest request);

    /**
     * 批量上传文件
     * 
     * @param files 上传的文件列表
     * @param request 上传请求参数
     * @return 文件上传响应列表
     */
    List<FileUploadResponse> uploadFiles(List<MultipartFile> files, FileUploadRequest request);

    /**
     * 下载文件
     * 
     * @param fileId 文件ID
     * @return 文件输入流
     */
    InputStream downloadFile(String fileId);

    /**
     * 获取文件信息
     * 
     * @param fileId 文件ID
     * @return 文件信息
     */
    FileInfo getFileInfo(String fileId);

    /**
     * 删除文件
     * 
     * @param fileId 文件ID
     * @return 是否删除成功
     */
    boolean deleteFile(String fileId);

    /**
     * 批量删除文件
     * 
     * @param fileIds 文件ID列表
     * @return 是否删除成功
     */
    boolean deleteFiles(List<String> fileIds);

    /**
     * 查询文件列表
     * 
     * @param request 查询请求参数
     * @return 分页文件列表
     */
    IPage<FileInfo> queryFiles(FileQueryRequest request);

    /**
     * 移动文件夹
     * 
     * @param request 文件夹操作请求
     * @return 是否操作成功
     */
    boolean moveFolder(FolderOperationRequest request);

    /**
     * 压缩文件夹
     * 
     * @param request 文件夹操作请求
     * @return 压缩文件信息
     */
    FileUploadResponse compressFolder(FolderOperationRequest request);

    /**
     * 获取文件预签名URL
     * 
     * @param fileId 文件ID
     * @return 预签名URL
     */
    String getPresignedUrl(String fileId);

    /**
     * 检查文件是否存在
     *
     * @param md5Hash 文件MD5哈希值
     * @return 文件信息（如果存在）
     */
    FileInfo checkFileExists(String md5Hash);

    /**
     * 获取图片文件的尺寸信息
     *
     * @param fileId 文件ID
     * @return 图片尺寸信息
     */
    FileUtil.ImageDimensions getImageDimensions(String fileId);

    /**
     * 获取文件夹中的文件列表
     *
     * @param folderPath 文件夹路径
     * @return 文件列表
     */
    List<FileInfo> listFolderFiles(String folderPath);

    /**
     * 列出MinIO存储桶中指定目录下的压缩文件
     *
     * @param folderPath 文件夹路径
     * @return 压缩文件信息列表
     */
    List<FileInfo> listArchiveFilesInMinIO(String folderPath);

    /**
     * 复制文件
     * 
     * @param sourceFileId 源文件ID
     * @param targetPath 目标路径
     * @param newFileName 新文件名（可选）
     * @return 新文件信息
     */
    FileUploadResponse copyFile(String sourceFileId, String targetPath, String newFileName);
}

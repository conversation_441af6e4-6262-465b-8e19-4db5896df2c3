package com.ylzx.file.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import java.util.List;

/**
 * 文件夹操作请求DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "文件夹操作请求")
public class FolderOperationRequest {

    /**
     * 源文件夹路径
     */
    @Schema(description = "源文件夹路径", example = "images/old_folder")
    @NotBlank(message = "源文件夹路径不能为空")
    @Pattern(regexp = "^[a-zA-Z0-9/_-]+$", message = "文件夹路径只能包含字母、数字、下划线、中划线和斜杠")
    private String sourcePath;

    /**
     * 目标文件夹路径（用于移动操作）
     */
    @Schema(description = "目标文件夹路径", example = "images/new_folder")
    @Pattern(regexp = "^[a-zA-Z0-9/_-]*$", message = "文件夹路径只能包含字母、数字、下划线、中划线和斜杠")
    private String targetPath;

    /**
     * 压缩文件名（用于压缩操作）
     */
    @Schema(description = "压缩文件名", example = "backup_20240101.zip")
    private String zipFileName;

    /**
     * 是否包含子文件夹
     */
    @Schema(description = "是否包含子文件夹", example = "true")
    private Boolean includeSubfolders = true;

    /**
     * 是否覆盖已存在的文件
     */
    @Schema(description = "是否覆盖已存在的文件", example = "false")
    private Boolean overwrite = false;

    /**
     * 文件ID列表（用于批量操作指定文件）
     */
    @Schema(description = "文件ID列表")
    private List<String> fileIds;

    /**
     * 操作者
     */
    @Schema(description = "操作者", example = "admin")
    private String operator;

    /**
     * 备注
     */
    @Schema(description = "备注", example = "批量整理文件")
    private String remark;
}

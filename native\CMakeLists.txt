cmake_minimum_required(VERSION 3.16)
project(dataset_export)

# 设置C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 检测编译器类型
if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
    # MinGW/GCC 配置
    message(STATUS "Using MinGW/GCC compiler")
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -O2")
    if(WIN32)
        # MinGW动态链接配置
        set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} -Wl,--enable-auto-import")
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -D_WIN32_WINNT=0x0601")
    endif()
elseif(MSVC)
    # MSVC 静态编译配置
    set(CMAKE_FIND_LIBRARY_SUFFIXES ".a")
    set(BUILD_SHARED_LIBS OFF)
    set(CMAKE_EXE_LINKER_FLAGS "-static")
    set(CMAKE_SHARED_LINKER_FLAGS "-static-libgcc -static-libstdc++")

    if(WIN32)
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -static-libgcc -static-libstdc++")
        set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -static-libgcc")
    endif()
endif()

# 设置编译选项
if(MSVC)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /W3")
else()
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Wextra -O2")
endif()

# 查找JNI
# 手动设置JNI路径
set(JAVA_HOME "C:/java/jdk-21.0.2")
set(JNI_INCLUDE_DIRS "${JAVA_HOME}/include" "${JAVA_HOME}/include/win32")
set(JNI_LIBRARIES "")
set(JNI_FOUND TRUE)
message(STATUS "JNI_INCLUDE_DIRS=${JNI_INCLUDE_DIRS}")
message(STATUS "JNI_LIBRARIES=${JNI_LIBRARIES}")

# 查找OpenCV (可选)
find_package(OpenCV QUIET)
if(OpenCV_FOUND)
    message(STATUS "Found OpenCV ${OpenCV_VERSION}")
    add_definitions(-DUSE_OPENCV)
else()
    message(STATUS "OpenCV not found, using basic image processing")
endif()

# 查找nlohmann/json
find_package(nlohmann_json QUIET)
if(NOT nlohmann_json_FOUND)
    message(STATUS "nlohmann/json not found, will use bundled version")
    # 可以在这里添加下载或使用bundled版本的逻辑
endif()

# 查找MinIO C++ SDK
find_package(miniocpp QUIET)
if(miniocpp_FOUND)
    message(STATUS "Found MinIO C++ SDK via find_package")
    add_definitions(-DUSE_MINIO)
    set(MINIO_FOUND TRUE)
else()
    # 尝试通过pkg-config查找MinIO
    find_package(PkgConfig QUIET)
    if(PkgConfig_FOUND)
        pkg_check_modules(MINIO QUIET minio-cpp)
        if(MINIO_FOUND)
            message(STATUS "Found MinIO C++ SDK via pkg-config")
            add_definitions(-DUSE_MINIO)
            include_directories(${MINIO_INCLUDE_DIRS})
            link_directories(${MINIO_LIBRARY_DIRS})
        endif()
    endif()

    if(NOT MINIO_FOUND)
        # 手动查找MinIO库（适用于vcpkg安装）
        if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU" AND WIN32)
            # MinGW环境下的vcpkg路径
            set(VCPKG_ROOT "$ENV{VCPKG_ROOT}")
            if(NOT VCPKG_ROOT)
                set(VCPKG_ROOT "C:/msys64/home/<USER>/vcpkg")
            endif()

            set(MINIO_INCLUDE_DIR "${VCPKG_ROOT}/installed/x64-mingw-dynamic/include")
            set(MINIO_LIB_DIR "${VCPKG_ROOT}/installed/x64-mingw-dynamic/lib")

            if(EXISTS "${MINIO_INCLUDE_DIR}/miniocpp" OR EXISTS "${MINIO_INCLUDE_DIR}/minio")
                message(STATUS "Found MinIO C++ SDK in vcpkg (MinGW): ${MINIO_INCLUDE_DIR}")
                add_definitions(-DUSE_MINIO)
                include_directories(${MINIO_INCLUDE_DIR})
                link_directories(${MINIO_LIB_DIR})
                set(MINIO_FOUND TRUE)
                set(MINIO_LIBRARIES "minio-cpp")
            endif()
        endif()
    endif()

    if(NOT MINIO_FOUND)
        message(STATUS "MinIO C++ SDK not found, MinIO features will be disabled")
    endif()
endif()

# 包含目录
include_directories(${JNI_INCLUDE_DIRS})
if(JNI_FOUND)
    include_directories(${JAVA_INCLUDE_PATH})
    include_directories(${JAVA_INCLUDE_PATH2})
endif()
include_directories(src)
include_directories(include)
include_directories(third_party)

# 源文件
set(SOURCES
    src/dataset_export_jni.cpp
    src/coco_exporter.cpp
    src/voc_exporter.cpp
    src/yolo_exporter.cpp
    src/image_processor.cpp
    src/image_crop.cpp
    src/minio_client.cpp
    src/utils.cpp
)

# 头文件
set(HEADERS
    include/dataset_export_jni.h
    include/coco_exporter.h
    include/voc_exporter.h
    include/yolo_exporter.h
    include/image_processor.h
    include/image_crop.h
    include/minio_client.h
    include/common.h
)

# 创建动态库
add_library(dataset_export SHARED ${SOURCES} ${HEADERS})

# 链接库
target_link_libraries(dataset_export ${JNI_LIBRARIES})

if(OpenCV_FOUND)
    target_link_libraries(dataset_export ${OpenCV_LIBS})
endif()

if(nlohmann_json_FOUND)
    target_link_libraries(dataset_export nlohmann_json::nlohmann_json)
endif()

if(MINIO_FOUND)
    if(miniocpp_FOUND)
        target_link_libraries(dataset_export miniocpp::miniocpp)
    elseif(MINIO_LIBRARIES)
        target_link_libraries(dataset_export ${MINIO_LIBRARIES})
    else()
        # 手动链接MinIO库（适用于vcpkg MinGW）
        target_link_libraries(dataset_export minio-cpp curl ssl crypto ws2_32 winmm crypt32)
    endif()
endif()

# 链接系统库
if(WIN32)
    if(CMAKE_CXX_COMPILER_ID STREQUAL "GNU")
        # MinGW链接配置
        target_link_libraries(dataset_export ws2_32 winmm)
    else()
        # MSVC静态链接配置
        target_link_libraries(dataset_export -static-libgcc -static-libstdc++ -Wl,-Bstatic -lstdc++ -lpthread -Wl,-Bdynamic)
    endif()
endif()

# 设置输出目录
set_target_properties(dataset_export PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Windows特定设置
if(WIN32)
    set_target_properties(dataset_export PROPERTIES
        PREFIX ""
        SUFFIX ".dll"
    )
endif()

# 安装规则
install(TARGETS dataset_export
    LIBRARY DESTINATION lib
    RUNTIME DESTINATION bin
)

# 复制到Java项目的resources目录
if(EXISTS "${CMAKE_SOURCE_DIR}/../ylzx-annotation/src/main/resources/native")
    add_custom_command(TARGET dataset_export POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy $<TARGET_FILE:dataset_export> 
        "${CMAKE_SOURCE_DIR}/../ylzx-annotation/src/main/resources/native/"
        COMMENT "Copying library to Java resources directory"
    )
endif()

# 生成JNI头文件的自定义目标
add_custom_target(generate_jni_headers
    COMMAND javac -h ${CMAKE_SOURCE_DIR}/include
    -cp "${CMAKE_SOURCE_DIR}/../ylzx-annotation/src/main/java"
    "${CMAKE_SOURCE_DIR}/../ylzx-annotation/src/main/java/com/ylzx/annotation/jni/DatasetExportNative.java"
    COMMENT "Generating JNI headers"
    VERBATIM
)

# 可选：创建MinIO测试程序
if(MINIO_FOUND)
    add_executable(test_minio test-minio.cpp src/minio_client.cpp)
    target_include_directories(test_minio PRIVATE include)

    if(miniocpp_FOUND)
        target_link_libraries(test_minio miniocpp::miniocpp)
    elseif(MINIO_LIBRARIES)
        target_link_libraries(test_minio ${MINIO_LIBRARIES})
    else()
        target_link_libraries(test_minio minio-cpp curl ssl crypto)
        if(WIN32)
            target_link_libraries(test_minio ws2_32 winmm crypt32)
        endif()
    endif()

    message(STATUS "MinIO test program will be built: test_minio")
endif()

# 打印配置信息
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "C++ compiler: ${CMAKE_CXX_COMPILER}")
message(STATUS "C++ flags: ${CMAKE_CXX_FLAGS}")

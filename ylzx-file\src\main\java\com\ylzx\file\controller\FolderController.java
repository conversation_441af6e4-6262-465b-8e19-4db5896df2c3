package com.ylzx.file.controller;

import com.ylzx.common.core.domain.AjaxResult;
import com.ylzx.file.domain.FileInfo;
import com.ylzx.file.domain.dto.FileUploadResponse;
import com.ylzx.file.domain.dto.FolderOperationRequest;
import com.ylzx.file.service.FileService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;

/**
 * 文件夹操作控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/folder")
@RequiredArgsConstructor
@Tag(name = "文件夹管理", description = "文件夹移动、压缩等操作")
public class FolderController {

    private final FileService fileService;

    /**
     * 移动文件夹
     */
    @PostMapping("/move")
    @Operation(summary = "移动文件夹", description = "将文件夹从源路径移动到目标路径")
    public AjaxResult moveFolder(
            @Parameter(description = "文件夹操作请求") @RequestBody @Valid FolderOperationRequest request) {

        try {
            boolean success = fileService.moveFolder(request);
            return success ? AjaxResult.success("文件夹移动成功") : AjaxResult.error("文件夹移动失败");
        } catch (Exception e) {
            log.error("文件夹移动失败", e);
            return AjaxResult.error("文件夹移动失败：" + e.getMessage());
        }
    }

    /**
     * 压缩文件夹
     */
    @PostMapping("/compress")
    @Operation(summary = "压缩文件夹", description = "将文件夹压缩为ZIP文件")
    public AjaxResult compressFolder(
            @Parameter(description = "文件夹操作请求") @RequestBody @Valid FolderOperationRequest request) {

        try {
            FileUploadResponse response = fileService.compressFolder(request);
            if (response != null) {
                return AjaxResult.success("文件夹压缩成功", response);
            } else {
                return AjaxResult.error("文件夹压缩失败");
            }
        } catch (Exception e) {
            log.error("文件夹压缩失败", e);
            return AjaxResult.error("文件夹压缩失败：" + e.getMessage());
        }
    }

    /**
     * 列出文件夹中的文件
     */
    @GetMapping("/list")
    @Operation(summary = "列出文件夹文件", description = "列出指定文件夹中的所有文件")
    public AjaxResult listFolderFiles(
            @Parameter(description = "文件夹路径") @RequestParam String folderPath) {

        try {
            List<FileInfo> files = fileService.listFolderFiles(folderPath);
            AjaxResult result = AjaxResult.success("查询成功", files);
            result.put("total", files.size());
            return result;
        } catch (Exception e) {
            log.error("列出文件夹文件失败", e);
            return AjaxResult.error("列出文件夹文件失败：" + e.getMessage());
        }
    }

    /**
     * 复制文件
     */
    @PostMapping("/copy")
    @Operation(summary = "复制文件", description = "将文件复制到指定路径")
    public AjaxResult copyFile(
            @Parameter(description = "源文件ID") @RequestParam String sourceFileId,
            @Parameter(description = "目标路径") @RequestParam String targetPath,
            @Parameter(description = "新文件名（可选）") @RequestParam(required = false) String newFileName) {

        try {
            FileUploadResponse response = fileService.copyFile(sourceFileId, targetPath, newFileName);
            return AjaxResult.success("文件复制成功", response);
        } catch (Exception e) {
            log.error("文件复制失败", e);
            return AjaxResult.error("文件复制失败：" + e.getMessage());
        }
    }

    /**
     * 检查文件是否存在
     */
    @GetMapping("/check")
    @Operation(summary = "检查文件是否存在", description = "根据MD5哈希值检查文件是否已存在")
    public AjaxResult checkFileExists(
            @Parameter(description = "文件MD5哈希值") @RequestParam String md5Hash) {

        try {
            FileInfo existingFile = fileService.checkFileExists(md5Hash);
            AjaxResult result = AjaxResult.success("检查完成");
            result.put("exists", existingFile != null);
            if (existingFile != null) {
                result.put("data", existingFile);
            }
            return result;
        } catch (Exception e) {
            log.error("检查文件是否存在失败", e);
            return AjaxResult.error("检查文件是否存在失败：" + e.getMessage());
        }
    }
}

package com.ylzx.file.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylzx.file.domain.FileInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文件信息Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface FileInfoMapper extends BaseMapper<FileInfo> {

    /**
     * 根据MD5哈希查询文件
     * 
     * @param md5Hash MD5哈希值
     * @return 文件信息
     */
    FileInfo selectByMd5Hash(@Param("md5Hash") String md5Hash);

    /**
     * 根据文件路径前缀查询文件列表
     * 
     * @param pathPrefix 路径前缀
     * @return 文件列表
     */
    List<FileInfo> selectByPathPrefix(@Param("pathPrefix") String pathPrefix);

    /**
     * 统计文件夹下的文件数量
     * 
     * @param folderPath 文件夹路径
     * @return 文件数量
     */
    Long countByFolderPath(@Param("folderPath") String folderPath);

    /**
     * 统计文件夹下的文件总大小
     * 
     * @param folderPath 文件夹路径
     * @return 文件总大小
     */
    Long sumFileSizeByFolderPath(@Param("folderPath") String folderPath);

    /**
     * 根据文件扩展名统计文件数量
     * 
     * @param extension 文件扩展名
     * @return 文件数量
     */
    Long countByExtension(@Param("extension") String extension);

    /**
     * 批量更新文件路径
     * 
     * @param fileIds 文件ID列表
     * @param oldPathPrefix 旧路径前缀
     * @param newPathPrefix 新路径前缀
     * @return 更新数量
     */
    int batchUpdateFilePath(@Param("fileIds") List<String> fileIds, 
                           @Param("oldPathPrefix") String oldPathPrefix, 
                           @Param("newPathPrefix") String newPathPrefix);

    /**
     * 查询重复文件（相同MD5）
     * 
     * @return 重复文件列表
     */
    List<FileInfo> selectDuplicateFiles();

    /**
     * 查询孤儿文件（数据库有记录但MinIO中不存在的文件）
     * 
     * @param existingPaths MinIO中存在的文件路径列表
     * @return 孤儿文件列表
     */
    List<FileInfo> selectOrphanFiles(@Param("existingPaths") List<String> existingPaths);
}

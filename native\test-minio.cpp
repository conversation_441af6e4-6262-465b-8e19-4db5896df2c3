#include <iostream>
#include <string>

#ifdef USE_MINIO
#include "minio_client.h"
#endif

int main() {
    std::cout << "==================================" << std::endl;
    std::cout << "MinIO Integration Test" << std::endl;
    std::cout << "==================================" << std::endl;

#ifdef USE_MINIO
    std::cout << "✓ MinIO support is compiled in" << std::endl;
    
    try {
        // 测试MinIO客户端创建
        MinioIntegration::MinioClient client;
        std::cout << "✓ MinIO client created successfully" << std::endl;
        
        // 测试配置
        MinioIntegration::MinioConfig config;
        config.endpoint = "http://192.168.101.121:9000";
        config.accessKey = "test-access-key";
        config.secretKey = "test-secret-key";
        config.bucketName = "test-bucket";
        config.useSSL = false;
        config.region = "us-east-1";
        
        std::cout << "✓ MinIO configuration created" << std::endl;
        std::cout << "  Endpoint: " << config.endpoint << std::endl;
        std::cout << "  Bucket: " << config.bucketName << std::endl;
        std::cout << "  SSL: " << (config.useSSL ? "enabled" : "disabled") << std::endl;
        
        // 注意：这里不实际连接，只是测试编译和链接
        std::cout << "✓ MinIO integration test passed" << std::endl;
        std::cout << "  (Note: No actual connection attempted)" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "❌ MinIO test failed: " << e.what() << std::endl;
        return 1;
    }
    
#else
    std::cout << "❌ MinIO support not compiled in" << std::endl;
    std::cout << "Please check:" << std::endl;
    std::cout << "1. MinIO C++ SDK is installed in vcpkg" << std::endl;
    std::cout << "2. CMake found the MinIO libraries" << std::endl;
    std::cout << "3. USE_MINIO preprocessor definition is set" << std::endl;
    return 1;
#endif

    std::cout << "==================================" << std::endl;
    std::cout << "Test completed successfully!" << std::endl;
    std::cout << "==================================" << std::endl;
    
    return 0;
}

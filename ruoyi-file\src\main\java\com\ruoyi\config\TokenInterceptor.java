package com.ruoyi.config;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.jwt.JWT;
import cn.hutool.jwt.JWTUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.handler.HandlerInterceptorAdapter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Component
public class TokenInterceptor extends HandlerInterceptorAdapter {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        //获得放置token的请求头
        String token = request.getParameter("token");
        if (token == null) {
            Map map = new HashMap();
            map.put("code", 500);
            map.put("msg", "token不能为空");
            this.write(map, request, response);
            return false;
        }
        Boolean is;
        try {
            is = JWTUtil.verify(token, "ylzx@2025".getBytes());
        } catch (Exception e) {
            e.printStackTrace();
            is = false;
        }
        if (is == false) {
            Map map = new HashMap();
            map.put("code", 500);
            map.put("msg", "token校验失败");
            this.write(map, request, response);
            return false;
        }

        final JWT jwt = JWTUtil.parseToken(token);
        String expDateStr = (String) jwt.getPayload("expDate");
        if (!DateUtil.date().isBefore(DateUtil.parseDateTime(expDateStr))) {
            Map map = new HashMap();
            map.put("code", 500);
            map.put("msg", "token已过期");
            this.write(map, request, response);
            return false;
        }
        return true;
    }

    public static void write(Object data, HttpServletRequest request, HttpServletResponse response) {
        // 设置允许跨域
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, OPTIONS, PUT, DELETE");
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Headers", "*");
        // 设置编码
        response.setCharacterEncoding("utf-8");
        // 设置请求头编码
        response.setHeader("Content-Type", "text/html;charset=utf-8");
        // 获取写入数据
        String writeData = JSONUtil.toJsonStr(data);
        // 检查是否jsonp请求
        String callback = request.getParameter("callback");
        if (StringUtils.isNotBlank(callback)) {
            response.setContentType("text/javascript;charset=utf-8");
            writeData = callback + "(" + writeData + ");";
        } else {
            response.setContentType("application/json;charset=utf-8");
        }

        PrintWriter out = null;
        try {
            out = response.getWriter();
            out.write(writeData);
        } catch (IOException e) {
            e.printStackTrace();
        }
        assert out != null;
        out.flush();
        out.close();
    }


}

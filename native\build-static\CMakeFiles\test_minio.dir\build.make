# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\msys64\mingw64\bin\cmake.exe

# The command to remove a file.
RM = C:\msys64\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\work\code\java\data-annotation-platform\native

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\work\code\java\data-annotation-platform\native\build-static

# Include any dependencies generated for this target.
include CMakeFiles/test_minio.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/test_minio.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/test_minio.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/test_minio.dir/flags.make

CMakeFiles/test_minio.dir/codegen:
.PHONY : CMakeFiles/test_minio.dir/codegen

CMakeFiles/test_minio.dir/test-minio.cpp.obj: CMakeFiles/test_minio.dir/flags.make
CMakeFiles/test_minio.dir/test-minio.cpp.obj: CMakeFiles/test_minio.dir/includes_CXX.rsp
CMakeFiles/test_minio.dir/test-minio.cpp.obj: C:/work/code/java/data-annotation-platform/native/test-minio.cpp
CMakeFiles/test_minio.dir/test-minio.cpp.obj: CMakeFiles/test_minio.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\work\code\java\data-annotation-platform\native\build-static\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/test_minio.dir/test-minio.cpp.obj"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_minio.dir/test-minio.cpp.obj -MF CMakeFiles\test_minio.dir\test-minio.cpp.obj.d -o CMakeFiles\test_minio.dir\test-minio.cpp.obj -c C:\work\code\java\data-annotation-platform\native\test-minio.cpp

CMakeFiles/test_minio.dir/test-minio.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/test_minio.dir/test-minio.cpp.i"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\work\code\java\data-annotation-platform\native\test-minio.cpp > CMakeFiles\test_minio.dir\test-minio.cpp.i

CMakeFiles/test_minio.dir/test-minio.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/test_minio.dir/test-minio.cpp.s"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\work\code\java\data-annotation-platform\native\test-minio.cpp -o CMakeFiles\test_minio.dir\test-minio.cpp.s

CMakeFiles/test_minio.dir/src/minio_client.cpp.obj: CMakeFiles/test_minio.dir/flags.make
CMakeFiles/test_minio.dir/src/minio_client.cpp.obj: CMakeFiles/test_minio.dir/includes_CXX.rsp
CMakeFiles/test_minio.dir/src/minio_client.cpp.obj: C:/work/code/java/data-annotation-platform/native/src/minio_client.cpp
CMakeFiles/test_minio.dir/src/minio_client.cpp.obj: CMakeFiles/test_minio.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\work\code\java\data-annotation-platform\native\build-static\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/test_minio.dir/src/minio_client.cpp.obj"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/test_minio.dir/src/minio_client.cpp.obj -MF CMakeFiles\test_minio.dir\src\minio_client.cpp.obj.d -o CMakeFiles\test_minio.dir\src\minio_client.cpp.obj -c C:\work\code\java\data-annotation-platform\native\src\minio_client.cpp

CMakeFiles/test_minio.dir/src/minio_client.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/test_minio.dir/src/minio_client.cpp.i"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\work\code\java\data-annotation-platform\native\src\minio_client.cpp > CMakeFiles\test_minio.dir\src\minio_client.cpp.i

CMakeFiles/test_minio.dir/src/minio_client.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/test_minio.dir/src/minio_client.cpp.s"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\work\code\java\data-annotation-platform\native\src\minio_client.cpp -o CMakeFiles\test_minio.dir\src\minio_client.cpp.s

# Object files for target test_minio
test_minio_OBJECTS = \
"CMakeFiles/test_minio.dir/test-minio.cpp.obj" \
"CMakeFiles/test_minio.dir/src/minio_client.cpp.obj"

# External object files for target test_minio
test_minio_EXTERNAL_OBJECTS =

test_minio.exe: CMakeFiles/test_minio.dir/test-minio.cpp.obj
test_minio.exe: CMakeFiles/test_minio.dir/src/minio_client.cpp.obj
test_minio.exe: CMakeFiles/test_minio.dir/build.make
test_minio.exe: C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/libminiocpp.a
test_minio.exe: C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/libcurlpp.a
test_minio.exe: C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/libcurl.a
test_minio.exe: C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/libINIReader.a
test_minio.exe: C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/libinih.a
test_minio.exe: C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/libpugixml.a
test_minio.exe: C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/libssl.a
test_minio.exe: C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/libcrypto.a
test_minio.exe: C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/libzlib.a
test_minio.exe: CMakeFiles/test_minio.dir/linkLibs.rsp
test_minio.exe: CMakeFiles/test_minio.dir/objects1.rsp
test_minio.exe: CMakeFiles/test_minio.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=C:\work\code\java\data-annotation-platform\native\build-static\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Linking CXX executable test_minio.exe"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\test_minio.dir\link.txt --verbose=$(VERBOSE)
	C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file C:/msys64/home/<USER>/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary C:/work/code/java/data-annotation-platform/native/build-static/test_minio.exe -installedDir C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/bin -OutVariable out

# Rule to build all files generated by this target.
CMakeFiles/test_minio.dir/build: test_minio.exe
.PHONY : CMakeFiles/test_minio.dir/build

CMakeFiles/test_minio.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\test_minio.dir\cmake_clean.cmake
.PHONY : CMakeFiles/test_minio.dir/clean

CMakeFiles/test_minio.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" C:\work\code\java\data-annotation-platform\native C:\work\code\java\data-annotation-platform\native C:\work\code\java\data-annotation-platform\native\build-static C:\work\code\java\data-annotation-platform\native\build-static C:\work\code\java\data-annotation-platform\native\build-static\CMakeFiles\test_minio.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/test_minio.dir/depend


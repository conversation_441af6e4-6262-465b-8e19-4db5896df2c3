<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylzx.annotation.mapper.AnnotationProjectsMapper">
    
    <resultMap type="com.ylzx.annotation.domain.AnnotationProjects" id="AnnotationProjectsResult">
        <result property="projectId"    column="project_id"    />
        <result property="name"    column="name"    />
        <result property="description"    column="description"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectAnnotationProjectsVo">
        select project_id, name, description, status, create_time, update_time, create_by, update_by from annotation_projects
    </sql>

    <select id="selectAnnotationProjectsList" parameterType="AnnotationProjects" resultMap="AnnotationProjectsResult">
        <include refid="selectAnnotationProjectsVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like '%' || #{name} || '%'</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="status != null"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectAnnotationProjectsByProjectId" parameterType="Long" resultMap="AnnotationProjectsResult">
        <include refid="selectAnnotationProjectsVo"/>
        where project_id = #{projectId}
    </select>

    <insert id="insertAnnotationProjects" parameterType="AnnotationProjects" useGeneratedKeys="true" keyProperty="projectId">
        insert into annotation_projects
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="description != null">description,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="description != null">#{description},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <update id="updateAnnotationProjects" parameterType="AnnotationProjects">
        update annotation_projects
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="description != null">description = #{description},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where project_id = #{projectId}
    </update>

    <delete id="deleteAnnotationProjectsByProjectId" parameterType="Long">
        delete from annotation_projects where project_id = #{projectId}
    </delete>

    <delete id="deleteAnnotationProjectsByProjectIds" parameterType="String">
        delete from annotation_projects where project_id in 
        <foreach item="projectId" collection="array" open="(" separator="," close=")">
            #{projectId}
        </foreach>
    </delete>
</mapper>
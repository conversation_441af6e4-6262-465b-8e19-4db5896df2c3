#include "dataset_export_jni.h"
#include "coco_exporter.h"
#include "voc_exporter.h"
#include "yolo_exporter.h"
#include "image_processor.h"
#include "image_crop.h"
#include "minio_client.h"
#include <iostream>
#include <stdexcept>

// 版本信息
const char* LIBRARY_VERSION = "1.0.0";

// JNI方法实现
JNIEXPORT jobject JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_exportCOCO
  (JNIEnv *env, jobject obj, jobject jconfig, jobjectArray jimages, jstring jcategories) {
    
    try {
        ExportConfig config = javaConfigToNative(env, jconfig);
        std::vector<ImageInfo> images = javaImagesToNative(env, jimages);
        
        const char* categoriesStr = env->GetStringUTFChars(jcategories, nullptr);
        std::string categories(categoriesStr);
        env->ReleaseStringUTFChars(jcategories, categoriesStr);
        
        COCOExporter exporter;
        ExportResult result = exporter.exportDataset(config, images, categories);
        
        return nativeResultToJava(env, result);
    } catch (const std::exception& e) {
        ExportResult errorResult;
        errorResult.success = false;
        errorResult.message = std::string("COCO导出失败: ") + e.what();
        errorResult.processedCount = 0;
        return nativeResultToJava(env, errorResult);
    }
}

JNIEXPORT jobject JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_exportVOC
  (JNIEnv *env, jobject obj, jobject jconfig, jobjectArray jimages, jstring jcategories) {
    
    try {
        ExportConfig config = javaConfigToNative(env, jconfig);
        std::vector<ImageInfo> images = javaImagesToNative(env, jimages);
        
        const char* categoriesStr = env->GetStringUTFChars(jcategories, nullptr);
        std::string categories(categoriesStr);
        env->ReleaseStringUTFChars(jcategories, categoriesStr);
        
        VOCExporter exporter;
        ExportResult result = exporter.exportDataset(config, images, categories);
        
        return nativeResultToJava(env, result);
    } catch (const std::exception& e) {
        ExportResult errorResult;
        errorResult.success = false;
        errorResult.message = std::string("VOC导出失败: ") + e.what();
        errorResult.processedCount = 0;
        return nativeResultToJava(env, errorResult);
    }
}

JNIEXPORT jobject JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_exportYOLO
  (JNIEnv *env, jobject obj, jobject jconfig, jobjectArray jimages, jstring jcategories) {
    
    try {
        ExportConfig config = javaConfigToNative(env, jconfig);
        std::vector<ImageInfo> images = javaImagesToNative(env, jimages);
        
        const char* categoriesStr = env->GetStringUTFChars(jcategories, nullptr);
        std::string categories(categoriesStr);
        env->ReleaseStringUTFChars(jcategories, categoriesStr);
        
        YOLOExporter exporter;
        ExportResult result = exporter.exportDataset(config, images, categories);
        
        return nativeResultToJava(env, result);
    } catch (const std::exception& e) {
        ExportResult errorResult;
        errorResult.success = false;
        errorResult.message = std::string("YOLO导出失败: ") + e.what();
        errorResult.processedCount = 0;
        return nativeResultToJava(env, errorResult);
    }
}

JNIEXPORT jboolean JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_applyImageTransformations
  (JNIEnv *env, jobject obj, jstring jimagePath, jstring joutputPath, jstring jtransformations) {
    
    try {
        const char* imagePathStr = env->GetStringUTFChars(jimagePath, nullptr);
        const char* outputPathStr = env->GetStringUTFChars(joutputPath, nullptr);
        const char* transformationsStr = env->GetStringUTFChars(jtransformations, nullptr);
        
        std::string imagePath(imagePathStr);
        std::string outputPath(outputPathStr);
        std::string transformations(transformationsStr);
        
        env->ReleaseStringUTFChars(jimagePath, imagePathStr);
        env->ReleaseStringUTFChars(joutputPath, outputPathStr);
        env->ReleaseStringUTFChars(jtransformations, transformationsStr);
        
        ImageProcessor processor;
        return processor.applyTransformations(imagePath, outputPath, transformations);
    } catch (const std::exception& e) {
        std::cerr << "图像变换失败: " << e.what() << std::endl;
        return JNI_FALSE;
    }
}

JNIEXPORT jstring JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_validateConfig
  (JNIEnv *env, jobject obj, jobject jconfig) {
    
    try {
        ExportConfig config = javaConfigToNative(env, jconfig);
        
        // 基本验证逻辑
        if (config.outputPath.empty()) {
            return env->NewStringUTF("输出路径不能为空");
        }
        
        if (config.targetWidth <= 0 || config.targetHeight <= 0) {
            return env->NewStringUTF("目标尺寸必须大于0");
        }
        
        return env->NewStringUTF("配置验证通过");
    } catch (const std::exception& e) {
        return env->NewStringUTF(("配置验证失败: " + std::string(e.what())).c_str());
    }
}

JNIEXPORT jobjectArray JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_getSupportedFormats
  (JNIEnv *env, jobject obj) {
    
    const char* formats[] = {"coco", "voc", "yolo"};
    int formatCount = sizeof(formats) / sizeof(formats[0]);
    
    jclass stringClass = env->FindClass("java/lang/String");
    jobjectArray result = env->NewObjectArray(formatCount, stringClass, nullptr);
    
    for (int i = 0; i < formatCount; i++) {
        jstring format = env->NewStringUTF(formats[i]);
        env->SetObjectArrayElement(result, i, format);
        env->DeleteLocalRef(format);
    }
    
    return result;
}

JNIEXPORT jstring JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_getLibraryVersion
  (JNIEnv *env, jobject obj) {
    return env->NewStringUTF(LIBRARY_VERSION);
}

JNIEXPORT jboolean JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_cleanupTempFiles
  (JNIEnv *env, jobject obj, jstring jtempDir) {
    
    try {
        const char* tempDirStr = env->GetStringUTFChars(jtempDir, nullptr);
        std::string tempDir(tempDirStr);
        env->ReleaseStringUTFChars(jtempDir, tempDirStr);
        
        // 实现临时文件清理逻辑
        // 这里可以添加具体的文件系统操作
        
        return JNI_TRUE;
    } catch (const std::exception& e) {
        std::cerr << "清理临时文件失败: " << e.what() << std::endl;
        return JNI_FALSE;
    }
}

// C++辅助函数实现
ExportConfig javaConfigToNative(JNIEnv* env, jobject jconfig) {
    ExportConfig config;

    jclass configClass = env->GetObjectClass(jconfig);

    // 获取字段ID
    jfieldID exportFormatField = env->GetFieldID(configClass, "exportFormat", "Ljava/lang/String;");
    jfieldID outputPathField = env->GetFieldID(configClass, "outputPath", "Ljava/lang/String;");
    jfieldID targetWidthField = env->GetFieldID(configClass, "targetWidth", "I");
    jfieldID targetHeightField = env->GetFieldID(configClass, "targetHeight", "I");
    jfieldID transformationsField = env->GetFieldID(configClass, "transformations", "Ljava/lang/String;");
    jfieldID grayscaleField = env->GetFieldID(configClass, "grayscale", "Z");
    jfieldID enableMaskField = env->GetFieldID(configClass, "enableMask", "Z");
    jfieldID rotationAngleField = env->GetFieldID(configClass, "rotationAngle", "D");
    jfieldID scaleRatioField = env->GetFieldID(configClass, "scaleRatio", "D");

    // 获取字段值
    jstring jexportFormat = (jstring)env->GetObjectField(jconfig, exportFormatField);
    jstring joutputPath = (jstring)env->GetObjectField(jconfig, outputPathField);
    jstring jtransformations = (jstring)env->GetObjectField(jconfig, transformationsField);

    // 转换字符串
    if (jexportFormat) {
        const char* formatStr = env->GetStringUTFChars(jexportFormat, nullptr);
        config.format = stringToFormat(std::string(formatStr));
        env->ReleaseStringUTFChars(jexportFormat, formatStr);
    }

    if (joutputPath) {
        const char* pathStr = env->GetStringUTFChars(joutputPath, nullptr);
        config.outputPath = std::string(pathStr);
        env->ReleaseStringUTFChars(joutputPath, pathStr);
    }

    if (jtransformations) {
        const char* transStr = env->GetStringUTFChars(jtransformations, nullptr);
        config.transformations = std::string(transStr);
        env->ReleaseStringUTFChars(jtransformations, transStr);
    }

    // 获取基本类型字段
    config.targetWidth = env->GetIntField(jconfig, targetWidthField);
    config.targetHeight = env->GetIntField(jconfig, targetHeightField);
    config.grayscale = env->GetBooleanField(jconfig, grayscaleField);
    config.enableMask = env->GetBooleanField(jconfig, enableMaskField);
    config.rotationAngle = env->GetDoubleField(jconfig, rotationAngleField);
    config.scaleRatio = env->GetDoubleField(jconfig, scaleRatioField);

    return config;
}

std::vector<ImageInfo> javaImagesToNative(JNIEnv* env, jobjectArray jimages) {
    std::vector<ImageInfo> images;

    jsize length = env->GetArrayLength(jimages);
    images.reserve(length);

    for (jsize i = 0; i < length; i++) {
        jobject jimage = env->GetObjectArrayElement(jimages, i);
        jclass imageClass = env->GetObjectClass(jimage);

        // 获取字段ID
        jfieldID imageIdField = env->GetFieldID(imageClass, "imageId", "J");
        jfieldID imagePathField = env->GetFieldID(imageClass, "imagePath", "Ljava/lang/String;");
        jfieldID annotationDataField = env->GetFieldID(imageClass, "annotationData", "Ljava/lang/String;");
        jfieldID datasetTypeField = env->GetFieldID(imageClass, "datasetType", "Ljava/lang/String;");
        jfieldID originalWidthField = env->GetFieldID(imageClass, "originalWidth", "I");
        jfieldID originalHeightField = env->GetFieldID(imageClass, "originalHeight", "I");

        ImageInfo info;
        info.imageId = env->GetLongField(jimage, imageIdField);
        info.originalWidth = env->GetIntField(jimage, originalWidthField);
        info.originalHeight = env->GetIntField(jimage, originalHeightField);

        // 获取字符串字段
        jstring jimagePath = (jstring)env->GetObjectField(jimage, imagePathField);
        jstring jannotationData = (jstring)env->GetObjectField(jimage, annotationDataField);
        jstring jdatasetType = (jstring)env->GetObjectField(jimage, datasetTypeField);

        if (jimagePath) {
            const char* pathStr = env->GetStringUTFChars(jimagePath, nullptr);
            info.imagePath = std::string(pathStr);
            env->ReleaseStringUTFChars(jimagePath, pathStr);
        }

        if (jannotationData) {
            const char* dataStr = env->GetStringUTFChars(jannotationData, nullptr);
            info.annotationData = std::string(dataStr);
            env->ReleaseStringUTFChars(jannotationData, dataStr);
        }

        if (jdatasetType) {
            const char* typeStr = env->GetStringUTFChars(jdatasetType, nullptr);
            info.datasetType = stringToDatasetType(std::string(typeStr));
            env->ReleaseStringUTFChars(jdatasetType, typeStr);
        }

        images.push_back(info);
        env->DeleteLocalRef(jimage);
    }

    return images;
}

jobject nativeResultToJava(JNIEnv* env, const ExportResult& result) {
    jclass resultClass = env->FindClass("com/ylzx/annotation/jni/DatasetExportNative$ExportResult");
    jmethodID constructor = env->GetMethodID(resultClass, "<init>", "()V");
    jobject jresult = env->NewObject(resultClass, constructor);

    // 设置字段
    jfieldID successField = env->GetFieldID(resultClass, "success", "Z");
    jfieldID messageField = env->GetFieldID(resultClass, "message", "Ljava/lang/String;");
    jfieldID processedCountField = env->GetFieldID(resultClass, "processedCount", "I");
    jfieldID outputPathField = env->GetFieldID(resultClass, "outputPath", "Ljava/lang/String;");

    env->SetBooleanField(jresult, successField, result.success);
    env->SetIntField(jresult, processedCountField, result.processedCount);

    jstring jmessage = env->NewStringUTF(result.message.c_str());
    jstring joutputPath = env->NewStringUTF(result.outputPath.c_str());

    env->SetObjectField(jresult, messageField, jmessage);
    env->SetObjectField(jresult, outputPathField, joutputPath);

    return jresult;
}

// ==================== MinIO相关JNI方法实现 ====================

JNIEXPORT jboolean JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_initializeMinioClient
  (JNIEnv *env, jobject obj, jobject jconfig) {
    (void)obj; // 避免未使用参数警告

    try {
        // 获取MinioConfig类
        jclass configClass = env->GetObjectClass(jconfig);

        // 获取字段ID
        jfieldID endpointField = env->GetFieldID(configClass, "endpoint", "Ljava/lang/String;");
        jfieldID accessKeyField = env->GetFieldID(configClass, "accessKey", "Ljava/lang/String;");
        jfieldID secretKeyField = env->GetFieldID(configClass, "secretKey", "Ljava/lang/String;");
        jfieldID bucketNameField = env->GetFieldID(configClass, "bucketName", "Ljava/lang/String;");
        jfieldID useSSLField = env->GetFieldID(configClass, "useSSL", "Z");
        jfieldID regionField = env->GetFieldID(configClass, "region", "Ljava/lang/String;");

        // 获取字段值
        jstring jendpoint = (jstring)env->GetObjectField(jconfig, endpointField);
        jstring jaccessKey = (jstring)env->GetObjectField(jconfig, accessKeyField);
        jstring jsecretKey = (jstring)env->GetObjectField(jconfig, secretKeyField);
        jstring jbucketName = (jstring)env->GetObjectField(jconfig, bucketNameField);
        jboolean juseSSL = env->GetBooleanField(jconfig, useSSLField);
        jstring jregion = (jstring)env->GetObjectField(jconfig, regionField);

        // 转换为C++字符串
        MinioIntegration::MinioConfig config;

        if (jendpoint) {
            const char* str = env->GetStringUTFChars(jendpoint, nullptr);
            config.endpoint = std::string(str);
            env->ReleaseStringUTFChars(jendpoint, str);
        }

        if (jaccessKey) {
            const char* str = env->GetStringUTFChars(jaccessKey, nullptr);
            config.accessKey = std::string(str);
            env->ReleaseStringUTFChars(jaccessKey, str);
        }

        if (jsecretKey) {
            const char* str = env->GetStringUTFChars(jsecretKey, nullptr);
            config.secretKey = std::string(str);
            env->ReleaseStringUTFChars(jsecretKey, str);
        }

        if (jbucketName) {
            const char* str = env->GetStringUTFChars(jbucketName, nullptr);
            config.bucketName = std::string(str);
            env->ReleaseStringUTFChars(jbucketName, str);
        }

        if (jregion) {
            const char* str = env->GetStringUTFChars(jregion, nullptr);
            config.region = std::string(str);
            env->ReleaseStringUTFChars(jregion, str);
        }

        config.useSSL = (juseSSL == JNI_TRUE);

        // 初始化MinIO客户端
        return MinioIntegration::MinioManager::initialize(config) ? JNI_TRUE : JNI_FALSE;

    } catch (const std::exception& e) {
        std::cerr << "MinIO initialization failed: " << e.what() << std::endl;
        return JNI_FALSE;
    }
}

JNIEXPORT jboolean JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_smartCropMinioToMinio
  (JNIEnv *env, jobject obj, jstring jsourceBucket, jstring jsourceObject,
   jstring jtargetBucket, jstring jtargetObject, jstring jannotationCoordinates, jobject jcropConfig) {
    (void)obj; // 避免未使用参数警告

    try {
        // 转换字符串参数
        const char* sourceBucketStr = env->GetStringUTFChars(jsourceBucket, nullptr);
        const char* sourceObjectStr = env->GetStringUTFChars(jsourceObject, nullptr);
        const char* targetBucketStr = env->GetStringUTFChars(jtargetBucket, nullptr);
        const char* targetObjectStr = env->GetStringUTFChars(jtargetObject, nullptr);
        const char* annotationCoordinatesStr = env->GetStringUTFChars(jannotationCoordinates, nullptr);

        std::string sourceBucket(sourceBucketStr);
        std::string sourceObject(sourceObjectStr);
        std::string targetBucket(targetBucketStr);
        std::string targetObject(targetObjectStr);
        std::string annotationCoordinates(annotationCoordinatesStr);

        // 释放字符串资源
        env->ReleaseStringUTFChars(jsourceBucket, sourceBucketStr);
        env->ReleaseStringUTFChars(jsourceObject, sourceObjectStr);
        env->ReleaseStringUTFChars(jtargetBucket, targetBucketStr);
        env->ReleaseStringUTFChars(jtargetObject, targetObjectStr);
        env->ReleaseStringUTFChars(jannotationCoordinates, annotationCoordinatesStr);

        // 转换裁剪配置
        ImageCrop::CropConfig config;
        jclass configClass = env->GetObjectClass(jcropConfig);

        jfieldID targetWidthField = env->GetFieldID(configClass, "targetWidth", "I");
        jfieldID targetHeightField = env->GetFieldID(configClass, "targetHeight", "I");
        jfieldID paddingField = env->GetFieldID(configClass, "padding", "I");
        jfieldID enableRandomPlacementField = env->GetFieldID(configClass, "enableRandomPlacement", "Z");
        jfieldID maintainAspectRatioField = env->GetFieldID(configClass, "maintainAspectRatio", "Z");

        config.targetWidth = env->GetIntField(jcropConfig, targetWidthField);
        config.targetHeight = env->GetIntField(jcropConfig, targetHeightField);
        config.padding = env->GetIntField(jcropConfig, paddingField);
        config.enableRandomPlacement = env->GetBooleanField(jcropConfig, enableRandomPlacementField) == JNI_TRUE;
        config.maintainAspectRatio = env->GetBooleanField(jcropConfig, maintainAspectRatioField) == JNI_TRUE;

        // 执行MinIO到MinIO的裁剪
        ImageCrop::ImageCropper cropper;
        ImageCrop::CropResult result = cropper.smartCropMinioToMinio(
            sourceBucket, sourceObject, targetBucket, targetObject, annotationCoordinates, config);

        return result.success ? JNI_TRUE : JNI_FALSE;

    } catch (const std::exception& e) {
        std::cerr << "MinIO to MinIO crop failed: " << e.what() << std::endl;
        return JNI_FALSE;
    }
}

JNIEXPORT jbyteArray JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_smartCropFromMemory
  (JNIEnv *env, jobject obj, jbyteArray jimageData, jstring jannotationCoordinates, jobject jcropConfig) {
    (void)obj; // 避免未使用参数警告

    try {
        // 获取输入图像数据
        jsize dataLength = env->GetArrayLength(jimageData);
        jbyte* dataPtr = env->GetByteArrayElements(jimageData, nullptr);

        std::vector<uint8_t> imageData(dataPtr, dataPtr + dataLength);
        env->ReleaseByteArrayElements(jimageData, dataPtr, JNI_ABORT);

        // 获取标注坐标
        const char* annotationCoordinatesStr = env->GetStringUTFChars(jannotationCoordinates, nullptr);
        std::string annotationCoordinates(annotationCoordinatesStr);
        env->ReleaseStringUTFChars(jannotationCoordinates, annotationCoordinatesStr);

        // 转换裁剪配置
        ImageCrop::CropConfig config;
        jclass configClass = env->GetObjectClass(jcropConfig);

        jfieldID targetWidthField = env->GetFieldID(configClass, "targetWidth", "I");
        jfieldID targetHeightField = env->GetFieldID(configClass, "targetHeight", "I");
        jfieldID paddingField = env->GetFieldID(configClass, "padding", "I");
        jfieldID enableRandomPlacementField = env->GetFieldID(configClass, "enableRandomPlacement", "Z");
        jfieldID maintainAspectRatioField = env->GetFieldID(configClass, "maintainAspectRatio", "Z");

        config.targetWidth = env->GetIntField(jcropConfig, targetWidthField);
        config.targetHeight = env->GetIntField(jcropConfig, targetHeightField);
        config.padding = env->GetIntField(jcropConfig, paddingField);
        config.enableRandomPlacement = env->GetBooleanField(jcropConfig, enableRandomPlacementField) == JNI_TRUE;
        config.maintainAspectRatio = env->GetBooleanField(jcropConfig, maintainAspectRatioField) == JNI_TRUE;

        // 执行内存裁剪
        ImageCrop::ImageCropper cropper;
        std::vector<uint8_t> outputData;
        ImageCrop::CropResult result = cropper.smartCropFromMemory(imageData, annotationCoordinates, config, outputData);

        if (!result.success) {
            return nullptr;
        }

        // 创建Java字节数组
        jbyteArray joutputData = env->NewByteArray(outputData.size());
        env->SetByteArrayRegion(joutputData, 0, outputData.size(), reinterpret_cast<const jbyte*>(outputData.data()));

        return joutputData;

    } catch (const std::exception& e) {
        std::cerr << "Memory crop failed: " << e.what() << std::endl;
        return nullptr;
    }
}

// 辅助函数：转换Java字符串数组到C++向量
std::vector<std::string> javaStringArrayToVector(JNIEnv* env, jobjectArray jarray) {
    std::vector<std::string> result;
    jsize length = env->GetArrayLength(jarray);

    for (jsize i = 0; i < length; i++) {
        jstring jstr = (jstring)env->GetObjectArrayElement(jarray, i);
        if (jstr) {
            const char* str = env->GetStringUTFChars(jstr, nullptr);
            result.push_back(std::string(str));
            env->ReleaseStringUTFChars(jstr, str);
        }
        env->DeleteLocalRef(jstr);
    }

    return result;
}

JNIEXPORT jobject JNICALL Java_com_ylzx_annotation_jni_DatasetExportNative_batchSmartCropMinioToMinio
  (JNIEnv *env, jobject obj, jobjectArray jsourceBuckets, jobjectArray jsourceObjects,
   jobjectArray jtargetBuckets, jobjectArray jtargetObjects, jobjectArray jannotationCoordinatesArray,
   jobject jcropConfig, jint jthreadCount) {
    (void)obj; // 避免未使用参数警告

    try {
        // 转换Java数组到C++向量
        std::vector<std::string> sourceBuckets = javaStringArrayToVector(env, jsourceBuckets);
        std::vector<std::string> sourceObjects = javaStringArrayToVector(env, jsourceObjects);
        std::vector<std::string> targetBuckets = javaStringArrayToVector(env, jtargetBuckets);
        std::vector<std::string> targetObjects = javaStringArrayToVector(env, jtargetObjects);
        std::vector<std::string> annotationCoordinatesArray = javaStringArrayToVector(env, jannotationCoordinatesArray);

        // 转换裁剪配置
        ImageCrop::CropConfig config;
        jclass configClass = env->GetObjectClass(jcropConfig);

        jfieldID targetWidthField = env->GetFieldID(configClass, "targetWidth", "I");
        jfieldID targetHeightField = env->GetFieldID(configClass, "targetHeight", "I");
        jfieldID paddingField = env->GetFieldID(configClass, "padding", "I");
        jfieldID enableRandomPlacementField = env->GetFieldID(configClass, "enableRandomPlacement", "Z");
        jfieldID maintainAspectRatioField = env->GetFieldID(configClass, "maintainAspectRatio", "Z");

        config.targetWidth = env->GetIntField(jcropConfig, targetWidthField);
        config.targetHeight = env->GetIntField(jcropConfig, targetHeightField);
        config.padding = env->GetIntField(jcropConfig, paddingField);
        config.enableRandomPlacement = env->GetBooleanField(jcropConfig, enableRandomPlacementField) == JNI_TRUE;
        config.maintainAspectRatio = env->GetBooleanField(jcropConfig, maintainAspectRatioField) == JNI_TRUE;

        // 执行批量MinIO到MinIO的裁剪
        ImageCrop::ImageCropper cropper;
        ImageCrop::BatchCropResult result = cropper.batchSmartCropMinioToMinio(
            sourceBuckets, sourceObjects, targetBuckets, targetObjects,
            annotationCoordinatesArray, config, jthreadCount);

        // 创建Java BatchCropResult对象
        jclass resultClass = env->FindClass("com/ylzx/annotation/jni/DatasetExportNative$BatchCropResult");
        jmethodID constructor = env->GetMethodID(resultClass, "<init>", "()V");
        jobject jresult = env->NewObject(resultClass, constructor);

        // 设置字段
        jfieldID successField = env->GetFieldID(resultClass, "success", "Z");
        jfieldID messageField = env->GetFieldID(resultClass, "message", "Ljava/lang/String;");
        jfieldID totalCountField = env->GetFieldID(resultClass, "totalCount", "I");
        jfieldID successCountField = env->GetFieldID(resultClass, "successCount", "I");
        jfieldID failedCountField = env->GetFieldID(resultClass, "failedCount", "I");

        env->SetBooleanField(jresult, successField, result.successCount > 0 ? JNI_TRUE : JNI_FALSE);
        env->SetIntField(jresult, totalCountField, result.totalCount);
        env->SetIntField(jresult, successCountField, result.successCount);
        env->SetIntField(jresult, failedCountField, result.failedCount);

        jstring jmessage = env->NewStringUTF(result.message.c_str());
        env->SetObjectField(jresult, messageField, jmessage);

        return jresult;

    } catch (const std::exception& e) {
        std::cerr << "Batch MinIO to MinIO crop failed: " << e.what() << std::endl;

        // 返回失败结果
        jclass resultClass = env->FindClass("com/ylzx/annotation/jni/DatasetExportNative$BatchCropResult");
        jmethodID constructor = env->GetMethodID(resultClass, "<init>", "()V");
        jobject jresult = env->NewObject(resultClass, constructor);

        jfieldID successField = env->GetFieldID(resultClass, "success", "Z");
        jfieldID messageField = env->GetFieldID(resultClass, "message", "Ljava/lang/String;");

        env->SetBooleanField(jresult, successField, JNI_FALSE);
        jstring jmessage = env->NewStringUTF(("Exception: " + std::string(e.what())).c_str());
        env->SetObjectField(jresult, messageField, jmessage);

        return jresult;
    }
}

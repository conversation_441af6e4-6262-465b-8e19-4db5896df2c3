package com.ruoyi.web.service;

import com.ruoyi.common.core.domain.R;
import com.ruoyi.system.api.domain.SysFile;
import com.ruoyi.system.api.domain.vo.SysFileSearchVO;
import com.ruoyi.web.domain.FileInfoEntity;
import org.dromara.x.file.storage.core.FileInfo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 系统文件服务接口
 */
public interface SysFileService {
    
    /**
     * 根据文件拥有者id获取文件（单个文件，取获取到的第一条）
     * 
     * @param ownerId 文件拥有者的id
     * @return 文件信息，没有则为null
     */
    R<SysFile> getFile(String ownerId);
    
    /**
     * 根据文件拥有者id获取文件列表
     * 
     * @param ownerId 文件拥有者id
     * @return 文件列表，没有则为空list
     */
    R<List<SysFile>> findFiles(String ownerId);

    R<SysFile> getFileByFileId(String fileId);

    /**
     * 根据文件拥有者id列表获取文件列表
     * 
     * @param ownerIds 文件拥有者id列表
     * @return 文件列表，没有则为空list
     */
    R<List<SysFile>> findFilesByOwnerIds(List<String> ownerIds);
    
    /**
     * 上传文件
     *
     * @param file 文件
     * @param sysFileSearchVO 附带参数
     * @return 返回文件信息
     * @throws IOException IO异常
     */
    R<SysFile> upload(MultipartFile file, SysFileSearchVO sysFileSearchVO) throws IOException;
    
    /**
     * 生成唯一文件名
     *
     * @param file 文件
     * @return 唯一文件名
     */
    String generateUniqueFilename(MultipartFile file);
    
    /**
     * 处理非静态资源
     *
     * @param file 文件
     * @param sysFileSearchVO 附带参数
     * @return 文件信息
     */
    FileInfo handleNonStaticResource(MultipartFile file, SysFileSearchVO sysFileSearchVO);
    
    /**
     * 生成文件URL
     *
     * @param fileInfo 文件信息
     * @param sysFileSearchVO 附带参数
     * @return 文件URL
     */
    String generateFileUrl(FileInfo fileInfo, SysFileSearchVO sysFileSearchVO);
    
    /**
     * 创建文件信息
     *
     * @param file 文件
     * @param platform 平台
     * @param path 路径
     * @param filename 文件名
     * @return 文件信息
     */
    FileInfo createFileInfo(MultipartFile file, String platform, String path, String filename);
    
    /**
     * 判断是否为图片
     *
     * @param file 文件
     * @return 是否为图片
     */
    boolean isImage(MultipartFile file);
    
    /**
     * 填充文件信息实体
     *
     * @param fileInfoEntity 文件信息实体
     * @param fileInfo 文件信息
     * @param sysFileSearchVO 附带参数
     * @param url 文件URL
     * @param thumbUrl 缩略图URL
     */
    void fillFileInfoEntity(FileInfoEntity fileInfoEntity, FileInfo fileInfo, SysFileSearchVO sysFileSearchVO, String url, String thumbUrl);
    
    /**
     * 构造文件URL
     *
     * @param domainHost 域名主机
     * @param fileUrl 文件URL
     * @return 完整URL
     */
    String constructFileUrl(String domainHost, String fileUrl);
    
    /**
     * 根据文件id读取文件流
     * 
     * @param response HTTP响应对象
     * @param id 文件ID
     * @throws Exception 异常信息
     */
    void getFileById(HttpServletResponse response, String id) throws Exception;
    
    /**
     * 根据文件拥有者id删除文件
     * 
     * @param ownerId 文件所有者id
     * @return 操作结果
     */
    R<?> removeFilesByOwner(String ownerId);
    
    /**
     * 获取附件信息并合并图片
     * 
     * @param req HTTP请求对象
     * @param res HTTP响应对象
     * @param ownerId 文件所有者id
     * @param width 图片宽度
     * @param height 图片高度
     * @param columns 列数
     * @param gutter 间距
     * @param padding 内边距
     * @param quality 图片质量
     * @throws IOException IO异常
     */
    void images(HttpServletRequest req, HttpServletResponse res, String ownerId, 
                Integer width, Integer height, Integer columns, Integer gutter, 
                Integer padding, Float quality) throws IOException;

    /**
     * 根据fileId列表获取文件列表
     * 
     * @param fileIdList fileId列表
     * @return 文件列表
     */
    R<List<SysFile>> findFilesByFileIdList(List<String> fileIdList);
} 
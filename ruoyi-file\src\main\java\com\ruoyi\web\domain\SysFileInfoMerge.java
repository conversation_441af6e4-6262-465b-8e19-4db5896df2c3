package com.ruoyi.web.domain;

import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.io.Serial;
import java.io.Serializable;

@TableName("sys_file_info_merge")
@KeySequence("owner_id,sign_name")
@EqualsAndHashCode(callSuper = false)
@ToString
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SysFileInfoMerge implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * owner_id - 联合主键之一
     */
    @TableField("owner_id")
    private String ownerId;

    /**
     * sign_name - 联合主键之二
     */
    @TableField("sign_name")
    private String signName;

    @TableField("file_name")
    private String fileName;
}

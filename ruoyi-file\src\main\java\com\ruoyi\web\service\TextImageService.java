package com.ruoyi.web.service;


import com.ruoyi.system.api.domain.ImageTextRequest;

import java.awt.Color;
import java.io.IOException;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;

/**
 * 文本图片生成服务接口
 *
 * <AUTHOR>
 */
public interface TextImageService {

    /**
     * 生成文字图片
     *
     * @param text             要生成的文字
     * @param fontSize         字体大小
     * @param width            图片宽度
     * @param height           图片高度
     * @param forceDoubleLines 强制双行显示
     * @param textColor        文本颜色
     * @param backgroundColor  背景颜色
     * @param fontName         字体名称
     * @param addShadow        是否添加阴影
     * @param output           输出流
     * @throws IOException IO异常
     */
    void generateTextImage(String text, int fontSize, int width, int height,
                           boolean forceDoubleLines, String textColor, String backgroundColor,
                           String fontName, boolean addShadow, OutputStream output) throws IOException;

    /**
     * 解析颜色字符串为Color对象
     *
     * @param colorStr 颜色字符串，例如"#FF0000"
     * @return Color对象
     */
    Color parseColor(String colorStr);

    /**
     * 合并多个图片/文本为一个大图
     * 图片会等比例缩放并居中显示，文本会生成为图片，所有图片使用统一大小549*489
     *
     * @param request         ImageTextRequest对象
     * @param itemWidth       每个小图宽度（固定为549）
     * @param itemHeight      每个小图高度（固定为489）
     * @param fontSize        字体大小
     * @param textColor       文本颜色
     * @param backgroundColor 背景颜色
     * @param fontName        字体名称
     * @param addShadow       是否添加阴影
     * @return                生成的图片URL
     * @throws IOException IO异常
     */
    String mergeImagesAndTexts(ImageTextRequest request,
                             int itemWidth, int itemHeight, int fontSize, String textColor,
                             String backgroundColor, String fontName, boolean addShadow) throws IOException;

    /**
     * 预生成图片并缓存
     * 用于提前生成图片并存入缓存，提高实际请求时的响应速度
     *
     * @param requests 图片生成请求列表
     * @param targetWidth 目标宽度
     * @param targetHeight 目标高度
     * @param fontSize 字体大小
     * @param textColor 文字颜色
     * @param backgroundColor 背景颜色
     * @param fontName 字体名称
     * @param addShadow 是否添加阴影
     * @return 预生成的图片数量
     */
    int preGenerateImagesCache(List<ImageTextRequest> requests, int targetWidth, int targetHeight,
                             int fontSize, String textColor, String backgroundColor, String fontName,
                             boolean addShadow);

    Map<String, String> batchGetUrls(ImageTextRequest request);
} 
package com.ylzx.annotation.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ylzx.annotation.domain.enums.AnnotationStatus;
import com.ylzx.common.annotation.Excel;
import com.ylzx.common.core.domain.BaseEntity;
import lombok.*;

import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 标注对象 annotation_annotations
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class AnnotationAnnotations extends BaseEntity
{
    @Serial
    private static final long serialVersionUID = 1L;

    /** 标注主键 */
    private Long annotationId;

    /** 标注分类主键 */
    @Excel(name = "标注分类主键")
    private Long categoryId;

    /** 标注图片主键 */
    @Excel(name = "标注图片主键")
    private Long imageId;

    /** 标注标签主键 */
    @Excel(name = "标注标签主键")
    private Long labelId;

    /** 标注形状类型 0:矩形 1:多边形 */
    @Excel(name = "标注形状类型")
    private String shapeType;

    /** 标注坐标 */
    @Excel(name = "标注坐标")
    private String coordinates;

    /** 标注状态 0:未标注 1:标注中 2:待审核 3:审核通过 4:审核不通过 */
    @Excel(name = "标注状态", readConverterExp = "0=未标注,1=标注中,2=待审核,3=审核通过,4=审核不通过")
    @Builder.Default
    private AnnotationStatus status = AnnotationStatus.PENDING_REVIEW;

    /** 删除标志 0:未删除 1:已删除 */
    @Builder.Default
    private String delFlag = "0";

    /** 标注人 */
    private String labeler;

    /** 标注时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime labelTime;



    /** 以下是非数据库字段 **/
    @TableField(exist = false)
    private String categoryName;
    @TableField(exist = false)
    private String labelName;
}

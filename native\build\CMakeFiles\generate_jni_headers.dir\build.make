# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\msys64\mingw64\bin\cmake.exe

# The command to remove a file.
RM = C:\msys64\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\work\code\java\data-annotation-platform\native

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\work\code\java\data-annotation-platform\native\build

# Utility rule file for generate_jni_headers.

# Include any custom commands dependencies for this target.
include CMakeFiles/generate_jni_headers.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/generate_jni_headers.dir/progress.make

CMakeFiles/generate_jni_headers:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=C:\work\code\java\data-annotation-platform\native\build\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Generating JNI headers"
	javac -h C:/work/code/java/data-annotation-platform/native/include -cp C:/work/code/java/data-annotation-platform/native/../ylzx-annotation/src/main/java C:/work/code/java/data-annotation-platform/native/../ylzx-annotation/src/main/java/com/ylzx/annotation/jni/DatasetExportNative.java

CMakeFiles/generate_jni_headers.dir/codegen:
.PHONY : CMakeFiles/generate_jni_headers.dir/codegen

generate_jni_headers: CMakeFiles/generate_jni_headers
generate_jni_headers: CMakeFiles/generate_jni_headers.dir/build.make
.PHONY : generate_jni_headers

# Rule to build all files generated by this target.
CMakeFiles/generate_jni_headers.dir/build: generate_jni_headers
.PHONY : CMakeFiles/generate_jni_headers.dir/build

CMakeFiles/generate_jni_headers.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\generate_jni_headers.dir\cmake_clean.cmake
.PHONY : CMakeFiles/generate_jni_headers.dir/clean

CMakeFiles/generate_jni_headers.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" C:\work\code\java\data-annotation-platform\native C:\work\code\java\data-annotation-platform\native C:\work\code\java\data-annotation-platform\native\build C:\work\code\java\data-annotation-platform\native\build C:\work\code\java\data-annotation-platform\native\build\CMakeFiles\generate_jni_headers.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/generate_jni_headers.dir/depend


package com.ylzx.annotation.service;

import com.ylzx.annotation.domain.AnnotationAnnotations;
import com.ylzx.annotation.domain.AnnotationImages;

import java.util.Date;
import java.util.List;

/**
 * MinIO图像处理服务接口
 * 提供基于MinIO的图像预处理和裁剪功能
 * 
 * <AUTHOR>
 */
public interface MinioImageProcessService {

    /**
     * 预处理项目图片
     * 根据项目配置和分类，处理审核通过的图片
     * 
     * @param projectId 项目ID
     * @param updateTimeThreshold 更新时间阈值，只处理此时间之后更新的图片
     * @return 处理结果
     */
    ProcessResult preprocessProjectImages(Long projectId, Date updateTimeThreshold);

    /**
     * 根据标注坐标裁剪图片（MinIO到MinIO）
     * 
     * @param image 图片信息
     * @param annotations 标注列表
     * @param outputBucket 输出存储桶
     * @param outputObject 输出对象名
     * @param cropConfig 裁剪配置
     * @return 处理结果
     */
    ProcessResult cropImageByAnnotations(AnnotationImages image, List<AnnotationAnnotations> annotations,
                                       String outputBucket, String outputObject, CropConfig cropConfig);

    /**
     * 批量裁剪图片（MinIO到MinIO）
     * 
     * @param imageAnnotationPairs 图片和标注对列表
     * @param outputBucketPrefix 输出存储桶前缀
     * @param cropConfig 裁剪配置
     * @return 批量处理结果
     */
    BatchProcessResult batchCropImages(List<ImageAnnotationPair> imageAnnotationPairs,
                                     String outputBucketPrefix, CropConfig cropConfig);

    /**
     * 处理结果
     */
    class ProcessResult {
        private boolean success;
        private String message;
        private int totalCount;
        private int successCount;
        private int failedCount;
        private long processingTimeMs;

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }

        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }

        public int getTotalCount() { return totalCount; }
        public void setTotalCount(int totalCount) { this.totalCount = totalCount; }

        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }

        public int getFailedCount() { return failedCount; }
        public void setFailedCount(int failedCount) { this.failedCount = failedCount; }

        public long getProcessingTimeMs() { return processingTimeMs; }
        public void setProcessingTimeMs(long processingTimeMs) { this.processingTimeMs = processingTimeMs; }
    }

    /**
     * 批量处理结果
     */
    class BatchProcessResult extends ProcessResult {
        private List<String> outputPaths;
        private List<String> failedPaths;
        private List<String> errorMessages;

        // Getters and Setters
        public List<String> getOutputPaths() { return outputPaths; }
        public void setOutputPaths(List<String> outputPaths) { this.outputPaths = outputPaths; }

        public List<String> getFailedPaths() { return failedPaths; }
        public void setFailedPaths(List<String> failedPaths) { this.failedPaths = failedPaths; }

        public List<String> getErrorMessages() { return errorMessages; }
        public void setErrorMessages(List<String> errorMessages) { this.errorMessages = errorMessages; }
    }

    /**
     * 图片和标注对
     */
    class ImageAnnotationPair {
        private AnnotationImages image;
        private List<AnnotationAnnotations> annotations;

        public ImageAnnotationPair() {}

        public ImageAnnotationPair(AnnotationImages image, List<AnnotationAnnotations> annotations) {
            this.image = image;
            this.annotations = annotations;
        }

        // Getters and Setters
        public AnnotationImages getImage() { return image; }
        public void setImage(AnnotationImages image) { this.image = image; }

        public List<AnnotationAnnotations> getAnnotations() { return annotations; }
        public void setAnnotations(List<AnnotationAnnotations> annotations) { this.annotations = annotations; }
    }

    /**
     * 裁剪配置
     */
    class CropConfig {
        private Integer targetWidth;
        private Integer targetHeight;
        private int padding = 20;
        private boolean enableRandomPlacement = true;
        private boolean maintainAspectRatio = true;
        private int[] backgroundColor = {128, 128, 128};

        // Getters and Setters
        public Integer getTargetWidth() { return targetWidth; }
        public void setTargetWidth(Integer targetWidth) { this.targetWidth = targetWidth; }

        public Integer getTargetHeight() { return targetHeight; }
        public void setTargetHeight(Integer targetHeight) { this.targetHeight = targetHeight; }

        public int getPadding() { return padding; }
        public void setPadding(int padding) { this.padding = padding; }

        public boolean isEnableRandomPlacement() { return enableRandomPlacement; }
        public void setEnableRandomPlacement(boolean enableRandomPlacement) { this.enableRandomPlacement = enableRandomPlacement; }

        public boolean isMaintainAspectRatio() { return maintainAspectRatio; }
        public void setMaintainAspectRatio(boolean maintainAspectRatio) { this.maintainAspectRatio = maintainAspectRatio; }

        public int[] getBackgroundColor() { return backgroundColor; }
        public void setBackgroundColor(int[] backgroundColor) { this.backgroundColor = backgroundColor; }
    }
}

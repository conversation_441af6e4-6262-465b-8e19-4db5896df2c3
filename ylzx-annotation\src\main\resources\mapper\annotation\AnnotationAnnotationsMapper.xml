<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylzx.annotation.mapper.AnnotationAnnotationsMapper">

    <resultMap type="com.ylzx.annotation.domain.AnnotationAnnotations" id="AnnotationAnnotationsResult">
        <result property="annotationId" column="annotation_id"/>
        <result property="categoryId" column="category_id"/>
        <result property="imageId" column="image_id"/>
        <result property="labelId" column="label_id"/>
        <result property="shapeType" column="shape_type"/>
        <result property="coordinates" column="coordinates"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="labeler" column="labeler"/>
        <result property="labelTime" column="label_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectAnnotationAnnotationsVo">
        select annotation_id,
               category_id,
               image_id,
               label_id,
               shape_type,
               coordinates,
               status,
               del_flag,
               labeler,
               label_time,
               create_by,
               create_time,
               update_by,
               update_time
        from annotation_annotations
    </sql>

    <select id="selectAnnotationAnnotationsList" parameterType="com.ylzx.annotation.domain.AnnotationAnnotations"
            resultMap="AnnotationAnnotationsResult">
        <include refid="selectAnnotationAnnotationsVo"/>
        <where>
            and del_flag = '0'
            <if test="categoryId != null ">and category_id = #{categoryId}</if>
            <if test="imageId != null ">and image_id = #{imageId}</if>
            <if test="labelId != null ">and label_id = #{labelId}</if>
            <if test="status != null">and status = #{status}</if>
        </where>
    </select>

    <select id="selectAnnotationAnnotationsByAnnotationId" parameterType="Long" resultMap="AnnotationAnnotationsResult">
        <include refid="selectAnnotationAnnotationsVo"/>
        where annotation_id = #{annotationId} and del_flag = '0'
    </select>

    <insert id="insertAnnotationAnnotations" parameterType="com.ylzx.annotation.domain.AnnotationAnnotations"
            useGeneratedKeys="true" keyProperty="annotationId">
        insert into annotation_annotations
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">category_id,</if>
            <if test="imageId != null">image_id,</if>
            <if test="labelId != null">label_id,</if>
            <if test="shapeType != null and shapeType != ''">shape_type,</if>
            <if test="coordinates != null and coordinates != ''">coordinates,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="labeler != null">labeler,</if>
            <if test="labelTime != null">label_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryId != null">#{categoryId},</if>
            <if test="imageId != null">#{imageId},</if>
            <if test="labelId != null">#{labelId},</if>
            <if test="shapeType != null and shapeType != ''">#{shapeType},</if>
            <if test="coordinates != null and coordinates != ''">#{coordinates},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},</if>
            <if test="labeler != null">#{labeler},</if>
            <if test="labelTime != null">#{labelTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">now(),</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">now(),</if>
        </trim>
    </insert>


    <update id="updateAnnotationAnnotations" parameterType="com.ylzx.annotation.domain.AnnotationAnnotations">
        update annotation_annotations
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="imageId != null">image_id = #{imageId},</if>
            <if test="labelId != null">label_id = #{labelId},</if>
            <if test="shapeType != null and shapeType != ''">shape_type = #{shapeType},</if>
            <if test="coordinates != null and coordinates != ''">coordinates = #{coordinates},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null and delFlag != ''">del_flag = #{delFlag},</if>
            <if test="labeler != null">labeler = #{labeler},</if>
            <if test="labelTime != null">label_time = #{labelTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = now(),</if>
        </trim>
        where annotation_id = #{annotationId}
    </update>

    <update id="updateAnnotationAnnotationsStatus" parameterType="com.ylzx.annotation.domain.AnnotationAnnotations">
        update annotation_annotations
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = now(),</if>
        </trim>
        where annotation_id = #{annotationId}
    </update>

    <update id="logicDeleteAnnotationAnnotationsByAnnotationId" parameterType="Long">
        update annotation_annotations
        set del_flag = '1'
        where annotation_id = #{annotationId}
    </update>

    <update id="logicDeleteAnnotationAnnotationsByAnnotationIds" parameterType="Long">
        update annotation_annotations set del_flag = '1' where annotation_id in
        <foreach item="annotationId" collection="array" open="(" separator="," close=")">
            #{annotationId}
        </foreach>
    </update>


    <delete id="deleteAnnotationAnnotations">
        delete from annotation_annotations
        <where>
            <if test="annotationId != null ">and annotation_id = #{annotationId}</if>
            <if test="categoryId != null ">and category_id = #{categoryId}</if>
            <if test="imageId != null ">and image_id = #{imageId}</if>
            <if test="labelId != null ">and label_id = #{labelId}</if>
        </where>
    </delete>

    <select id="checkOverallStatusInSql" resultType="com.ylzx.annotation.domain.AnnotationAnnotations">
        WITH target_info AS (SELECT image_id, category_id
                             FROM annotation_annotations
                             WHERE annotation_id = #{annotationId}
                               AND del_flag = '0'),
             relevant_statuses AS (SELECT status
                                   FROM annotation_annotations
                                   WHERE image_id = (SELECT image_id FROM target_info)
                                     AND category_id = (SELECT category_id FROM target_info)
                                     AND del_flag = '0')
        SELECT (SELECT image_id FROM target_info)    as imageId,
               (SELECT category_id FROM target_info) as categoryId,
               CASE
                   -- 如果找不到相关标注，则默认返回 '3' (审核通过)
                   WHEN (SELECT count(*) FROM relevant_statuses) = 0 THEN '3'
                   -- 如果有任何一个状态为'4'，则整体为审核不通过（最高优先级）
                   WHEN (SELECT bool_or(status = '4') FROM relevant_statuses) THEN '4'
                   -- 如果包含状态'1'或'2'（且根据前面的判断没有'4'），则整体为未审核完
                   WHEN (SELECT bool_or(status = '1' OR status = '2') FROM relevant_statuses) THEN '2'
                   -- 其他所有情况（如仅包含'0'或'3'），均视为审核通过
                   ELSE '3'
                   END                               as status
    </select>


    <insert id="insertAnnotationAnnotationsBatch">
        INSERT INTO annotation_annotations
        <!-- 动态生成字段列表 (基于第一条记录的非空字段) -->
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="list[0].annotationId != null">annotation_id,</if>
            <if test="list[0].categoryId != null">category_id,</if>
            <if test="list[0].imageId != null">image_id,</if>
            <if test="list[0].labelId != null">label_id,</if>
            <if test="list[0].shapeType != null and list[0].shapeType != ''">shape_type,</if>
            <if test="list[0].coordinates != null and list[0].coordinates != ''">coordinates,</if>
            <if test="list[0].status != null">status,</if>
            <if test="list[0].createTime != null">create_time,</if>
            <if test="list[0].updateTime != null">update_time,</if>
            <if test="list[0].createBy != null">create_by,</if>
            <if test="list[0].updateBy != null">update_by,</if>
            <if test="list[0].delFlag != null">del_flag,</if>
            <if test="list[0].labeler != null">labeler,</if>
            <if test="list[0].labelTime != null">label_time,</if>
        </trim>
        VALUES
        <!-- 遍历记录列表 -->
        <foreach collection="list" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="list[0].annotationId != null">#{item.annotationId},</if>
                <if test="list[0].categoryId != null">#{item.categoryId},</if>
                <if test="list[0].imageId != null">#{item.imageId},</if>
                <if test="list[0].labelId != null">#{item.labelId},</if>
                <if test="list[0].shapeType != null and list[0].shapeType != ''">#{item.shapeType},</if>
                <if test="list[0].coordinates != null and list[0].coordinates != ''">#{item.coordinates},</if>
                <if test="list[0].status != null">#{item.status},</if>
                <if test="list[0].createTime != null">#{item.createTime},</if>
                <if test="list[0].updateTime != null">#{item.updateTime},</if>
                <if test="list[0].createBy != null">#{item.createBy},</if>
                <if test="list[0].updateBy != null">#{item.updateBy},</if>
                <if test="list[0].delFlag != null">#{item.delFlag},</if>
                <if test="list[0].labeler != null">#{item.labeler},</if>
                <if test="list[0].labelTime != null">#{item.labelTime},</if>
            </trim>
        </foreach>
    </insert>
</mapper>
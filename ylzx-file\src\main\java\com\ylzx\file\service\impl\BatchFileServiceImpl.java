package com.ylzx.file.service.impl;

import cn.hutool.core.util.StrUtil;
import com.ylzx.file.domain.FileInfo;
import com.ylzx.file.domain.dto.FileUploadRequest;
import com.ylzx.file.domain.dto.FileUploadResponse;
import com.ylzx.file.mapper.FileInfoMapper;
import com.ylzx.file.service.BatchFileService;
import com.ylzx.file.service.FileService;
import com.ylzx.file.util.MinioUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;

import java.util.stream.Collectors;

/**
 * 批量文件处理服务实现类
 * 使用虚线程优化批量操作性能
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BatchFileServiceImpl implements BatchFileService {

    private final FileService fileService;
    private final MinioUtil minioUtil;
    private final FileInfoMapper fileInfoMapper;

    @Override
    public CompletableFuture<List<FileUploadResponse>> uploadFilesAsync(List<MultipartFile> files, FileUploadRequest request) {
        return CompletableFuture.supplyAsync(() -> {
            List<CompletableFuture<FileUploadResponse>> futures = files.stream()
                    .map(file -> CompletableFuture.supplyAsync(() -> {
                        try {
                            return fileService.uploadFile(file, request);
                        } catch (Exception e) {
                            log.error("异步上传文件失败: {}", file.getOriginalFilename(), e);
                            return null;
                        }
                    }))
                    .toList();

            return futures.stream()
                    .map(CompletableFuture::join)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        });
    }

    @Override
    public Map<String, String> getPresignedUrls(List<String> fileIds) {
        if (fileIds == null || fileIds.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, String> result = new ConcurrentHashMap<>();
        
        // 使用虚线程并行处理
        List<CompletableFuture<Void>> futures = fileIds.stream()
                .map(fileId -> CompletableFuture.runAsync(() -> {
                    try {
                        String url = fileService.getPresignedUrl(fileId);
                        if (StrUtil.isNotBlank(url)) {
                            result.put(fileId, url);
                        }
                    } catch (Exception e) {
                        log.error("获取预签名URL失败: {}", fileId, e);
                    }
                }))
                .toList();

        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        
        return result;
    }

    @Override
    public CompletableFuture<Map<String, String>> getPresignedUrlsAsync(List<String> fileIds) {
        return CompletableFuture.supplyAsync(() -> getPresignedUrls(fileIds));
    }

    @Override
    public Map<String, InputStream> downloadFiles(List<String> fileIds) {
        if (fileIds == null || fileIds.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, InputStream> result = new ConcurrentHashMap<>();
        
        // 使用虚线程并行下载
        List<CompletableFuture<Void>> futures = fileIds.stream()
                .map(fileId -> CompletableFuture.runAsync(() -> {
                    try {
                        InputStream inputStream = fileService.downloadFile(fileId);
                        if (inputStream != null) {
                            result.put(fileId, inputStream);
                        }
                    } catch (Exception e) {
                        log.error("下载文件失败: {}", fileId, e);
                    }
                }))
                .toList();

        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        
        return result;
    }

    @Override
    public CompletableFuture<Map<String, InputStream>> downloadFilesAsync(List<String> fileIds) {
        return CompletableFuture.supplyAsync(() -> downloadFiles(fileIds));
    }

    @Override
    public Map<String, FileInfo> checkFilesExist(List<String> md5Hashes) {
        if (md5Hashes == null || md5Hashes.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, FileInfo> result = new ConcurrentHashMap<>();
        
        // 使用虚线程并行检查
        List<CompletableFuture<Void>> futures = md5Hashes.stream()
                .map(md5Hash -> CompletableFuture.runAsync(() -> {
                    try {
                        FileInfo fileInfo = fileService.checkFileExists(md5Hash);
                        if (fileInfo != null) {
                            result.put(md5Hash, fileInfo);
                        }
                    } catch (Exception e) {
                        log.error("检查文件是否存在失败: {}", md5Hash, e);
                    }
                }))
                .toList();

        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
        
        return result;
    }

    @Override
    public CompletableFuture<Map<String, FileInfo>> checkFilesExistAsync(List<String> md5Hashes) {
        return CompletableFuture.supplyAsync(() -> checkFilesExist(md5Hashes));
    }

    @Override
    public List<FileUploadResponse> extractAndUploadFromArchive(MultipartFile archiveFile, String folderPath, String createBy) {
        List<FileUploadResponse> results = new ArrayList<>();

        try {
            // 1. 先上传压缩文件本身
            FileUploadRequest archiveRequest = new FileUploadRequest();
            archiveRequest.setFolderPath(folderPath + "/archives");
            archiveRequest.setCreateBy(createBy);
            archiveRequest.setRemark("压缩包文件");

            FileUploadResponse archiveResponse = fileService.uploadFile(archiveFile, archiveRequest);
            results.add(archiveResponse);

            // 2. 解压并上传内部文件
            Map<String, InputStream> extractedFiles = extractArchiveFiles(archiveFile);

            for (Map.Entry<String, InputStream> entry : extractedFiles.entrySet()) {
                try {
                    String fileName = entry.getKey();
                    InputStream fileStream = entry.getValue();

                    // 跳过目录条目
                    if (fileName.endsWith("/")) {
                        continue;
                    }

                    // 创建上传请求
                    FileUploadRequest request = new FileUploadRequest();
                    request.setFolderPath(folderPath + "/extracted");
                    request.setCreateBy(createBy);
                    request.setRemark("从压缩包解压的文件");

                    // 创建临时文件进行上传
                    byte[] fileBytes = fileStream.readAllBytes();
                    MultipartFile extractedFile = new SimpleMultipartFile(fileName, fileBytes);

                    FileUploadResponse response = fileService.uploadFile(extractedFile, request);
                    results.add(response);

                } catch (Exception e) {
                    log.error("上传解压文件失败: {}", entry.getKey(), e);
                } finally {
                    try {
                        entry.getValue().close();
                    } catch (IOException e) {
                        log.warn("关闭文件流失败", e);
                    }
                }
            }

            log.info("压缩文件解压上传完成，共处理 {} 个文件", results.size());
            return results;

        } catch (Exception e) {
            log.error("压缩文件解压上传失败", e);
            throw new RuntimeException("压缩文件解压上传失败: " + e.getMessage(), e);
        }
    }

    /**
     * 解压压缩文件
     */
    private Map<String, InputStream> extractArchiveFiles(MultipartFile archiveFile) throws IOException {
        Map<String, InputStream> extractedFiles = new HashMap<>();
        String fileName = archiveFile.getOriginalFilename();

        if (fileName == null) {
            throw new IllegalArgumentException("压缩文件名不能为空");
        }

        if (fileName.toLowerCase().endsWith(".zip")) {
            extractZipFiles(archiveFile.getInputStream(), extractedFiles);
        } else if (fileName.toLowerCase().endsWith(".tar.gz") || fileName.toLowerCase().endsWith(".tgz")) {
            extractTarGzFiles(archiveFile.getInputStream(), extractedFiles);
        } else {
            throw new IllegalArgumentException("不支持的压缩文件格式: " + fileName);
        }

        return extractedFiles;
    }

    /**
     * 解压ZIP文件
     */
    private void extractZipFiles(InputStream inputStream, Map<String, InputStream> extractedFiles) throws IOException {
        try (ZipInputStream zipInputStream = new ZipInputStream(inputStream, StandardCharsets.UTF_8)) {
            ZipEntry entry;
            while ((entry = zipInputStream.getNextEntry()) != null) {
                if (!entry.isDirectory()) {
                    ByteArrayOutputStream baos = new ByteArrayOutputStream();
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = zipInputStream.read(buffer)) != -1) {
                        baos.write(buffer, 0, bytesRead);
                    }
                    extractedFiles.put(entry.getName(), new ByteArrayInputStream(baos.toByteArray()));
                }
                zipInputStream.closeEntry();
            }
        }
    }

    /**
     * 解压TAR.GZ文件
     */
    private void extractTarGzFiles(InputStream inputStream, Map<String, InputStream> extractedFiles) throws IOException {
        try (GzipCompressorInputStream gzipInputStream = new GzipCompressorInputStream(inputStream);
             TarArchiveInputStream tarInputStream = new TarArchiveInputStream(gzipInputStream)) {

            TarArchiveEntry entry;
            while ((entry = tarInputStream.getNextTarEntry()) != null) {
                if (!entry.isDirectory()) {
                    ByteArrayOutputStream baos = new ByteArrayOutputStream();
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = tarInputStream.read(buffer)) != -1) {
                        baos.write(buffer, 0, bytesRead);
                    }
                    extractedFiles.put(entry.getName(), new ByteArrayInputStream(baos.toByteArray()));
                }
            }
        }
    }

    @Override
    public CompletableFuture<List<FileUploadResponse>> extractAndUploadFromArchiveAsync(MultipartFile archiveFile, String folderPath, String createBy) {
        return CompletableFuture.supplyAsync(() -> extractAndUploadFromArchive(archiveFile, folderPath, createBy));
    }

    @Override
    public List<FileInfo> getFileInfos(List<String> fileIds) {
        if (fileIds == null || fileIds.isEmpty()) {
            return new ArrayList<>();
        }

        // 批量查询数据库
        return fileInfoMapper.selectBatchIds(fileIds);
    }

    @Override
    public CompletableFuture<List<FileInfo>> getFileInfosAsync(List<String> fileIds) {
        return CompletableFuture.supplyAsync(() -> getFileInfos(fileIds));
    }

    /**
     * 简单的MultipartFile实现
     */
    private static class SimpleMultipartFile implements MultipartFile {
        private final String name;
        private final byte[] content;

        public SimpleMultipartFile(String name, byte[] content) {
            this.name = name;
            this.content = content;
        }

        @Override
        public String getName() {
            return "file";
        }

        @Override
        public String getOriginalFilename() {
            return name;
        }

        @Override
        public String getContentType() {
            return "application/octet-stream";
        }

        @Override
        public boolean isEmpty() {
            return content.length == 0;
        }

        @Override
        public long getSize() {
            return content.length;
        }

        @Override
        public byte[] getBytes() throws IOException {
            return content;
        }

        @Override
        public InputStream getInputStream() throws IOException {
            return new ByteArrayInputStream(content);
        }

        @Override
        public void transferTo(File dest) throws IOException, IllegalStateException {
            try (FileOutputStream fos = new FileOutputStream(dest)) {
                fos.write(content);
            }
        }
    }
}

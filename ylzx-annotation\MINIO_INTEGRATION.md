# MinIO集成说明

本文档说明了ylzx-annotation项目如何集成MinIO文件存储服务。

## 概述

原来的文件存储方式是将文件保存在本地文件系统中，现在改为使用MinIO对象存储，通过ylzx-file服务进行文件管理。

## 主要变更

### 1. 数据库表结构变更

#### annotation_image_sources 表
- 新增 `file_id` 字段：存储MinIO中的文件ID，替代原来的 `path` 字段
- 新增 `archive_file_id` 字段：存储压缩包的文件ID，替代原来的 `archive_path` 字段

#### annotation_images 表
- 新增 `file_id` 字段：存储MinIO中的文件ID，替代原来的 `storage_path` 字段

### 2. 服务层变更

#### AnnotationImageSourcesService
- 文件上传改为调用ylzx-file服务
- 不再直接操作本地文件系统
- 使用file_id来标识文件

#### AnnotationImagesService
- 新增预签名URL获取方法
- 支持批量获取预签名URL
- 为前端提供安全的文件访问链接

### 3. 控制器变更

#### AnnotationImagesController
- 图片列表接口返回带有预签名URL的DTO
- 新增获取预签名URL的API接口
- 支持批量获取预签名URL

## 使用方式

### 1. 启动服务

确保以下服务正常运行：
1. MinIO服务（默认端口9000）
2. ylzx-file服务（默认端口8083）
3. ylzx-annotation服务

### 2. 文件上传

```java
// 上传数据源文件
AnnotationImageSources source = annotationImageSourcesService.uploadSource(file);
```

上传流程：
1. 计算文件哈希值，检查重复
2. 调用ylzx-file服务上传文件到MinIO
3. 保存文件ID到数据库
4. 处理压缩包中的图片

### 3. 获取图片URL

```java
// 获取单个图片的预签名URL
String url = annotationImagesService.getImagePresignedUrl(imageId);

// 批量获取预签名URL
Map<String, String> urlMap = annotationImagesService.getImagePresignedUrls(imageIds);
```

### 4. 前端显示图片

前端通过API获取图片列表时，每个图片对象都包含 `presignedUrl` 字段：

```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "imageId": 1,
      "imageName": "example.jpg",
      "presignedUrl": "http://localhost:9000/data-annotation/...",
      "fileId": "abc123"
    }
  ]
}
```

## API接口

### 获取图片预签名URL

```http
GET /annotation/images/presigned-url/{imageId}
```

响应：
```json
{
  "code": 200,
  "msg": "获取预签名URL成功",
  "data": "http://localhost:9000/data-annotation/..."
}
```

### 批量获取预签名URL

```http
POST /annotation/images/presigned-urls
Content-Type: application/json

[1, 2, 3, 4, 5]
```

响应：
```json
{
  "code": 200,
  "msg": "获取预签名URL成功",
  "data": {
    "1": "http://localhost:9000/data-annotation/...",
    "2": "http://localhost:9000/data-annotation/...",
    "3": "http://localhost:9000/data-annotation/..."
  }
}
```

## 配置说明

### ylzx-file服务配置

在 `application.yml` 中配置MinIO连接信息：

```yaml
minio:
  endpoint: http://localhost:9000
  access-key: minioadmin
  secret-key: minioadmin
  bucket-name: data-annotation
  secure: false
```

### ylzx-annotation服务配置

添加ylzx-file依赖：

```xml
<dependency>
    <groupId>com.ylzx</groupId>
    <artifactId>ylzx-file</artifactId>
    <version>3.9.0</version>
</dependency>
```

## 数据迁移

如果需要从现有的本地文件系统迁移到MinIO：

1. 执行数据库表结构变更脚本：`sql/migrate_to_minio.sql`
2. 将现有文件上传到MinIO
3. 更新数据库中的file_id字段
4. 验证迁移结果
5. 删除旧的path字段（可选）

## 注意事项

1. **安全性**：预签名URL有过期时间，默认1小时
2. **性能**：批量获取预签名URL比单个获取更高效
3. **错误处理**：如果MinIO服务不可用，会返回null或空URL
4. **缓存**：可以考虑对预签名URL进行缓存以提高性能
5. **监控**：建议监控ylzx-file服务的健康状态

## 故障排除

### 常见问题

1. **预签名URL获取失败**
   - 检查ylzx-file服务是否正常运行
   - 检查MinIO服务是否可访问
   - 检查file_id是否正确

2. **文件上传失败**
   - 检查文件大小是否超过限制
   - 检查MinIO存储空间是否充足
   - 检查网络连接是否正常

3. **图片显示异常**
   - 检查预签名URL是否过期
   - 检查浏览器是否支持跨域访问
   - 检查MinIO的访问策略配置

# MinIO集成指南

本指南介绍如何在数据标注平台中使用MinIO直接处理图像的新功能。

## 概述

新的MinIO集成功能允许：
- 直接从MinIO下载图像进行处理
- 处理后的图像直接上传到MinIO
- 消除临时文件的需要
- 提高处理性能和减少磁盘I/O

## 安装依赖

### 1. 安装vcpkg（如果尚未安装）

```powershell
# 克隆vcpkg
git clone https://github.com/Microsoft/vcpkg.git C:\vcpkg
cd C:\vcpkg

# 运行bootstrap脚本
.\bootstrap-vcpkg.bat

# 集成到Visual Studio
.\vcpkg integrate install
```

### 2. 安装必要的依赖包

```powershell
# 安装基础依赖
.\vcpkg install curl:x64-windows
.\vcpkg install openssl:x64-windows
.\vcpkg install pugixml:x64-windows
.\vcpkg install opencv:x64-windows
.\vcpkg install nlohmann-json:x64-windows
```

### 3. 安装MinIO C++ SDK

由于MinIO C++ SDK可能不在vcpkg官方仓库中，需要手动编译：

```powershell
# 克隆MinIO C++ SDK
git clone https://github.com/minio/minio-cpp.git
cd minio-cpp

# 创建构建目录
mkdir build && cd build

# 配置CMake
cmake .. -DCMAKE_TOOLCHAIN_FILE=C:\vcpkg\scripts\buildsystems\vcpkg.cmake

# 编译
cmake --build . --config Release

# 安装
cmake --install .
```

## 编译C++库

### 使用提供的构建脚本

```powershell
cd native
.\build-with-minio.bat
```

### 手动编译

```powershell
cd native\build

# 配置CMake
cmake .. -DCMAKE_TOOLCHAIN_FILE=C:\vcpkg\scripts\buildsystems\vcpkg.cmake -DCMAKE_BUILD_TYPE=Release

# 编译
cmake --build . --config Release
```

## 配置应用程序

### 1. MinIO配置

在`application.yml`中配置MinIO连接信息：

```yaml
minio:
  endpoint: http://***************:9000
  access-key: your-access-key
  secret-key: your-secret-key
  bucket-name: annotation-data
  secure: false
```

### 2. 启用MinIO处理

确保在启动时MinIO客户端正确初始化。`MinioImageProcessServiceImpl`会在`@PostConstruct`中自动初始化。

## 使用新功能

### 1. 预处理项目图片

```java
@Autowired
private MinioImageProcessService minioImageProcessService;

// 预处理项目图片
Date threshold = new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000); // 24小时前
ProcessResult result = minioImageProcessService.preprocessProjectImages(projectId, threshold);
```

### 2. 单张图片裁剪

```java
// 准备裁剪配置
CropConfig cropConfig = new CropConfig();
cropConfig.setTargetWidth(512);
cropConfig.setTargetHeight(512);
cropConfig.setPadding(20);

// 执行裁剪
ProcessResult result = minioImageProcessService.cropImageByAnnotations(
    image, annotations, "output-bucket", "output/path.jpg", cropConfig);
```

### 3. 批量图片裁剪

```java
// 准备图片和标注对
List<ImageAnnotationPair> pairs = new ArrayList<>();
pairs.add(new ImageAnnotationPair(image1, annotations1));
pairs.add(new ImageAnnotationPair(image2, annotations2));

// 执行批量裁剪
BatchProcessResult result = minioImageProcessService.batchCropImages(
    pairs, "projects/1/processed", cropConfig);
```

## API接口

### 预处理项目图片

```http
POST /annotation/minio-process/preprocess/{projectId}?updateTimeThreshold=1234567890000
```

### 测试MinIO连接

```http
GET /annotation/minio-process/test-connection
```

### 获取项目处理状态

```http
GET /annotation/minio-process/status/{projectId}
```

## 架构优势

### 1. 性能提升
- 消除临时文件I/O
- 直接内存处理
- 并行处理支持

### 2. 存储优化
- 减少本地磁盘使用
- 统一的云存储管理
- 自动清理机制

### 3. 可扩展性
- 支持分布式处理
- 易于水平扩展
- 云原生架构

## 故障排除

### 1. MinIO连接失败

检查配置：
```yaml
minio:
  endpoint: http://your-minio-server:9000  # 确保地址正确
  access-key: your-access-key              # 确保密钥正确
  secret-key: your-secret-key
```

### 2. C++库加载失败

确保：
- `dataset_export.dll`在正确位置
- 所有依赖库已安装
- JNI路径配置正确

### 3. 图像处理失败

检查：
- 图像格式是否支持
- 标注坐标是否有效
- MinIO对象是否存在

## 性能调优

### 1. 批处理大小

```java
// 推荐批处理大小：200-500张图片
int batchSize = 200;
```

### 2. 线程数配置

```java
// 根据CPU核心数调整
int threadCount = Math.min(8, Runtime.getRuntime().availableProcessors());
```

### 3. 内存管理

```java
// 大批量处理时分批进行
for (int i = 0; i < totalImages; i += batchSize) {
    List<ImageAnnotationPair> batch = images.subList(i, Math.min(i + batchSize, totalImages.size()));
    processBatch(batch);
}
```

## 监控和日志

### 1. 处理进度监控

```java
// 实现处理进度回调
public interface ProcessProgressCallback {
    void onProgress(int processed, int total);
    void onComplete(ProcessResult result);
    void onError(String error);
}
```

### 2. 性能指标

- 处理速度（图片/秒）
- 内存使用量
- MinIO传输速度
- 错误率

## 未来扩展

### 1. 支持更多图像格式
- WebP
- TIFF
- RAW格式

### 2. 高级处理功能
- 图像增强
- 自动标注
- 质量检测

### 3. 分布式处理
- 多节点协调
- 负载均衡
- 故障恢复

## 总结

新的MinIO集成功能提供了：
- ✅ 完全的云原生图像处理
- ✅ 高性能的批量处理
- ✅ 简化的部署和维护
- ✅ 可扩展的架构设计

通过这个集成，你的数据标注平台现在可以高效地处理大规模图像数据，而无需依赖本地文件系统。

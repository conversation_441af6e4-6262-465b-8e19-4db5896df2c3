# CMAKE generated file: DO NOT EDIT!
# Generated by "MinGW Makefiles" Generator, CMake Version 4.1

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

SHELL = cmd.exe

# The CMake executable.
CMAKE_COMMAND = C:\msys64\mingw64\bin\cmake.exe

# The command to remove a file.
RM = C:\msys64\mingw64\bin\cmake.exe -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = C:\work\code\java\data-annotation-platform\native

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = C:\work\code\java\data-annotation-platform\native\build-static

# Include any dependencies generated for this target.
include CMakeFiles/dataset_export.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/dataset_export.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/dataset_export.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/dataset_export.dir/flags.make

CMakeFiles/dataset_export.dir/codegen:
.PHONY : CMakeFiles/dataset_export.dir/codegen

CMakeFiles/dataset_export.dir/src/dataset_export_jni.cpp.obj: CMakeFiles/dataset_export.dir/flags.make
CMakeFiles/dataset_export.dir/src/dataset_export_jni.cpp.obj: CMakeFiles/dataset_export.dir/includes_CXX.rsp
CMakeFiles/dataset_export.dir/src/dataset_export_jni.cpp.obj: C:/work/code/java/data-annotation-platform/native/src/dataset_export_jni.cpp
CMakeFiles/dataset_export.dir/src/dataset_export_jni.cpp.obj: CMakeFiles/dataset_export.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\work\code\java\data-annotation-platform\native\build-static\CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/dataset_export.dir/src/dataset_export_jni.cpp.obj"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/dataset_export.dir/src/dataset_export_jni.cpp.obj -MF CMakeFiles\dataset_export.dir\src\dataset_export_jni.cpp.obj.d -o CMakeFiles\dataset_export.dir\src\dataset_export_jni.cpp.obj -c C:\work\code\java\data-annotation-platform\native\src\dataset_export_jni.cpp

CMakeFiles/dataset_export.dir/src/dataset_export_jni.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/dataset_export.dir/src/dataset_export_jni.cpp.i"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\work\code\java\data-annotation-platform\native\src\dataset_export_jni.cpp > CMakeFiles\dataset_export.dir\src\dataset_export_jni.cpp.i

CMakeFiles/dataset_export.dir/src/dataset_export_jni.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/dataset_export.dir/src/dataset_export_jni.cpp.s"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\work\code\java\data-annotation-platform\native\src\dataset_export_jni.cpp -o CMakeFiles\dataset_export.dir\src\dataset_export_jni.cpp.s

CMakeFiles/dataset_export.dir/src/coco_exporter.cpp.obj: CMakeFiles/dataset_export.dir/flags.make
CMakeFiles/dataset_export.dir/src/coco_exporter.cpp.obj: CMakeFiles/dataset_export.dir/includes_CXX.rsp
CMakeFiles/dataset_export.dir/src/coco_exporter.cpp.obj: C:/work/code/java/data-annotation-platform/native/src/coco_exporter.cpp
CMakeFiles/dataset_export.dir/src/coco_exporter.cpp.obj: CMakeFiles/dataset_export.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\work\code\java\data-annotation-platform\native\build-static\CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/dataset_export.dir/src/coco_exporter.cpp.obj"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/dataset_export.dir/src/coco_exporter.cpp.obj -MF CMakeFiles\dataset_export.dir\src\coco_exporter.cpp.obj.d -o CMakeFiles\dataset_export.dir\src\coco_exporter.cpp.obj -c C:\work\code\java\data-annotation-platform\native\src\coco_exporter.cpp

CMakeFiles/dataset_export.dir/src/coco_exporter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/dataset_export.dir/src/coco_exporter.cpp.i"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\work\code\java\data-annotation-platform\native\src\coco_exporter.cpp > CMakeFiles\dataset_export.dir\src\coco_exporter.cpp.i

CMakeFiles/dataset_export.dir/src/coco_exporter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/dataset_export.dir/src/coco_exporter.cpp.s"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\work\code\java\data-annotation-platform\native\src\coco_exporter.cpp -o CMakeFiles\dataset_export.dir\src\coco_exporter.cpp.s

CMakeFiles/dataset_export.dir/src/voc_exporter.cpp.obj: CMakeFiles/dataset_export.dir/flags.make
CMakeFiles/dataset_export.dir/src/voc_exporter.cpp.obj: CMakeFiles/dataset_export.dir/includes_CXX.rsp
CMakeFiles/dataset_export.dir/src/voc_exporter.cpp.obj: C:/work/code/java/data-annotation-platform/native/src/voc_exporter.cpp
CMakeFiles/dataset_export.dir/src/voc_exporter.cpp.obj: CMakeFiles/dataset_export.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\work\code\java\data-annotation-platform\native\build-static\CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/dataset_export.dir/src/voc_exporter.cpp.obj"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/dataset_export.dir/src/voc_exporter.cpp.obj -MF CMakeFiles\dataset_export.dir\src\voc_exporter.cpp.obj.d -o CMakeFiles\dataset_export.dir\src\voc_exporter.cpp.obj -c C:\work\code\java\data-annotation-platform\native\src\voc_exporter.cpp

CMakeFiles/dataset_export.dir/src/voc_exporter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/dataset_export.dir/src/voc_exporter.cpp.i"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\work\code\java\data-annotation-platform\native\src\voc_exporter.cpp > CMakeFiles\dataset_export.dir\src\voc_exporter.cpp.i

CMakeFiles/dataset_export.dir/src/voc_exporter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/dataset_export.dir/src/voc_exporter.cpp.s"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\work\code\java\data-annotation-platform\native\src\voc_exporter.cpp -o CMakeFiles\dataset_export.dir\src\voc_exporter.cpp.s

CMakeFiles/dataset_export.dir/src/yolo_exporter.cpp.obj: CMakeFiles/dataset_export.dir/flags.make
CMakeFiles/dataset_export.dir/src/yolo_exporter.cpp.obj: CMakeFiles/dataset_export.dir/includes_CXX.rsp
CMakeFiles/dataset_export.dir/src/yolo_exporter.cpp.obj: C:/work/code/java/data-annotation-platform/native/src/yolo_exporter.cpp
CMakeFiles/dataset_export.dir/src/yolo_exporter.cpp.obj: CMakeFiles/dataset_export.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\work\code\java\data-annotation-platform\native\build-static\CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/dataset_export.dir/src/yolo_exporter.cpp.obj"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/dataset_export.dir/src/yolo_exporter.cpp.obj -MF CMakeFiles\dataset_export.dir\src\yolo_exporter.cpp.obj.d -o CMakeFiles\dataset_export.dir\src\yolo_exporter.cpp.obj -c C:\work\code\java\data-annotation-platform\native\src\yolo_exporter.cpp

CMakeFiles/dataset_export.dir/src/yolo_exporter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/dataset_export.dir/src/yolo_exporter.cpp.i"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\work\code\java\data-annotation-platform\native\src\yolo_exporter.cpp > CMakeFiles\dataset_export.dir\src\yolo_exporter.cpp.i

CMakeFiles/dataset_export.dir/src/yolo_exporter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/dataset_export.dir/src/yolo_exporter.cpp.s"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\work\code\java\data-annotation-platform\native\src\yolo_exporter.cpp -o CMakeFiles\dataset_export.dir\src\yolo_exporter.cpp.s

CMakeFiles/dataset_export.dir/src/image_processor.cpp.obj: CMakeFiles/dataset_export.dir/flags.make
CMakeFiles/dataset_export.dir/src/image_processor.cpp.obj: CMakeFiles/dataset_export.dir/includes_CXX.rsp
CMakeFiles/dataset_export.dir/src/image_processor.cpp.obj: C:/work/code/java/data-annotation-platform/native/src/image_processor.cpp
CMakeFiles/dataset_export.dir/src/image_processor.cpp.obj: CMakeFiles/dataset_export.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\work\code\java\data-annotation-platform\native\build-static\CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/dataset_export.dir/src/image_processor.cpp.obj"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/dataset_export.dir/src/image_processor.cpp.obj -MF CMakeFiles\dataset_export.dir\src\image_processor.cpp.obj.d -o CMakeFiles\dataset_export.dir\src\image_processor.cpp.obj -c C:\work\code\java\data-annotation-platform\native\src\image_processor.cpp

CMakeFiles/dataset_export.dir/src/image_processor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/dataset_export.dir/src/image_processor.cpp.i"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\work\code\java\data-annotation-platform\native\src\image_processor.cpp > CMakeFiles\dataset_export.dir\src\image_processor.cpp.i

CMakeFiles/dataset_export.dir/src/image_processor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/dataset_export.dir/src/image_processor.cpp.s"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\work\code\java\data-annotation-platform\native\src\image_processor.cpp -o CMakeFiles\dataset_export.dir\src\image_processor.cpp.s

CMakeFiles/dataset_export.dir/src/image_crop.cpp.obj: CMakeFiles/dataset_export.dir/flags.make
CMakeFiles/dataset_export.dir/src/image_crop.cpp.obj: CMakeFiles/dataset_export.dir/includes_CXX.rsp
CMakeFiles/dataset_export.dir/src/image_crop.cpp.obj: C:/work/code/java/data-annotation-platform/native/src/image_crop.cpp
CMakeFiles/dataset_export.dir/src/image_crop.cpp.obj: CMakeFiles/dataset_export.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\work\code\java\data-annotation-platform\native\build-static\CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/dataset_export.dir/src/image_crop.cpp.obj"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/dataset_export.dir/src/image_crop.cpp.obj -MF CMakeFiles\dataset_export.dir\src\image_crop.cpp.obj.d -o CMakeFiles\dataset_export.dir\src\image_crop.cpp.obj -c C:\work\code\java\data-annotation-platform\native\src\image_crop.cpp

CMakeFiles/dataset_export.dir/src/image_crop.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/dataset_export.dir/src/image_crop.cpp.i"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\work\code\java\data-annotation-platform\native\src\image_crop.cpp > CMakeFiles\dataset_export.dir\src\image_crop.cpp.i

CMakeFiles/dataset_export.dir/src/image_crop.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/dataset_export.dir/src/image_crop.cpp.s"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\work\code\java\data-annotation-platform\native\src\image_crop.cpp -o CMakeFiles\dataset_export.dir\src\image_crop.cpp.s

CMakeFiles/dataset_export.dir/src/minio_client.cpp.obj: CMakeFiles/dataset_export.dir/flags.make
CMakeFiles/dataset_export.dir/src/minio_client.cpp.obj: CMakeFiles/dataset_export.dir/includes_CXX.rsp
CMakeFiles/dataset_export.dir/src/minio_client.cpp.obj: C:/work/code/java/data-annotation-platform/native/src/minio_client.cpp
CMakeFiles/dataset_export.dir/src/minio_client.cpp.obj: CMakeFiles/dataset_export.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\work\code\java\data-annotation-platform\native\build-static\CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/dataset_export.dir/src/minio_client.cpp.obj"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/dataset_export.dir/src/minio_client.cpp.obj -MF CMakeFiles\dataset_export.dir\src\minio_client.cpp.obj.d -o CMakeFiles\dataset_export.dir\src\minio_client.cpp.obj -c C:\work\code\java\data-annotation-platform\native\src\minio_client.cpp

CMakeFiles/dataset_export.dir/src/minio_client.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/dataset_export.dir/src/minio_client.cpp.i"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\work\code\java\data-annotation-platform\native\src\minio_client.cpp > CMakeFiles\dataset_export.dir\src\minio_client.cpp.i

CMakeFiles/dataset_export.dir/src/minio_client.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/dataset_export.dir/src/minio_client.cpp.s"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\work\code\java\data-annotation-platform\native\src\minio_client.cpp -o CMakeFiles\dataset_export.dir\src\minio_client.cpp.s

CMakeFiles/dataset_export.dir/src/utils.cpp.obj: CMakeFiles/dataset_export.dir/flags.make
CMakeFiles/dataset_export.dir/src/utils.cpp.obj: CMakeFiles/dataset_export.dir/includes_CXX.rsp
CMakeFiles/dataset_export.dir/src/utils.cpp.obj: C:/work/code/java/data-annotation-platform/native/src/utils.cpp
CMakeFiles/dataset_export.dir/src/utils.cpp.obj: CMakeFiles/dataset_export.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=C:\work\code\java\data-annotation-platform\native\build-static\CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/dataset_export.dir/src/utils.cpp.obj"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/dataset_export.dir/src/utils.cpp.obj -MF CMakeFiles\dataset_export.dir\src\utils.cpp.obj.d -o CMakeFiles\dataset_export.dir\src\utils.cpp.obj -c C:\work\code\java\data-annotation-platform\native\src\utils.cpp

CMakeFiles/dataset_export.dir/src/utils.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/dataset_export.dir/src/utils.cpp.i"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E C:\work\code\java\data-annotation-platform\native\src\utils.cpp > CMakeFiles\dataset_export.dir\src\utils.cpp.i

CMakeFiles/dataset_export.dir/src/utils.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/dataset_export.dir/src/utils.cpp.s"
	C:\msys64\mingw64\bin\g++.exe $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S C:\work\code\java\data-annotation-platform\native\src\utils.cpp -o CMakeFiles\dataset_export.dir\src\utils.cpp.s

# Object files for target dataset_export
dataset_export_OBJECTS = \
"CMakeFiles/dataset_export.dir/src/dataset_export_jni.cpp.obj" \
"CMakeFiles/dataset_export.dir/src/coco_exporter.cpp.obj" \
"CMakeFiles/dataset_export.dir/src/voc_exporter.cpp.obj" \
"CMakeFiles/dataset_export.dir/src/yolo_exporter.cpp.obj" \
"CMakeFiles/dataset_export.dir/src/image_processor.cpp.obj" \
"CMakeFiles/dataset_export.dir/src/image_crop.cpp.obj" \
"CMakeFiles/dataset_export.dir/src/minio_client.cpp.obj" \
"CMakeFiles/dataset_export.dir/src/utils.cpp.obj"

# External object files for target dataset_export
dataset_export_EXTERNAL_OBJECTS =

bin/dataset_export.dll: CMakeFiles/dataset_export.dir/src/dataset_export_jni.cpp.obj
bin/dataset_export.dll: CMakeFiles/dataset_export.dir/src/coco_exporter.cpp.obj
bin/dataset_export.dll: CMakeFiles/dataset_export.dir/src/voc_exporter.cpp.obj
bin/dataset_export.dll: CMakeFiles/dataset_export.dir/src/yolo_exporter.cpp.obj
bin/dataset_export.dll: CMakeFiles/dataset_export.dir/src/image_processor.cpp.obj
bin/dataset_export.dll: CMakeFiles/dataset_export.dir/src/image_crop.cpp.obj
bin/dataset_export.dll: CMakeFiles/dataset_export.dir/src/minio_client.cpp.obj
bin/dataset_export.dll: CMakeFiles/dataset_export.dir/src/utils.cpp.obj
bin/dataset_export.dll: CMakeFiles/dataset_export.dir/build.make
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_gapi.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_stitching.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_alphamat.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_aruco.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_bgsegm.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_ccalib.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_cvv.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_dnn_objdetect.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_dnn_superres.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_dpm.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_face.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_freetype.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_fuzzy.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_hdf.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_hfs.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_img_hash.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_intensity_transform.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_line_descriptor.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_mcc.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_ovis.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_quality.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_rapid.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_reg.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_rgbd.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_saliency.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_sfm.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_signal.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_stereo.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_structured_light.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_superres.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_surface_matching.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_tracking.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_videostab.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_viz.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_wechat_qrcode.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_xfeatures2d.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_xobjdetect.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_xphoto.dll.a
bin/dataset_export.dll: C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/libminiocpp.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_shape.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_highgui.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_datasets.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_plot.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_text.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_ml.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_phase_unwrapping.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_optflow.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_ximgproc.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_video.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_videoio.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_imgcodecs.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_objdetect.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_calib3d.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_dnn.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_features2d.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_flann.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_photo.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_imgproc.dll.a
bin/dataset_export.dll: C:/msys64/mingw64/lib/libopencv_core.dll.a
bin/dataset_export.dll: C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/libcurlpp.a
bin/dataset_export.dll: C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/libcurl.a
bin/dataset_export.dll: C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/libINIReader.a
bin/dataset_export.dll: C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/libinih.a
bin/dataset_export.dll: C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/libpugixml.a
bin/dataset_export.dll: C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/libssl.a
bin/dataset_export.dll: C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/libcrypto.a
bin/dataset_export.dll: C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/lib/libzlib.a
bin/dataset_export.dll: CMakeFiles/dataset_export.dir/linkLibs.rsp
bin/dataset_export.dll: CMakeFiles/dataset_export.dir/objects1.rsp
bin/dataset_export.dll: CMakeFiles/dataset_export.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=C:\work\code\java\data-annotation-platform\native\build-static\CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Linking CXX shared library bin\dataset_export.dll"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles\dataset_export.dir\link.txt --verbose=$(VERBOSE)
	C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file C:/msys64/home/<USER>/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary C:/work/code/java/data-annotation-platform/native/build-static/bin/dataset_export.dll -installedDir C:/msys64/home/<USER>/vcpkg/installed/x64-mingw-static/bin -OutVariable out
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold "Copying library to Java resources directory"
	C:\msys64\mingw64\bin\cmake.exe -E copy C:/work/code/java/data-annotation-platform/native/build-static/bin/dataset_export.dll C:/work/code/java/data-annotation-platform/native/../ylzx-annotation/src/main/resources/native/

# Rule to build all files generated by this target.
CMakeFiles/dataset_export.dir/build: bin/dataset_export.dll
.PHONY : CMakeFiles/dataset_export.dir/build

CMakeFiles/dataset_export.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles\dataset_export.dir\cmake_clean.cmake
.PHONY : CMakeFiles/dataset_export.dir/clean

CMakeFiles/dataset_export.dir/depend:
	$(CMAKE_COMMAND) -E cmake_depends "MinGW Makefiles" C:\work\code\java\data-annotation-platform\native C:\work\code\java\data-annotation-platform\native C:\work\code\java\data-annotation-platform\native\build-static C:\work\code\java\data-annotation-platform\native\build-static C:\work\code\java\data-annotation-platform\native\build-static\CMakeFiles\dataset_export.dir\DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/dataset_export.dir/depend


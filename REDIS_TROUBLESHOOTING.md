# Redis 连接问题解决方案

## 问题分析
应用启动失败，错误显示：`Unable to connect to Redis` 和 `Connection refused: localhost:6379`

虽然配置文件中Redis地址是 `***************:6379`，但错误信息显示尝试连接`localhost:6379`，这表明：
1. Redis服务器 ***************:6379 不可达
2. 应用回退到默认的localhost配置

## 解决方案

### 方案一：启动本地Redis（推荐用于开发）

#### 使用Docker启动本地Redis
```bash
# 启动Redis容器
docker run -d --name redis \
  -p 6379:6379 \
  redis:7-alpine

# 或者带密码启动（如果需要）
docker run -d --name redis \
  -p 6379:6379 \
  redis:7-alpine redis-server --requirepass "your-password"
```

#### 修改配置使用本地Redis
临时修改 `application.yml` 中的Redis配置：
```yaml
spring:
  data:
    redis:
      host: localhost  # 改为localhost
      port: 6379
      database: 0      # 改为0，本地Redis通常使用数据库0
      password: ''     # 如果没有密码就留空
```

### 方案二：修复远程Redis连接

#### 检查网络连接
```bash
# 测试能否连接到远程Redis
telnet 192.168.************

# 或者使用ping测试网络
ping ***************
```

#### 检查Redis服务状态
登录到 *************** 服务器检查：
```bash
# 检查Redis服务状态
systemctl status redis
# 或
docker ps | grep redis

# 检查Redis配置
redis-cli -h *************** -p 6379 -a 'Ylzx@2024!+9821*' ping
```

### 方案三：创建开发环境配置

创建一个开发环境专用的配置文件 `application-dev.yml`：
```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      database: 0
      password: ''
      timeout: 10s
      lettuce:
        pool:
          min-idle: 0
          max-idle: 8
          max-active: 8
          max-wait: -1ms
```

然后启动时指定profile：
```bash
java -jar app.jar --spring.profiles.active=dev
```

### 方案四：禁用Redis（临时方案）

如果暂时不需要Redis功能，可以在启动类上添加排除：
```java
@SpringBootApplication(exclude = {
    DataSourceAutoConfiguration.class,
    RedisAutoConfiguration.class  // 添加这行
})
```

## 推荐步骤

1. **立即解决**: 使用方案一启动本地Redis
2. **长期解决**: 检查***************的Redis服务状态
3. **开发便利**: 创建开发环境专用配置

## 快速启动命令

```bash
# 1. 启动本地Redis
docker run -d --name redis -p 6379:6379 redis:7-alpine

# 2. 验证Redis可用
docker exec -it redis redis-cli ping

# 3. 重启Spring Boot应用
```

选择最适合当前情况的方案即可快速解决问题。
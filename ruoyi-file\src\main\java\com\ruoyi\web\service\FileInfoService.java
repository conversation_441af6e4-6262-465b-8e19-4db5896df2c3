package com.ruoyi.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.api.domain.vo.SysFileSearchVO;
import com.ruoyi.web.domain.FileInfoEntity;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Transactional(rollbackFor = Exception.class)
public interface FileInfoService extends IService<FileInfoEntity> {

    /**
     * 主方法，处理排序逻辑
     *
     * @param fileList 要排序的文件列表
     * @return 需要更新的FileInfoEntity列表
     */
    List<FileInfoEntity> processFileSort(List<SysFileSearchVO> fileList);

    /**
     * 根据文件id保存文件备注
     */
    FileInfoEntity saveFileInfoRemark(SysFileSearchVO fileInfoEntity);

    /**
     * 根据文件id保存文件备注
     */
    List<FileInfoEntity> saveFileInfoRemark(List<SysFileSearchVO> fileInfoEntityList);


    /**
     * 根据文件id删除文件
     */
    boolean deleteFileInfo(String fileIds);

    /**
     * 根据文件ids删除文件
     */
    boolean deleteFileInfo(List<String> fileIds);


}

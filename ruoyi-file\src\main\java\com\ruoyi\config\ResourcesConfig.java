package com.ruoyi.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.CacheControl;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.PostConstruct;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 通用映射配置
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "file")
@Data
@Slf4j
public class ResourcesConfig implements WebMvcConfigurer {
    /**
     * 文件url
     */
    private String domain;
    /**
     * 资源映射路径 前缀
     */
    public List<String> prefix;

    /**
     * 上传文件存储在本地的根路径
     */
    private List<String> path;

    public String getPathByPrefix(String prefix) {
        for (int i = 0; i < this.prefix.size(); i++) {
            if (this.prefix.get(i).replaceAll("/", "").equals(prefix)) {
                return this.path.get(i);
            }
        }
        return null;
    }

    private List<String> addpaths;

    private List<String> excludepaths;

    /**
     * 存储处理好的完整排除路径
     */
    private List<ExcludePathPattern> processedExcludePaths;

    /**
     * 用于存储排除路径的模式和处理逻辑
     */
    @Data
    public static class ExcludePathPattern {
        private final String basePath;
        private final PathType pathType;

        public ExcludePathPattern(String basePath, PathType pathType) {
            this.basePath = basePath;
            this.pathType = pathType;
        }

        public boolean matches(String url) {
            switch (pathType) {
                case DOUBLE_WILDCARD:
                    // /** 模式，匹配所有子路径
                    return url.startsWith(basePath);
                case SINGLE_WILDCARD:
                    // /* 模式，只匹配当前层级
                    if (!url.startsWith(basePath)) {
                        return false;
                    }
                    String remainingPath = url.substring(basePath.length());
                    return !remainingPath.contains("/");
                case EXACT:
                    // 精确匹配
                    return url.startsWith(basePath);
                default:
                    return false;
            }
        }
    }

    /**
     * 路径匹配类型枚举
     */
    public enum PathType {
        DOUBLE_WILDCARD,  // /**
        SINGLE_WILDCARD,  // /*
        EXACT            // 精确匹配
    }

    /**
     * 初始化处理排除路径
     * 在所有属性设置完成后自动调用
     */
    @PostConstruct
    public void initProcessedExcludePaths() {
        if (excludepaths == null || excludepaths.isEmpty()) {
            processedExcludePaths = new ArrayList<>();
            return;
        }

        processedExcludePaths = excludepaths.stream().map(path -> {
            String normalizedPath = path.startsWith("/") ? path : "/" + path;
            String domainPrefix = getDomainAndAssets();

            if (normalizedPath.endsWith("/**")) {
                String basePath = concatUrl(domainPrefix, normalizedPath.substring(0, normalizedPath.length() - 2));
                return new ExcludePathPattern(basePath, PathType.DOUBLE_WILDCARD);
            } else if (normalizedPath.endsWith("/*")) {
                String basePath = concatUrl(domainPrefix, normalizedPath.substring(0, normalizedPath.length() - 1));
                return new ExcludePathPattern(basePath, PathType.SINGLE_WILDCARD);
            } else {
                String exactPath = concatUrl(domainPrefix, normalizedPath);
                return new ExcludePathPattern(exactPath, PathType.EXACT);
            }
        }).collect(Collectors.toList());
    }

    /**
     * 检查URL是否应该被排除
     */
//    public boolean shouldExcludeUrl(String url) {
//        if (processedExcludePaths == null) {
//            return false;
//        }
//        return processedExcludePaths.stream().anyMatch(pattern -> pattern.matches(url));
//    }

    /**
     * 拼接URL路径
     */
    private String concatUrl(String part1, String part2) {
        StringBuilder sb = new StringBuilder();

        if (part1.endsWith("/")) {
            sb.append(part1.substring(0, part1.length() - 1));
        } else {
            sb.append(part1);
        }

        if (!part2.startsWith("/")) {
            sb.append("/");
        }

        sb.append(part2);

        return sb.toString();
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        for (int i = 0; i < prefix.size(); i++) {
            log.info("注册前缀：{},注册路径:{}", "/assets" + prefix.get(i), "file:" + path.get(i) + File.separator);
            /** 本地文件上传路径 */
            registry.addResourceHandler("/assets" + prefix.get(i) + "/**")
                    .addResourceLocations("file:" + path.get(i) + File.separator)
                    // 添加资源处理器的CORS配置
                    .setCachePeriod(3600).setCacheControl(CacheControl.maxAge(1, TimeUnit.HOURS));
        }
    }

    /**
     * 开启跨域
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        for (int i = 0; i < prefix.size(); i++) {
            registry.addMapping(prefix.get(i) + "/**")
                    // 使用 allowedOriginPatterns 替代 allowedOrigins
                    .allowedOriginPatterns("*")
                    // 设置允许的方法
                    .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")
                    // 允许携带认证信息
                    .allowCredentials(true)
                    // 允许的请求头
                    .allowedHeaders("*")
                    // 设置预检请求的有效期
                    .maxAge(3600);
        }
    }

    /**
     * 读取静态资源文件前缀
     */
//    public String getDomainHost() {
//        // 默认存储和读取第一条
//        if (this.prefix == null || this.prefix.isEmpty()) {
//            throw new IllegalStateException("prefix 列表为空，无法获取域名前缀。");
//        }
//
//        String prefix = this.prefix.get(0);
//        StringBuilder domainHost = new StringBuilder();
//
//        domainHost.append(this.domain != null ? this.domain.trim() : "");
//
//        if (prefix != null) {
//            prefix = prefix.trim();
//            if (!domainHost.toString().endsWith("/") && !prefix.startsWith("/")) {
//                domainHost.append("/");
//            } else if (domainHost.toString().endsWith("/") && prefix.startsWith("/")) {
//                // 避免重复的 '/'
//                prefix = prefix.substring(1);
//            }
//            domainHost.append(prefix);
//        }
//
//        return domainHost.toString();
//    }

    /**
     * 读取静态资源文件地址
     */
    public String getDefaultPath() {
        //默认存储和读取第一条
        return this.path.get(0);
    }

    @Autowired
    private TokenInterceptor tokenInterceptor;

    @Override
    //注册拦截器
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(tokenInterceptor) //注册拦截器
                .addPathPatterns(addpaths.toArray(new String[0])) //拦截一切请求(任何请求路径)
                .excludePathPatterns(excludepaths.toArray(new String[0]));//放过login登录请求
    }

    public String getDomainAndAssets() {
        return domain + "/assets/";
    }

    public String getDomainNoAssets() {
        return domain;
    }
}
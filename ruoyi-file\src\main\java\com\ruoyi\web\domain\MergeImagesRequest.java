package com.ruoyi.web.domain;

import com.ruoyi.system.api.domain.ImageTextRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 合并图片请求参数
 */
@Data
@ApiModel(description = "合并图片请求参数")
public class MergeImagesRequest {

    private ImageTextRequest request;

    @ApiModelProperty(value = "图片宽度（可选，默认549）")
    private Integer width;
    
    @ApiModelProperty(value = "图片高度（可选，默认489）")
    private Integer height;
    
    @ApiModelProperty(value = "字体大小（可选，默认32）")
    private Integer fontSize;
    
    @ApiModelProperty(value = "文本颜色（可选，默认黑色#000000）")
    private String textColor;
    
    @ApiModelProperty(value = "背景颜色（可选，默认白色#FFFFFF）")
    private String backgroundColor;
    
    @ApiModelProperty(value = "字体名称（可选，默认宋体）")
    private String fontName;
    
    @ApiModelProperty(value = "是否添加阴影（可选，默认false）")
    private Boolean addShadow;
} 
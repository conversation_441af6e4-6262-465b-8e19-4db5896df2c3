# YLZX文件服务

基于MinIO的文件存储服务，提供文件上传、下载、管理等功能。

## 功能特性

- ✅ 文件上传（单文件/批量）
- ✅ 文件下载
- ✅ 文件删除（单个/批量）
- ✅ 文件查询和分页
- ✅ 文件去重（基于MD5）
- ✅ 缩略图生成（图片文件）
- ✅ 文件夹操作（移动、压缩）
- ✅ 预签名URL生成
- ✅ 文件复制
- ✅ RESTful API接口
- ✅ Swagger文档

## 技术栈

- Spring Boot 3.x
- MyBatis Plus
- MinIO
- PostgreSQL
- Hutool
- SpringDoc OpenAPI 3
- Apache Commons Compress

## 数据库表结构

### file_info 表

优化后的文件信息表，去除了不必要的字段：

```sql
CREATE TABLE file_info (
    id VARCHAR(32) NOT NULL COMMENT '文件ID，使用UUID',
    file_name VARCHAR(500) NOT NULL COMMENT '文件名（存储在MinIO中的文件名）',
    original_filename VARCHAR(500) NOT NULL COMMENT '原始文件名',
    file_size BIGINT NOT NULL COMMENT '文件大小（字节）',
    content_type VARCHAR(100) COMMENT '文件MIME类型',
    file_extension VARCHAR(20) COMMENT '文件扩展名',
    file_path VARCHAR(1000) NOT NULL COMMENT 'MinIO中的文件路径（包含文件夹）',
    file_url VARCHAR(1000) COMMENT '文件访问URL',
    md5_hash VARCHAR(32) COMMENT '文件MD5哈希值，用于去重',
    thumbnail_path VARCHAR(1000) COMMENT '缩略图路径',
    thumbnail_url VARCHAR(1000) COMMENT '缩略图访问URL',
    thumbnail_size BIGINT COMMENT '缩略图大小（字节）',
    status SMALLINT DEFAULT 1 COMMENT '文件状态：1-正常，0-已删除',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(64) COMMENT '创建者',
    update_by VARCHAR(64) COMMENT '更新者',
    remark TEXT COMMENT '备注',
    PRIMARY KEY (id)
);
```

## 配置说明

### application.yml

```yaml
# MinIO配置
minio:
  endpoint: http://localhost:9000
  access-key: minioadmin
  secret-key: minioadmin
  bucket-name: data-annotation
  secure: false

# 数据库配置
spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ************************************************
    username: postgres
    password: 123456
```

## API接口

### 文件管理接口

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 单文件上传 | POST | `/api/file/upload` | 上传单个文件 |
| 批量上传 | POST | `/api/file/upload/batch` | 批量上传文件 |
| 文件下载 | GET | `/api/file/download/{fileId}` | 下载文件 |
| 缩略图下载 | GET | `/api/file/thumbnail/{fileId}` | 下载缩略图 |
| 获取文件信息 | GET | `/api/file/info/{fileId}` | 获取文件详情 |
| 删除文件 | DELETE | `/api/file/{fileId}` | 删除单个文件 |
| 批量删除 | DELETE | `/api/file/batch` | 批量删除文件 |
| 查询文件 | POST | `/api/file/query` | 分页查询文件 |
| 预签名URL | GET | `/api/file/presigned-url/{fileId}` | 获取预签名URL |

### 文件夹管理接口

| 接口 | 方法 | 路径 | 说明 |
|------|------|------|------|
| 移动文件夹 | POST | `/api/folder/move` | 移动文件夹 |
| 压缩文件夹 | POST | `/api/folder/compress` | 压缩文件夹为ZIP |
| 列出文件 | GET | `/api/folder/list` | 列出文件夹中的文件 |
| 复制文件 | POST | `/api/folder/copy` | 复制文件 |
| 检查文件存在 | GET | `/api/folder/check` | 检查文件是否存在 |

## API响应格式

项目使用统一的响应格式，与YLZX项目保持一致：

### 成功响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    // 具体数据
  }
}
```

### 错误响应
```json
{
  "code": 500,
  "msg": "错误信息"
}
```

### 分页响应（查询接口）
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    // 数据列表
  ],
  "total": 100
}
```

## 使用示例

### 1. 文件上传

```bash
curl -X POST "http://localhost:8083/file/api/file/upload" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/your/file.jpg" \
  -F "folderPath=images/avatars" \
  -F "generateThumbnail=true" \
  -F "createBy=admin"
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "文件上传成功",
  "data": {
    "id": "1234567890abcdef",
    "originalFilename": "avatar.jpg",
    "fileName": "20240101_123456_avatar.jpg",
    "fileSize": 1024000,
    "contentType": "image/jpeg",
    "fileExtension": "jpg",
    "filePath": "images/avatars/2024/01/01/20240101_123456_avatar.jpg",
    "fileUrl": "/api/file/download/1234567890abcdef",
    "md5Hash": "d41d8cd98f00b204e9800998ecf8427e",
    "thumbnailUrl": "/api/file/thumbnail/1234567890abcdef",
    "createTime": "2024-01-01 12:34:56"
  }
}
```

### 2. 文件查询

```bash
curl -X POST "http://localhost:8083/file/api/file/query" \
  -H "Content-Type: application/json" \
  -d '{
    "fileName": "avatar",
    "fileExtension": "jpg",
    "pageNum": 1,
    "pageSize": 10
  }'
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "id": "1234567890abcdef",
      "originalFilename": "avatar.jpg",
      "fileName": "20240101_123456_avatar.jpg",
      "fileSize": 1024000,
      "contentType": "image/jpeg",
      "filePath": "images/avatars/2024/01/01/20240101_123456_avatar.jpg",
      "createTime": "2024-01-01 12:34:56"
    }
  ],
  "total": 1
}
```

### 3. 文件夹压缩

```bash
curl -X POST "http://localhost:8083/file/api/folder/compress" \
  -H "Content-Type: application/json" \
  -d '{
    "sourcePath": "images/avatars",
    "zipFileName": "avatars_backup.zip",
    "operator": "admin"
  }'
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "文件夹压缩成功",
  "data": {
    "id": "zip123456789",
    "originalFilename": "avatars_backup.zip",
    "fileName": "20240101_123456_avatars_backup.zip",
    "fileSize": 5242880,
    "contentType": "application/zip",
    "filePath": "compressed/2024/01/01/20240101_123456_avatars_backup.zip",
    "remark": "压缩文件夹：images/avatars，包含10个文件，原始大小: 10.00 MB, 压缩后: 5.00 MB, 压缩比: 50.00%"
  }
}
```

## 部署说明

1. 确保MinIO服务正常运行
2. 创建数据库表（执行 `src/main/resources/sql/file_info.sql`）
3. 修改配置文件中的数据库和MinIO连接信息
4. 启动应用：`java -jar ylzx-file.jar`
5. 访问Swagger文档：`http://localhost:8083/file/swagger-ui.html`

## 存储桶结构

```
data-annotation/
├── images/
│   ├── 2024/01/01/
│   │   ├── timestamp_uuid_filename.jpg
│   │   └── ...
│   └── thumbnails/
│       ├── 2024/01/01/
│       │   ├── timestamp_uuid_filename_thumb.jpg
│       │   └── ...
├── documents/
│   ├── 2024/01/01/
│   │   ├── timestamp_uuid_document.pdf
│   │   └── ...
└── compressed/
    ├── 2024/01/01/
    │   ├── timestamp_uuid_archive.zip
    │   └── ...
```

## 注意事项

1. 文件上传大小限制为100MB，可在配置文件中调整
2. 支持的图片格式：jpg, jpeg, png, gif, bmp, webp
3. 文件去重基于MD5哈希值
4. 删除操作为软删除，不会立即删除MinIO中的文件
5. 缩略图默认尺寸为200x200像素，保持原始宽高比

## 开发计划

- [ ] 文件版本管理
- [ ] 文件分享功能
- [ ] 文件同步功能
- [ ] 更多文件格式支持
- [ ] 文件预览功能
- [ ] 批量操作优化

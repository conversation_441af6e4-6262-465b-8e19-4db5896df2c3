@echo off
echo 正在启动开发环境所需服务...

echo.
echo 1. 检查Docker是否运行...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: Docker未安装或未运行，请先安装并启动Docker
    pause
    exit /b 1
)

echo.
echo 2. 启动Redis服务...
docker run -d --name redis-dev -p 6379:6379 redis:7-alpine 2>nul
if %errorlevel% equ 0 (
    echo ✓ Redis服务启动成功 (localhost:6379)
) else (
    echo - Redis容器可能已存在，尝试启动现有容器...
    docker start redis-dev >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✓ Redis服务启动成功
    ) else (
        echo ! Redis启动可能有问题，请手动检查
    )
)

echo.
echo 3. 启动MinIO服务...
docker run -d --name minio-dev -p 9000:9000 -p 9001:9001 -e "MINIO_ROOT_USER=minioadmin" -e "MINIO_ROOT_PASSWORD=minioadmin" -v %USERPROFILE%/minio-data:/data minio/minio server /data --console-address ":9001" 2>nul
if %errorlevel% equ 0 (
    echo ✓ MinIO服务启动成功
    echo   - API端点: http://localhost:9000
    echo   - 管理界面: http://localhost:9001
    echo   - 用户名: minioadmin
    echo   - 密码: minioadmin
) else (
    echo - MinIO容器可能已存在，尝试启动现有容器...
    docker start minio-dev >nul 2>&1
    if %errorlevel% eql 0 (
        echo ✓ MinIO服务启动成功
    ) else (
        echo ! MinIO启动可能有问题，请手动检查
    )
)

echo.
echo 4. 等待服务启动完成...
timeout /t 3 >nul

echo.
echo 5. 验证服务状态...
docker exec redis-dev redis-cli ping >nul 2>&1
if %errorlevel% equ 0 (
    echo ✓ Redis服务正常
) else (
    echo ! Redis可能未完全启动，请稍等片刻
)

echo.
echo 开发环境服务启动完成！
echo.
echo 下一步：
echo 1. 访问 http://localhost:9001 登录MinIO管理界面
echo 2. 创建存储桶 'ylzx-files-dev'
echo 3. 启动Spring Boot应用
echo.
echo 停止服务使用: docker stop redis-dev minio-dev
echo 删除容器使用: docker rm redis-dev minio-dev
echo.
pause
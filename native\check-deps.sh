#!/bin/bash

echo "========================================"
echo "Checking MSYS2 Dependencies"
echo "========================================"

# 检查MSYS2环境
if [ -z "$MSYSTEM" ]; then
    echo "❌ Not in MSYS2 environment"
    echo "Please run this in MSYS2 MinGW 64-bit terminal"
    exit 1
fi

echo "✓ MSYS2 Environment: $MSYSTEM"
echo "✓ Architecture: $(uname -m)"
echo ""

# 检查编译器
echo "Checking compilers..."
if command -v gcc >/dev/null 2>&1; then
    echo "✓ GCC: $(gcc --version | head -1)"
else
    echo "❌ GCC not found"
fi

if command -v g++ >/dev/null 2>&1; then
    echo "✓ G++: $(g++ --version | head -1)"
else
    echo "❌ G++ not found"
fi

if command -v cmake >/dev/null 2>&1; then
    echo "✓ CMake: $(cmake --version | head -1)"
else
    echo "❌ CMake not found"
fi

if command -v make >/dev/null 2>&1; then
    echo "✓ Make: $(make --version | head -1)"
else
    echo "❌ Make not found"
fi

echo ""

# 检查系统包
echo "Checking system packages..."

check_package() {
    local package=$1
    local display_name=$2
    
    if pacman -Qi "$package" >/dev/null 2>&1; then
        local version=$(pacman -Qi "$package" | grep "Version" | cut -d: -f2 | xargs)
        echo "✓ $display_name: $version"
        return 0
    else
        echo "❌ $display_name: Not installed"
        echo "   Install with: pacman -S $package"
        return 1
    fi
}

# 检查必要的包
check_package "mingw-w64-x86_64-opencv" "OpenCV"
check_package "mingw-w64-x86_64-curl" "cURL"
check_package "mingw-w64-x86_64-openssl" "OpenSSL"
check_package "mingw-w64-x86_64-nlohmann-json" "nlohmann/json"
check_package "mingw-w64-x86_64-cmake" "CMake"
check_package "mingw-w64-x86_64-gcc" "GCC"

echo ""

# 检查pkg-config
echo "Checking pkg-config..."
if command -v pkg-config >/dev/null 2>&1; then
    echo "✓ pkg-config available"
    
    # 检查OpenCV的pkg-config
    if pkg-config --exists opencv4; then
        echo "✓ OpenCV4 pkg-config: $(pkg-config --modversion opencv4)"
        echo "  Include dirs: $(pkg-config --cflags opencv4 | cut -c1-60)..."
        echo "  Library dirs: $(pkg-config --libs opencv4 | cut -c1-60)..."
    else
        echo "⚠ OpenCV4 pkg-config not found"
    fi
    
    # 检查其他包的pkg-config
    for pkg in libcurl openssl nlohmann_json; do
        if pkg-config --exists $pkg; then
            echo "✓ $pkg pkg-config: $(pkg-config --modversion $pkg)"
        else
            echo "⚠ $pkg pkg-config not found"
        fi
    done
else
    echo "❌ pkg-config not found"
    echo "   Install with: pacman -S mingw-w64-x86_64-pkg-config"
fi

echo ""

# 检查vcpkg（用于MinIO）
echo "Checking vcpkg (for MinIO)..."
if [ -n "$VCPKG_ROOT" ]; then
    echo "✓ VCPKG_ROOT set to: $VCPKG_ROOT"
    
    if [ -f "$VCPKG_ROOT/vcpkg.exe" ]; then
        echo "✓ vcpkg.exe found"
        
        # 检查MinIO
        if $VCPKG_ROOT/vcpkg.exe list | grep -q "minio"; then
            echo "✓ MinIO packages:"
            $VCPKG_ROOT/vcpkg.exe list | grep minio | sed 's/^/  /'
        else
            echo "❌ MinIO not found in vcpkg"
            echo "   Install with: $VCPKG_ROOT/vcpkg.exe install minio-cpp:x64-mingw-dynamic"
        fi
    else
        echo "❌ vcpkg.exe not found at $VCPKG_ROOT"
    fi
else
    echo "⚠ VCPKG_ROOT not set"
    echo "   Set with: export VCPKG_ROOT=/path/to/your/vcpkg"
fi

echo ""

# 检查库文件位置
echo "Checking library locations..."

# OpenCV库
OPENCV_LIB_DIR="/mingw64/lib"
if [ -d "$OPENCV_LIB_DIR" ]; then
    echo "✓ MinGW lib directory: $OPENCV_LIB_DIR"
    if ls $OPENCV_LIB_DIR/libopencv*.dll.a >/dev/null 2>&1; then
        echo "✓ OpenCV libraries found:"
        ls $OPENCV_LIB_DIR/libopencv*.dll.a | head -3 | sed 's/^/  /'
        echo "  ... (and more)"
    else
        echo "❌ OpenCV libraries not found in $OPENCV_LIB_DIR"
    fi
fi

# 头文件
OPENCV_INCLUDE_DIR="/mingw64/include/opencv4"
if [ -d "$OPENCV_INCLUDE_DIR" ]; then
    echo "✓ OpenCV headers: $OPENCV_INCLUDE_DIR"
else
    echo "❌ OpenCV headers not found at $OPENCV_INCLUDE_DIR"
fi

echo ""
echo "========================================"
echo "Summary"
echo "========================================"

echo "System packages status:"
echo "- Use system packages for: OpenCV, cURL, OpenSSL, nlohmann/json"
echo "- Use vcpkg for: MinIO C++ SDK"
echo ""

echo "Recommended build command:"
echo "  cd /c/work/code/java/data-annotation-platform/native"
echo "  export VCPKG_ROOT=/path/to/your/vcpkg  # if you have vcpkg"
echo "  chmod +x build-system-libs.sh"
echo "  ./build-system-libs.sh"
echo ""

echo "If packages are missing, install them with:"
echo "  pacman -S mingw-w64-x86_64-opencv mingw-w64-x86_64-curl mingw-w64-x86_64-openssl mingw-w64-x86_64-nlohmann-json"

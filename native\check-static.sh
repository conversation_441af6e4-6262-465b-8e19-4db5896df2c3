#!/bin/bash

echo "========================================"
echo "Static Build Dependency Checker"
echo "========================================"

# 查找库文件
LIBRARY_FILE=""
for dir in build build-static .; do
    for lib in libdataset_export.dll dataset_export.dll; do
        if [ -f "$dir/$lib" ]; then
            LIBRARY_FILE="$dir/$lib"
            echo "✓ Found library: $LIBRARY_FILE"
            break 2
        fi
    done
done

if [ -z "$LIBRARY_FILE" ]; then
    echo "❌ No library file found!"
    echo "Please build the library first with:"
    echo "  ./build-static.sh"
    exit 1
fi

echo ""
echo "========================================"
echo "Library Information"
echo "========================================"

# 基本信息
echo "File: $LIBRARY_FILE"
echo "Size: $(ls -lh "$LIBRARY_FILE" | awk '{print $5}')"
echo "Modified: $(ls -l "$LIBRARY_FILE" | awk '{print $6, $7, $8}')"

echo ""
echo "========================================"
echo "Dependency Analysis"
echo "========================================"

# 使用ldd分析依赖
if command -v ldd >/dev/null 2>&1; then
    echo "Dynamic dependencies (ldd):"
    LDD_OUTPUT=$(ldd "$LIBRARY_FILE" 2>/dev/null)
    
    if [ $? -eq 0 ]; then
        echo "$LDD_OUTPUT"
        
        echo ""
        echo "Dependency categorization:"
        
        # Windows系统库（这些是OK的）
        SYSTEM_DEPS=$(echo "$LDD_OUTPUT" | grep -i "kernel32\|user32\|advapi32\|ws2_32\|winmm\|crypt32\|ntdll")
        if [ -n "$SYSTEM_DEPS" ]; then
            echo "✓ Windows system libraries (OK):"
            echo "$SYSTEM_DEPS" | sed 's/^/  /'
        fi
        
        # MinGW运行时库（这些需要避免）
        MINGW_DEPS=$(echo "$LDD_OUTPUT" | grep -i "libgcc_s_seh-1\|libstdc++-6\|libwinpthread-1")
        if [ -n "$MINGW_DEPS" ]; then
            echo ""
            echo "❌ MinGW runtime dependencies (problematic):"
            echo "$MINGW_DEPS" | sed 's/^/  /'
            echo ""
            echo "These dependencies mean the library is NOT fully static!"
            echo "Target machines will need these MinGW DLLs."
        else
            echo ""
            echo "✓ No MinGW runtime dependencies found!"
        fi
        
        # 其他库
        OTHER_DEPS=$(echo "$LDD_OUTPUT" | grep -v -i "kernel32\|user32\|advapi32\|ws2_32\|winmm\|crypt32\|ntdll\|libgcc_s_seh-1\|libstdc++-6\|libwinpthread-1" | grep "\.dll")
        if [ -n "$OTHER_DEPS" ]; then
            echo ""
            echo "⚠ Other dependencies:"
            echo "$OTHER_DEPS" | sed 's/^/  /'
        fi
        
    else
        echo "Could not analyze dependencies with ldd"
    fi
else
    echo "ldd not available"
fi

# 使用objdump分析（备用方法）
echo ""
if command -v objdump >/dev/null 2>&1; then
    echo "Import table analysis (objdump):"
    OBJDUMP_OUTPUT=$(objdump -p "$LIBRARY_FILE" 2>/dev/null | grep "DLL Name")
    
    if [ -n "$OBJDUMP_OUTPUT" ]; then
        echo "$OBJDUMP_OUTPUT"
    else
        echo "Could not analyze with objdump"
    fi
fi

echo ""
echo "========================================"
echo "Portability Assessment"
echo "========================================"

# 评估可移植性
PORTABLE=true
ISSUES=()

# 检查MinGW依赖
if ldd "$LIBRARY_FILE" 2>/dev/null | grep -q "libgcc_s_seh-1\|libstdc++-6\|libwinpthread-1"; then
    PORTABLE=false
    ISSUES+=("Has MinGW runtime dependencies")
fi

# 检查非系统库依赖
NON_SYSTEM_DEPS=$(ldd "$LIBRARY_FILE" 2>/dev/null | grep -v -i "kernel32\|user32\|advapi32\|ws2_32\|winmm\|crypt32\|ntdll\|libgcc_s_seh-1\|libstdc++-6\|libwinpthread-1" | grep "\.dll" | grep -v "not found")
if [ -n "$NON_SYSTEM_DEPS" ]; then
    PORTABLE=false
    ISSUES+=("Has non-system library dependencies")
fi

if [ "$PORTABLE" = true ]; then
    echo "✅ PORTABLE: This library should work on other Windows machines"
    echo "   without requiring additional DLL files."
else
    echo "❌ NOT FULLY PORTABLE: This library has external dependencies"
    echo ""
    echo "Issues found:"
    for issue in "${ISSUES[@]}"; do
        echo "  - $issue"
    done
fi

echo ""
echo "========================================"
echo "Recommendations"
echo "========================================"

if [ "$PORTABLE" = false ]; then
    echo "To improve portability:"
    echo ""
    echo "1. Rebuild with stronger static linking:"
    echo "   export BUILD_STATIC=ON"
    echo "   ./build-static.sh"
    echo ""
    echo "2. Ensure static libraries are available:"
    echo "   pacman -S mingw-w64-x86_64-opencv-static"
    echo "   pacman -S mingw-w64-x86_64-curl-static"
    echo ""
    echo "3. Use static triplet for vcpkg packages:"
    echo "   vcpkg install minio-cpp:x64-mingw-static"
    echo ""
    echo "4. Check CMake configuration for static flags"
else
    echo "✓ Library appears to be properly static!"
    echo ""
    echo "Deployment steps:"
    echo "1. Copy $LIBRARY_FILE to target machines"
    echo "2. Place in Java application's resources/native/ directory"
    echo "3. Test functionality on target machines"
fi

echo ""
echo "========================================"
echo "Test Commands"
echo "========================================"

echo "To test this library:"
echo ""
echo "1. Copy to Java project:"
echo "   cp '$LIBRARY_FILE' ../ylzx-annotation/src/main/resources/native/"
echo ""
echo "2. Start Java application and check logs for:"
echo "   - Library loading success"
echo "   - MinIO initialization"
echo "   - Image processing functionality"
echo ""
echo "3. Test on a clean Windows machine without MinGW/MSYS2"

# 创建测试报告
REPORT_FILE="static-build-report.txt"
cat > "$REPORT_FILE" << EOF
Static Build Analysis Report
============================
Generated: $(date)
Library: $LIBRARY_FILE
Size: $(ls -lh "$LIBRARY_FILE" | awk '{print $5}')

Dependencies:
$(ldd "$LIBRARY_FILE" 2>/dev/null || echo "Could not analyze dependencies")

Portability: $([ "$PORTABLE" = true ] && echo "GOOD" || echo "NEEDS IMPROVEMENT")

$(if [ "$PORTABLE" = false ]; then
    echo "Issues:"
    for issue in "${ISSUES[@]}"; do
        echo "- $issue"
    done
fi)

Recommendations:
$(if [ "$PORTABLE" = false ]; then
    echo "- Rebuild with static linking improvements"
    echo "- Install static versions of dependencies"
    echo "- Use static vcpkg triplets"
else
    echo "- Library is ready for deployment"
    echo "- Test on target machines"
fi)
EOF

echo ""
echo "✓ Analysis report saved to: $REPORT_FILE"

package com.ylzx.file.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文件查询请求DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "文件查询请求")
public class FileQueryRequest {

    /**
     * 文件名（模糊查询）
     */
    @Schema(description = "文件名（模糊查询）", example = "avatar")
    private String fileName;

    /**
     * 原始文件名（模糊查询）
     */
    @Schema(description = "原始文件名（模糊查询）", example = "头像")
    private String originalFilename;

    /**
     * 文件扩展名
     */
    @Schema(description = "文件扩展名", example = "jpg")
    private String fileExtension;

    /**
     * 文件夹路径
     */
    @Schema(description = "文件夹路径", example = "images/avatars")
    private String folderPath;

    /**
     * 文件类型
     */
    @Schema(description = "文件MIME类型", example = "image/jpeg")
    private String contentType;

    /**
     * 最小文件大小（字节）
     */
    @Schema(description = "最小文件大小（字节）", example = "1024")
    private Long minFileSize;

    /**
     * 最大文件大小（字节）
     */
    @Schema(description = "最大文件大小（字节）", example = "10485760")
    private Long maxFileSize;

    /**
     * 开始时间
     */
    @Schema(description = "开始时间", example = "2024-01-01 00:00:00")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Schema(description = "结束时间", example = "2024-12-31 23:59:59")
    private LocalDateTime endTime;

    /**
     * 创建者
     */
    @Schema(description = "创建者", example = "admin")
    private String createBy;

    /**
     * 页码
     */
    @Schema(description = "页码", example = "1")
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "10")
    private Integer pageSize = 10;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段", example = "createTime")
    private String orderBy = "createTime";

    /**
     * 排序方向
     */
    @Schema(description = "排序方向", example = "desc")
    private String orderDirection = "desc";
}

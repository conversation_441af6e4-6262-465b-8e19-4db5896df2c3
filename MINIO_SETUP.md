# MinIO 设置指南

## 问题说明
应用启动失败，错误信息显示：`endpoint must not be null`，这是因为MinIO服务未启动或配置不正确。

## 解决方案

### 1. 启动MinIO服务

#### 方式一：使用Docker（推荐）
```bash
# 拉取MinIO镜像
docker pull minio/minio

# 创建数据目录
mkdir -p ~/minio/data

# 启动MinIO服务
docker run -p 9000:9000 -p 9001:9001 \
  --name minio \
  -e "MINIO_ROOT_USER=minioadmin" \
  -e "MINIO_ROOT_PASSWORD=minioadmin" \
  -v ~/minio/data:/data \
  minio/minio server /data --console-address ":9001"
```

#### 方式二：直接下载运行
1. 从 https://min.io/download 下载对应系统的MinIO
2. Windows用户下载 `minio.exe`
3. 运行命令：
```bash
# Windows
set MINIO_ROOT_USER=minioadmin
set MINIO_ROOT_PASSWORD=minioadmin
minio.exe server C:\minio-data --console-address ":9001"

# Linux/macOS
export MINIO_ROOT_USER=minioadmin
export MINIO_ROOT_PASSWORD=minioadmin
./minio server ~/minio-data --console-address ":9001"
```

### 2. 验证MinIO服务
- 管理界面：http://localhost:9001
- 用户名：minioadmin
- 密码：minioadmin
- API端点：http://localhost:9000

### 3. 创建存储桶
1. 登录MinIO管理界面
2. 点击 "Create Bucket"
3. 输入桶名：`ylzx-files`
4. 点击 "Create Bucket"

### 4. 修改配置（如需要）
如果MinIO运行在不同的地址或端口，请修改 `application.yml` 中的配置：

```yaml
minio:
  endpoint: http://your-minio-host:9000
  access-key: your-access-key
  secret-key: your-secret-key
  bucket-name: your-bucket-name
```

### 5. 重新启动应用
完成MinIO设置后，重新启动Spring Boot应用。

## 当前配置
应用当前配置的MinIO连接信息：
- **端点**: http://127.0.0.1:9000
- **用户名**: minioadmin
- **密码**: minioadmin
- **存储桶**: ylzx-files

## 故障排除
1. **端口冲突**: 如果9000端口被占用，修改MinIO启动端口并更新application.yml
2. **防火墙**: 确保9000和9001端口未被防火墙阻止
3. **网络**: 确保MinIO服务可以通过配置的endpoint访问

启动MinIO服务后，应用应该能够正常启动。
package com.ylzx.annotation.service.impl;

import com.ylzx.annotation.domain.AnnotationAnnotations;
import com.ylzx.annotation.domain.AnnotationImages;
import com.ylzx.annotation.jni.DatasetExportNative;
import com.ylzx.annotation.service.ImageCropService;
import com.ylzx.annotation.service.MinioFileService;
import com.ylzx.file.service.FileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 图像裁剪服务实现类
 * 统一使用C++进行图像处理
 */
@Slf4j
@Service
public class ImageCropServiceImpl implements ImageCropService {

    @Autowired
    private DatasetExportNative exportNative;

    @Autowired
    private FileService fileService;
    
    @Autowired
    private MinioFileService minioFileService;
    
    // 临时文件缓存，避免重复下载同一文件
    private final ConcurrentMap<String, String> tempFileCache = new ConcurrentHashMap<>();

    @Override
    public CropResult cropImageByAnnotations(AnnotationImages image, List<AnnotationAnnotations> annotations,
                                           String outputPath, CropConfig cropConfig) {
        try {
            // 构建标注坐标字符串
            StringBuilder coordinatesBuilder = new StringBuilder();
            for (int i = 0; i < annotations.size(); i++) {
                if (i > 0) {
                    coordinatesBuilder.append(";");
                }
                coordinatesBuilder.append(annotations.get(i).getCoordinates());
            }

            // 构建图像路径 - 使用MinIO预签名URL
            String imagePath = getImagePathFromMinIO(image.getFileId());
            if (imagePath == null) {
                log.error("无法获取图片路径，fileId: {}", image.getFileId());
                return new CropResult(false, "无法获取图片路径");
            }

            // 直接调用C++进行智能裁剪
            boolean success = exportNative.smartCropByAnnotations(
                imagePath,
                outputPath,
                coordinatesBuilder.toString(),
                convertToCppConfig(cropConfig)
            );

            if (success) {
                return new CropResult(true, "裁剪成功", outputPath);
            } else {
                return new CropResult(false, "C++裁剪失败", null);
            }

        } catch (Exception e) {
            log.error("图像裁剪失败: {}", image.getImageId(), e);
            return new CropResult(false, "裁剪异常: " + e.getMessage(), null);
        }
    }

    @Override
    public BatchCropResult batchCropImages(List<ImageAnnotationPair> imageAnnotationPairs,
                                         String outputBasePath, CropConfig cropConfig) {
        long startTime = System.currentTimeMillis();
        BatchCropResult batchResult = new BatchCropResult();

        try {
            // 准备批量处理的数据
            List<String> imagePaths = new ArrayList<>();
            List<String> outputPaths = new ArrayList<>();
            List<String> annotationCoordinatesArray = new ArrayList<>();

            for (int i = 0; i < imageAnnotationPairs.size(); i++) {
                ImageAnnotationPair pair = imageAnnotationPairs.get(i);
                AnnotationImages image = pair.getImage();
                List<AnnotationAnnotations> annotations = pair.getAnnotations();

                // 图像路径 - 使用MinIO预签名URL
                String imagePath = getImagePathFromMinIO(image.getFileId());
                if (imagePath != null) {
                    imagePaths.add(imagePath);
                } else {
                    log.warn("无法获取图片路径，跳过图片，fileId: {}", image.getFileId());
                }

                // 输出路径 - 基于MinIO的输出路径
                String outputFileName = generateOutputFileName(image, i);
                String outputPath = outputBasePath + "/" + outputFileName; // 使用MinIO路径格式
                outputPaths.add(outputPath);

                // 标注坐标
                StringBuilder coordinatesBuilder = new StringBuilder();
                for (int j = 0; j < annotations.size(); j++) {
                    if (j > 0) {
                        coordinatesBuilder.append(";");
                    }
                    coordinatesBuilder.append(annotations.get(j).getCoordinates());
                }
                annotationCoordinatesArray.add(coordinatesBuilder.toString());
            }

            // 调用C++批量智能裁剪
            DatasetExportNative.BatchCropResult nativeResult = exportNative.batchSmartCropByAnnotations(
                imagePaths.toArray(new String[0]),
                outputPaths.toArray(new String[0]),
                annotationCoordinatesArray.toArray(new String[0]),
                convertToCppConfig(cropConfig),
                Math.min(8, imageAnnotationPairs.size())
            );

            // 转换结果
            batchResult.setSuccess(nativeResult.success);
            batchResult.setMessage(nativeResult.message);
            batchResult.setTotalCount(nativeResult.totalCount);
            batchResult.setSuccessCount(nativeResult.successCount);
            batchResult.setFailedCount(nativeResult.failedCount);

            List<CropResult> results = new ArrayList<>();
            for (int i = 0; i < imageAnnotationPairs.size(); i++) {
                CropResult result = new CropResult();
                if (i < nativeResult.successCount) {
                    result.setSuccess(true);
                    result.setMessage("裁剪成功");
                    if (nativeResult.outputPaths != null && i < nativeResult.outputPaths.length) {
                        result.setOutputPath(nativeResult.outputPaths[i]);
                    }
                } else {
                    result.setSuccess(false);
                    result.setMessage("裁剪失败");
                }
                results.add(result);
            }
            batchResult.setResults(results);

        } catch (Exception e) {
            log.error("批量图像裁剪失败", e);
            batchResult.setSuccess(false);
            batchResult.setMessage("批量裁剪异常: " + e.getMessage());
            batchResult.setTotalCount(imageAnnotationPairs.size());
            batchResult.setSuccessCount(0);
            batchResult.setFailedCount(imageAnnotationPairs.size());
        } finally {
            batchResult.setTotalProcessingTimeMs(System.currentTimeMillis() - startTime);
        }

        return batchResult;
    }

    /**
     * 转换为C++配置对象
     */
    private DatasetExportNative.CropConfig convertToCppConfig(CropConfig cropConfig) {
        DatasetExportNative.CropConfig cppConfig = new DatasetExportNative.CropConfig();
        cppConfig.targetWidth = cropConfig.getTargetWidth() != null ? cropConfig.getTargetWidth() : 0;
        cppConfig.targetHeight = cropConfig.getTargetHeight() != null ? cropConfig.getTargetHeight() : 0;
        cppConfig.padding = cropConfig.getPadding();
        cppConfig.enableRandomPlacement = cropConfig.isEnableRandomPlacement();
        cppConfig.backgroundColor = cropConfig.getBackgroundColor();
        cppConfig.maintainAspectRatio = cropConfig.isMaintainAspectRatio();
        return cppConfig;
    }

    /**
     * 生成输出文件名
     */
    private String generateOutputFileName(AnnotationImages image, int index) {
        String originalName = image.getOriginalFilename();
        String baseName = originalName.substring(0, originalName.lastIndexOf('.'));
        String extension = originalName.substring(originalName.lastIndexOf('.'));
        return String.format("%s_crop_%d_%d%s", baseName, image.getImageId(), index, extension);
    }

    /**
     * 从MinIO获取图片路径（下载到临时文件）
     * C++库需要本地文件路径，所以将MinIO中的图片下载到临时文件
     */
    private String getImagePathFromMinIO(String fileId) {
        try {
            if (fileId == null || fileId.isEmpty()) {
                return null;
            }

            // 检查缓存中是否已有临时文件
            String cachedTempPath = tempFileCache.get(fileId);
            if (cachedTempPath != null && Files.exists(Path.of(cachedTempPath))) {
                log.debug("使用缓存的临时文件: {}", cachedTempPath);
                return cachedTempPath;
            }

            // 从MinIO下载文件到临时目录
            InputStream inputStream = minioFileService.downloadFile(fileId);
            if (inputStream == null) {
                log.error("无法从MinIO下载文件: {}", fileId);
                return null;
            }

            // 获取文件信息以确定文件扩展名
            MinioFileService.FileInfoDto fileInfo = minioFileService.getFileInfo(fileId);
            String fileExtension = fileInfo != null && fileInfo.getFileExtension() != null ? 
                                  fileInfo.getFileExtension() : ".jpg";
            
            // 创建临时文件
            Path tempDir = Path.of(System.getProperty("java.io.tmpdir"), "annotation-crop");
            Files.createDirectories(tempDir);
            
            Path tempFile = tempDir.resolve("img_" + fileId.replace("/", "_") + fileExtension);
            
            // 复制文件内容到临时文件
            try (FileOutputStream fos = new FileOutputStream(tempFile.toFile())) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                }
            } finally {
                inputStream.close();
            }

            String tempFilePath = tempFile.toAbsolutePath().toString();
            
            // 缓存临时文件路径
            tempFileCache.put(fileId, tempFilePath);
            
            log.debug("已下载MinIO文件到临时路径: {} -> {}", fileId, tempFilePath);
            return tempFilePath;

        } catch (Exception e) {
            log.error("从MinIO获取图片路径失败，fileId: {}", fileId, e);
            return null;
        }
    }
    
    /**
     * 清理临时文件缓存
     */
    public void clearTempFileCache() {
        tempFileCache.forEach((fileId, tempPath) -> {
            try {
                Path path = Path.of(tempPath);
                if (Files.exists(path)) {
                    Files.delete(path);
                    log.debug("已清理临时文件: {}", tempPath);
                }
            } catch (Exception e) {
                log.warn("清理临时文件失败: {}", tempPath, e);
            }
        });
        tempFileCache.clear();
    }
    
    /**
     * 清理指定文件ID的临时文件
     */
    public void clearTempFile(String fileId) {
        String tempPath = tempFileCache.remove(fileId);
        if (tempPath != null) {
            try {
                Path path = Path.of(tempPath);
                if (Files.exists(path)) {
                    Files.delete(path);
                    log.debug("已清理临时文件: {}", tempPath);
                }
            } catch (Exception e) {
                log.warn("清理临时文件失败: {}", tempPath, e);
            }
        }
    }
}
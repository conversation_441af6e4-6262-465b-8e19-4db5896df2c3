#!/bin/bash

echo "========================================"
echo "Static MinGW build with MinIO support"
echo "========================================"

# 构建选项
BUILD_STATIC=${BUILD_STATIC:-ON}
echo "Build mode: $([ "$BUILD_STATIC" = "ON" ] && echo "Static" || echo "Dynamic")"

# 检查是否在MSYS2环境中
if [ -z "$MSYSTEM" ]; then
    echo "Error: This script should be run in MSYS2 MinGW64 environment"
    echo "Please open MSYS2 MinGW 64-bit terminal and run this script"
    exit 1
fi

echo "MSYS2 environment detected: $MSYSTEM"

# 检查vcpkg环境变量
if [ -z "$VCPKG_ROOT" ]; then
    echo "Error: VCPKG_ROOT environment variable not set"
    echo "Please set it to your vcpkg installation path, for example:"
    echo "  export VCPKG_ROOT=/home/<USER>/vcpkg"
    echo "  or"
    echo "  export VCPKG_ROOT=/c/vcpkg"
    exit 1
fi

echo "Using vcpkg at: $VCPKG_ROOT"

# 检查vcpkg是否存在
if [ ! -f "$VCPKG_ROOT/vcpkg.exe" ]; then
    echo "Error: vcpkg.exe not found at $VCPKG_ROOT"
    echo "Please check your VCPKG_ROOT path"
    exit 1
fi

# 检查MinIO是否已安装
echo "Checking MinIO installation..."
if $VCPKG_ROOT/vcpkg.exe list | grep -q minio; then
    echo "✓ MinIO found:"
    $VCPKG_ROOT/vcpkg.exe list | grep minio
else
    echo "✗ MinIO not found. Installing..."
    echo "Please install MinIO first:"
    echo "  $VCPKG_ROOT/vcpkg.exe install minio-cpp:x64-mingw-dynamic"
    echo "Then run this script again."
    exit 1
fi

# 检查其他依赖
echo "Checking other dependencies..."
for pkg in curl openssl nlohmann-json opencv4; do
    if $VCPKG_ROOT/vcpkg.exe list | grep -q "$pkg:x64-mingw-dynamic"; then
        echo "✓ $pkg found"
    else
        echo "✗ $pkg not found, installing..."
        $VCPKG_ROOT/vcpkg.exe install $pkg:x64-mingw-dynamic
    fi
done

# 设置构建环境
export CMAKE_TOOLCHAIN_FILE="$VCPKG_ROOT/scripts/buildsystems/vcpkg.cmake"
export VCPKG_TARGET_TRIPLET="x64-mingw-dynamic"

# 创建并进入构建目录
mkdir -p build
cd build

# 清理之前的构建
rm -f CMakeCache.txt
rm -rf CMakeFiles

echo "========================================"
echo "Configuring with CMake..."
echo "========================================"

# 配置CMake
CMAKE_ARGS=(
    -G "MinGW Makefiles"
    -DCMAKE_BUILD_TYPE=Release
    -DBUILD_STATIC="$BUILD_STATIC"
    -DCMAKE_C_COMPILER=gcc
    -DCMAKE_CXX_COMPILER=g++
)

# 如果使用vcpkg（用于MinIO）
if [ -n "$CMAKE_TOOLCHAIN_FILE" ]; then
    CMAKE_ARGS+=(
        -DCMAKE_TOOLCHAIN_FILE="$CMAKE_TOOLCHAIN_FILE"
        -DVCPKG_TARGET_TRIPLET="$VCPKG_TARGET_TRIPLET"
    )
fi

# 静态编译特殊配置
if [ "$BUILD_STATIC" = "ON" ]; then
    CMAKE_ARGS+=(
        -DCMAKE_FIND_LIBRARY_SUFFIXES=".a"
        -DBUILD_SHARED_LIBS=OFF
    )
    echo "Configuring for static linking..."
fi

cmake .. "${CMAKE_ARGS[@]}"

if [ $? -ne 0 ]; then
    echo "❌ CMake configuration failed!"
    echo "Please check the error messages above"
    exit 1
fi

echo "========================================"
echo "Building..."
echo "========================================"

# 构建
make -j$(nproc)

if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    echo "Please check the error messages above"
    exit 1
fi

echo "========================================"
echo "Build completed successfully! ✓"
echo "========================================"

# 查找生成的库文件
echo "Looking for generated library files..."
for lib in libdataset_export.dll dataset_export.dll libdataset_export.so; do
    if [ -f "$lib" ]; then
        echo "✓ Found: $lib"
        
        # 复制到Java resources目录
        JAVA_RESOURCES_DIR="../ylzx-annotation/src/main/resources/native"
        if [ -d "$JAVA_RESOURCES_DIR" ]; then
            echo "Copying to Java resources directory..."
            cp "$lib" "$JAVA_RESOURCES_DIR/"
            if [ $? -eq 0 ]; then
                echo "✓ Library copied to: $JAVA_RESOURCES_DIR/$lib"
            else
                echo "❌ Failed to copy library"
            fi
        else
            echo "⚠ Java resources directory not found: $JAVA_RESOURCES_DIR"
            echo "Please copy $lib manually to your Java project"
        fi
        break
    fi
done

# 显示依赖信息
echo ""
echo "Library dependencies:"
if command -v ldd >/dev/null 2>&1 && [ -f "libdataset_export.dll" ]; then
    ldd libdataset_export.dll | head -10
elif command -v objdump >/dev/null 2>&1 && [ -f "libdataset_export.dll" ]; then
    objdump -p libdataset_export.dll | grep "DLL Name" | head -10
fi

echo ""
echo "========================================"
echo "Build Summary"
echo "========================================"
echo "✓ Environment: $MSYSTEM"
echo "✓ Compiler: $(gcc --version | head -1)"
echo "✓ vcpkg: $VCPKG_ROOT"
echo "✓ Target: $VCPKG_TARGET_TRIPLET"
echo ""
echo "Next steps:"
echo "1. Test the Java application"
echo "2. Check MinIO connectivity"
echo "3. Run image processing tests"
echo ""
echo "If you encounter issues:"
echo "1. Check that all DLL dependencies are available"
echo "2. Verify MinIO configuration in Java"
echo "3. Check the application logs"

package com.ruoyi.web.domain;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.core.web.entity.BaseTableEntity;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@TableName("sys_file_info")
public class FileInfoEntity extends BaseTableEntity {

    private String ownerId;//拥有者id

    private String platform;//存储平台
    private String storagePath;//存储路径，形如：xxxx/
    private String fileName;//当前文件名
    private String originalFilename;//源文件名

    private Long fileSize;//文件大小
    private String ext;//文件后缀
    private String url;//地址
    private String thumbUrl;//缩略图地址
    private String contentType;//文件类型
    private String thFilename;//缩略图文件名
    private Long thSize;//缩略图文件大小

    private Boolean isStatic = false;//是否是附件文件
    private Long sort;//排序字段
    private String remark;//备注
    private Boolean delFlag = false;//删除标记

    // 如果是null,返回0
    public Long getSort() {
        return sort == null ? 0 : sort;
    }

    public String getStoragePath() {
        if (StrUtil.isNotBlank(this.storagePath)) {
            return storagePath.replaceFirst("^/", "");
        }
        return storagePath;
    }
}

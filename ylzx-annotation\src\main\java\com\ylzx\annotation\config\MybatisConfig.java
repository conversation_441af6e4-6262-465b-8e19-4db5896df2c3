package com.ylzx.annotation.config;

import com.ylzx.annotation.domain.enums.AnnotationStatus;
import com.ylzx.annotation.handler.AnnotationStatusTypeHandler;
import org.mybatis.spring.boot.autoconfigure.ConfigurationCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @comment
 * @Version: 1.0
 * @Date 2025 07 01 09 21
 **/
@Configuration
public class MybatisConfig {

    @Bean
    public ConfigurationCustomizer typeHandlerRegistry() {
        return configuration -> {
            // 注册自定义枚举处理器
            configuration.getTypeHandlerRegistry().register(AnnotationStatus.class, AnnotationStatusTypeHandler.class);
        };
    }
}
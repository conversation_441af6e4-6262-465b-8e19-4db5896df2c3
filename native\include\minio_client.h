#ifndef MINIO_CLIENT_H
#define MINIO_CLIENT_H

#include <string>
#include <vector>
#include <memory>
#include <cstdint>

#ifdef USE_MINIO
#include <miniocpp/client.h>
#include <miniocpp/args.h>
#include <miniocpp/response.h>
#include <miniocpp/error.h>
#endif

namespace MinioIntegration {

/**
 * MinIO配置结构
 */
struct MinioConfig {
    std::string endpoint;
    std::string accessKey;
    std::string secretKey;
    std::string bucketName;
    bool useSSL = false;
    std::string region = "us-east-1";
};

/**
 * MinIO操作结果
 */
struct MinioResult {
    bool success = false;
    std::string message;
    std::string etag;
    size_t size = 0;
};

/**
 * MinIO客户端封装类
 */
class MinioClient {
public:
    MinioClient();
    ~MinioClient();

    /**
     * 初始化MinIO客户端
     * @param config MinIO配置
     * @return 是否初始化成功
     */
    bool initialize(const MinioConfig& config);

    /**
     * 检查存储桶是否存在
     * @param bucketName 存储桶名称
     * @return 是否存在
     */
    bool bucketExists(const std::string& bucketName);

    /**
     * 创建存储桶
     * @param bucketName 存储桶名称
     * @return 是否创建成功
     */
    bool createBucket(const std::string& bucketName);

    /**
     * 从MinIO下载文件到内存
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param data 输出数据缓冲区
     * @return 操作结果
     */
    MinioResult downloadToMemory(const std::string& bucketName, 
                                const std::string& objectName,
                                std::vector<uint8_t>& data);

    /**
     * 从内存上传文件到MinIO
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param data 数据缓冲区
     * @param contentType 内容类型
     * @return 操作结果
     */
    MinioResult uploadFromMemory(const std::string& bucketName,
                                const std::string& objectName,
                                const std::vector<uint8_t>& data,
                                const std::string& contentType = "application/octet-stream");

    /**
     * 从MinIO下载文件到本地文件
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param localPath 本地文件路径
     * @return 操作结果
     */
    MinioResult downloadToFile(const std::string& bucketName,
                              const std::string& objectName,
                              const std::string& localPath);

    /**
     * 从本地文件上传到MinIO
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param localPath 本地文件路径
     * @param contentType 内容类型
     * @return 操作结果
     */
    MinioResult uploadFromFile(const std::string& bucketName,
                              const std::string& objectName,
                              const std::string& localPath,
                              const std::string& contentType = "application/octet-stream");

    /**
     * 删除对象
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 操作结果
     */
    MinioResult removeObject(const std::string& bucketName,
                            const std::string& objectName);

    /**
     * 获取对象信息
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @return 操作结果（size字段包含文件大小）
     */
    MinioResult getObjectInfo(const std::string& bucketName,
                             const std::string& objectName);

    /**
     * 生成预签名URL
     * @param bucketName 存储桶名称
     * @param objectName 对象名称
     * @param expiry 过期时间（秒）
     * @return 预签名URL，失败返回空字符串
     */
    std::string getPresignedUrl(const std::string& bucketName,
                               const std::string& objectName,
                               int expiry = 3600);

    /**
     * 检查客户端是否已初始化
     * @return 是否已初始化
     */
    bool isInitialized() const { return initialized_; }

    /**
     * 获取最后的错误信息
     * @return 错误信息
     */
    std::string getLastError() const { return lastError_; }

private:
    bool initialized_;
    std::string lastError_;
    MinioConfig config_;

#ifdef USE_MINIO
    std::unique_ptr<minio::s3::Client> client_;
#endif

    /**
     * 设置错误信息
     * @param error 错误信息
     */
    void setError(const std::string& error);

    /**
     * 清除错误信息
     */
    void clearError();
};

/**
 * 全局MinIO客户端实例管理
 */
class MinioManager {
public:
    /**
     * 获取全局MinIO客户端实例
     * @return MinIO客户端指针
     */
    static MinioClient* getInstance();

    /**
     * 初始化全局MinIO客户端
     * @param config MinIO配置
     * @return 是否初始化成功
     */
    static bool initialize(const MinioConfig& config);

    /**
     * 销毁全局MinIO客户端实例
     */
    static void destroy();

private:
    static std::unique_ptr<MinioClient> instance_;
    static bool initialized_;
};

} // namespace MinioIntegration

#endif // MINIO_CLIENT_H

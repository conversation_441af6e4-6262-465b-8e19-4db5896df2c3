package com.ylzx.annotation.service;

import org.springframework.web.multipart.MultipartFile;
import java.io.InputStream;
import java.util.List;

/**
 * MinIO文件服务接口
 * 用于与ylzx-file服务交互
 * 
 * <AUTHOR>
 */
public interface MinioFileService {

    /**
     * 上传文件到MinIO
     * 
     * @param file 上传的文件
     * @param folderPath 文件夹路径
     * @param createBy 创建者
     * @return 文件ID
     */
    String uploadFile(MultipartFile file, String folderPath, String createBy);

    /**
     * 批量上传文件到MinIO
     * 
     * @param files 文件列表
     * @param folderPath 文件夹路径
     * @param createBy 创建者
     * @return 文件ID列表
     */
    List<String> uploadFiles(List<MultipartFile> files, String folderPath, String createBy);

    /**
     * 从MinIO下载文件
     * 
     * @param fileId 文件ID
     * @return 文件输入流
     */
    InputStream downloadFile(String fileId);

    /**
     * 删除MinIO中的文件
     * 
     * @param fileId 文件ID
     * @return 是否删除成功
     */
    boolean deleteFile(String fileId);

    /**
     * 批量删除MinIO中的文件
     * 
     * @param fileIds 文件ID列表
     * @return 是否删除成功
     */
    boolean deleteFiles(List<String> fileIds);

    /**
     * 获取文件的预签名URL
     * 
     * @param fileId 文件ID
     * @return 预签名URL
     */
    String getPresignedUrl(String fileId);

    /**
     * 批量获取文件的预签名URL
     * 
     * @param fileIds 文件ID列表
     * @return 预签名URL映射
     */
    java.util.Map<String, String> getPresignedUrls(List<String> fileIds);

    /**
     * 检查文件是否存在
     * 
     * @param md5Hash 文件MD5哈希值
     * @return 文件ID（如果存在）
     */
    String checkFileExists(String md5Hash);

    /**
     * 获取文件信息
     * 
     * @param fileId 文件ID
     * @return 文件信息
     */
    FileInfoDto getFileInfo(String fileId);

    /**
     * 文件信息DTO
     */
    class FileInfoDto {
        private String id;
        private String originalFilename;
        private String fileName;
        private Long fileSize;
        private String contentType;
        private String fileExtension;
        private String filePath;
        private String fileUrl;
        private String md5Hash;
        private String thumbnailUrl;
        private java.time.LocalDateTime createTime;

        // Getters and Setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        
        public String getOriginalFilename() { return originalFilename; }
        public void setOriginalFilename(String originalFilename) { this.originalFilename = originalFilename; }
        
        public String getFileName() { return fileName; }
        public void setFileName(String fileName) { this.fileName = fileName; }
        
        public Long getFileSize() { return fileSize; }
        public void setFileSize(Long fileSize) { this.fileSize = fileSize; }
        
        public String getContentType() { return contentType; }
        public void setContentType(String contentType) { this.contentType = contentType; }
        
        public String getFileExtension() { return fileExtension; }
        public void setFileExtension(String fileExtension) { this.fileExtension = fileExtension; }
        
        public String getFilePath() { return filePath; }
        public void setFilePath(String filePath) { this.filePath = filePath; }
        
        public String getFileUrl() { return fileUrl; }
        public void setFileUrl(String fileUrl) { this.fileUrl = fileUrl; }
        
        public String getMd5Hash() { return md5Hash; }
        public void setMd5Hash(String md5Hash) { this.md5Hash = md5Hash; }
        
        public String getThumbnailUrl() { return thumbnailUrl; }
        public void setThumbnailUrl(String thumbnailUrl) { this.thumbnailUrl = thumbnailUrl; }
        
        public java.time.LocalDateTime getCreateTime() { return createTime; }
        public void setCreateTime(java.time.LocalDateTime createTime) { this.createTime = createTime; }
    }
}

package com.ylzx.common.utils;

import org.apache.commons.compress.archivers.tar.TarArchiveEntry;
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipFile;
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Enumeration;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/**
 * 压缩文件处理工具类
 *
 * <AUTHOR>
 */
public class ArchiveUtils {

    private static final Logger log = LoggerFactory.getLogger(ArchiveUtils.class);
    private static final int BUFFER_SIZE = 4096;

    /**
     * 解压 ZIP 文件
     *
     * @param source      源文件路径
     * @param destination 目标目录路径
     * @throws IOException IO异常
     */
    public static void unzip(Path source, Path destination) throws IOException {
        log.info("开始解压ZIP文件: {} 到 {}", source, destination);
        try (InputStream fis = Files.newInputStream(source);
             BufferedInputStream bis = new BufferedInputStream(fis);
             ZipInputStream zis = new ZipInputStream(bis)) {
            ZipEntry entry;
            while ((entry = zis.getNextEntry()) != null) {
                Path entryPath = destination.resolve(entry.getName());
                if (entry.isDirectory()) {
                    Files.createDirectories(entryPath);
                } else {
                    Files.createDirectories(entryPath.getParent());
                    try (OutputStream fos = Files.newOutputStream(entryPath);
                         BufferedOutputStream bos = new BufferedOutputStream(fos, BUFFER_SIZE)) {
                        zis.transferTo(bos);
                    }
                }
                zis.closeEntry();
            }
        }
        log.info("ZIP文件解压完成: {}", source);
    }


    public static void unzip(File zipFile, File destDir) throws IOException {
        try (ZipFile zip = new ZipFile(zipFile)) {
            Enumeration<ZipArchiveEntry> entries = zip.getEntries();
            while (entries.hasMoreElements()) {
                ZipArchiveEntry entry = entries.nextElement();
                File entryFile = new File(destDir, entry.getName());

                // 安全校验：防止ZIP路径穿越攻击
                String canonicalPath = entryFile.getCanonicalPath();
                if (!canonicalPath.startsWith(destDir.getCanonicalPath() + File.separator)) {
                    throw new IOException("非法ZIP条目路径: " + entry.getName());
                }

                if (entry.isDirectory()) {
                    entryFile.mkdirs();
                } else {
                    entryFile.getParentFile().mkdirs();
                    try (InputStream in = zip.getInputStream(entry);
                         FileOutputStream out = new FileOutputStream(entryFile)) {
                        IOUtils.copy(in, out);
                    }
                }
            }
        }
    }

    /**
     * 解压 TAR.GZ 文件
     *
     * @param source      源文件路径
     * @param destination 目标目录路径
     * @throws IOException IO异常
     */
    public static void untarGz(Path source, Path destination) throws IOException {
        log.info("开始解压TAR.GZ文件: {} 到 {}", source, destination);
        try (InputStream fis = Files.newInputStream(source);
             BufferedInputStream bis = new BufferedInputStream(fis);
             GzipCompressorInputStream gzis = new GzipCompressorInputStream(bis);
             TarArchiveInputStream tis = new TarArchiveInputStream(gzis)) {

            TarArchiveEntry entry;
            while ((entry = (TarArchiveEntry) tis.getNextEntry()) != null) {
                Path entryPath = destination.resolve(entry.getName());
                if (entry.isDirectory()) {
                    Files.createDirectories(entryPath);
                } else {
                    Files.createDirectories(entryPath.getParent());
                    // 使用 transferTo 简化流复制
                    try (OutputStream out = Files.newOutputStream(entryPath)) {
                        tis.transferTo(out);
                    }
                }
            }
        }
        log.info("TAR.GZ文件解压完成: {}", source);
    }

} 
# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a data annotation platform built with Spring Boot 3.x, Java 21, and PostgreSQL. The platform provides tools for image annotation, dataset management, and export functionality in various formats (COCO, VOC, YOLO).

Key features include:
- Image annotation tools with rectangle and polygon support
- Dataset management and organization
- Export to multiple formats (COCO, VOC, YOLO)
- Image processing capabilities (cropping, transformation)
- High-performance C++ native library for image processing via JNI

## Repository Structure

```
data-annotation-platform/
├── ylzx-admin/           # Main application entry point
├── ylzx-annotation/      # Core annotation functionality
├── ylzx-common/          # Shared utilities and constants
├── ylzx-framework/       # Framework-level components (security, caching)
├── ylzx-system/          # System management (users, roles, permissions)
├── ylzx-flowable/        # Workflow engine integration
├── ylzx-generator/       # Code generation tools
├── ylzx-quartz/          # Scheduled tasks
├── native/               # C++ native library for high-performance operations
├── annotation_data/      # Data storage directory for annotations
└── sql/                  # Database initialization scripts
```

## Common Development Commands

### Building the Project
```bash
# Build the entire project
mvn clean install

# Build without running tests
mvn clean install -DskipTests

# Build the native library (requires CMake and C++ compiler)
cd native
./build.sh  # Linux/macOS
# or
build.bat   # Windows
```

### Running the Application
```bash
# Run from command line
mvn spring-boot:run -pl ylzx-admin

# Or run the main class directly
# YlzxApplication.java in ylzx-admin module
```

### Running Tests
```bash
# Run all tests
mvn test

# Run tests for a specific module
mvn test -pl ylzx-annotation

# Run a specific test class
mvn test -Dtest=ImageCropServiceTest -pl ylzx-annotation

# Note: Limited test coverage - only basic tests in ylzx-file module
```

### Database Migration
```bash
# Initialize database with scripts in sql/ directory
# Default database: PostgreSQL
# Database name: data-annotation-platform
# Required PostgreSQL extension: pg_trgm (for text search functionality)
```

## Architecture Overview

### Backend Stack
- **Framework**: Spring Boot 3.5.0 with Jakarta EE
- **Language**: Java 21
- **Database**: PostgreSQL (with MySQL support available)
- **ORM**: MyBatis-Plus 3.5.5
- **Security**: Spring Security 6.5.0
- **Caching**: Redis
- **Build Tool**: Maven
- **API Documentation**: SpringDoc OpenAPI 2.2.0
- **File Storage**: MinIO 8.5.12
- **Utils**: Hutool 5.8.32, FastJSON 2.0.53

### Key Modules
1. **ylzx-annotation**: Core annotation functionality including image processing, dataset export, and annotation management
2. **ylzx-admin**: Main application entry point and configuration
3. **ylzx-framework**: Security, caching, and cross-cutting concerns
4. **ylzx-system**: User management, roles, permissions, and system configuration
5. **ylzx-file**: File upload/download and storage management (MinIO integration)
6. **ylzx-flowable**: Workflow engine integration (Flowable 7.1.0)
7. **ylzx-generator**: Code generation tools
8. **ylzx-quartz**: Scheduled task management

### Native Library Integration
The platform includes a high-performance C++ native library for image processing operations:
- Located in the `native/` directory
- Integrated via JNI through `DatasetExportNative.java` at `ylzx-annotation/src/main/java/com/ylzx/annotation/jni/`
- Provides optimized image transformation and dataset export capabilities
- Supports multiple export formats (COCO, VOC, YOLO)
- Includes batch processing and smart cropping functionality
- Cross-platform support (Windows, Linux, macOS) with automatic library loading

### Data Flow
1. Images are uploaded and stored in the `annotation_data/` directory
2. Annotations are created and managed through the web interface
3. Datasets can be exported in various formats using the native library
4. Image processing operations (cropping, transformation) are handled by the C++ library for performance

## Configuration Files
- `application.yml`: Main configuration file (server, database, redis, annotation paths)
- `application-druid.yml`: Database connection pool configuration
- `application-prod.yml`: Production environment configuration
- Configuration includes annotation base path (`./annotation_data/`) and auto-processing settings
- API documentation available at: `/dev-api/swagger-ui/index.html`

## Important Directories
- `annotation_data/`: Root directory for all annotation-related files (configurable via `ylzx.annotation.base-path`)
  - `custom-upload/`: Upload directory (configurable)
  - `custom-extraction/`: Extraction directory (configurable)
  - `custom-scan/`: Scan directory (configurable)
  - `custom-processed/`: Processed files directory (configurable)
  - `custom-error/`: Failed processing directory (configurable)
- `native/`: C++ native library source code and compiled libraries
- `sql/`: Database initialization scripts
- `uploadPath/`: Default upload directory for user files
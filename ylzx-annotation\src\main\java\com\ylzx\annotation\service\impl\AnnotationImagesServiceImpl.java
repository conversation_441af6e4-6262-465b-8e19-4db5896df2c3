package com.ylzx.annotation.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.ylzx.annotation.domain.AnnotationCategoriesImages;
import com.ylzx.annotation.domain.AnnotationImageSources;
import com.ylzx.annotation.domain.AnnotationImages;
import com.ylzx.annotation.mapper.AnnotationCategoriesImagesMapper;

import com.ylzx.annotation.mapper.AnnotationImageSourcesMapper;
import com.ylzx.annotation.mapper.AnnotationImagesMapper;
import com.ylzx.file.service.FileService;
import com.ylzx.annotation.service.AnnotationImagesService;
import com.ylzx.common.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;

/**
 * 标注图片Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
public class AnnotationImagesServiceImpl extends ServiceImpl<AnnotationImagesMapper, AnnotationImages> implements AnnotationImagesService {
    private static final Logger log = LoggerFactory.getLogger(AnnotationImagesServiceImpl.class);

    @Autowired
    private AnnotationImagesMapper annotationImagesMapper;

    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private AnnotationCategoriesImagesMapper categoriesImagesMapper;

    @Autowired
    private AnnotationImageSourcesMapper annotationImageSourcesMapper;

    @Autowired
    private FileService fileService;

    private static final List<String> SUPPORTED_IMAGE_EXTENSIONS = Arrays.asList(
            ".png", ".jpg", ".jpeg", ".gif", ".webp", ".heic", ".heif"
    );

    /**
     * 查询标注图片
     *
     * @param imageId 标注图片主键
     * @return 标注图片
     */
    @Override
    public AnnotationImages selectAnnotationImagesByImageId(Long imageId) {
        return annotationImagesMapper.selectAnnotationImagesByImageId(imageId);
    }

    /**
     * 查询标注图片列表
     *
     * @param annotationImages 标注图片
     * @return 标注图片集合
     */
    @Override
    public List<AnnotationImages> selectAnnotationImagesList(AnnotationImages annotationImages) {
        List<AnnotationImages> annotationImagesList = annotationImagesMapper.selectAnnotationImagesList(annotationImages);


//        List<AnnotationCategories> annotationCategoriesList = annotationCategoriesMapper.selectAnnotationCategoriesList(new AnnotationCategories());
        List<AnnotationImageSources> annotationImageSourcesList = annotationImageSourcesMapper.selectAnnotationImageSourcesList(AnnotationImageSources.builder().build());
        for (AnnotationImages images : annotationImagesList) {

            // 设置分类名称
//            for (AnnotationCategories category : annotationCategoriesList) {
//                if (images.getCategoryId().equals(category.getCategoryId())) {
//                    images.setCategoryName(category.getName());
//                    break;
//                }
//            }
            // 设置数据源名称
            for (AnnotationImageSources sources : annotationImageSourcesList) {
                if (images.getSourceId().equals(sources.getSourceId())) {
                    images.setSourceName(sources.getSourceName());
                    break;
                }
            }
        }

        return annotationImagesList;
    }

    /**
     * 新增标注图片
     *
     * @param annotationImages 标注图片
     * @return 结果
     */
    @Override
    public int insertAnnotationImages(AnnotationImages annotationImages) {
        return annotationImagesMapper.insertAnnotationImages(annotationImages);
    }

    /**
     * 批量新增标注图片
     *
     * @param annotationImagesList 标注图片列表
     * @return 结果
     */
    @Override
    public int insertBatch(List<AnnotationImages> annotationImagesList) {
        if (annotationImagesList == null || annotationImagesList.isEmpty()) {
            return 0;
        }

        // 设置创建时间
        LocalDateTime now = LocalDateTime.now();
        for (AnnotationImages image : annotationImagesList) {
            if (image.getCreateTime() == null) {
                image.setCreateTime(now);
            }
            if (image.getUpdateTime() == null) {
                image.setUpdateTime(now);
            }
        }

        return annotationImagesMapper.insertBatch(annotationImagesList);
    }

    /**
     * 修改标注图片
     *
     * @param annotationImages 标注图片
     * @return 结果
     */
    @Override
    public int updateAnnotationImages(AnnotationImages annotationImages) {
        return annotationImagesMapper.updateAnnotationImages(annotationImages);
    }

    /**
     * 批量删除标注图片
     *
     * @param imageIds 需要删除的标注图片主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationImagesByImageIds(Long[] imageIds) {
        return annotationImagesMapper.deleteAnnotationImagesByImageIds(imageIds);
    }

    /**
     * 删除标注图片信息
     *
     * @param imageId 标注图片主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationImagesByImageId(Long imageId) {
        return annotationImagesMapper.deleteAnnotationImagesByImageId(imageId);
    }

    @Override
    public void processImagesFromSourceId(Long sourceId, Long categoryId) {
        List<AnnotationImageSources> annotationImageSources = annotationImageSourcesMapper.selectAnnotationImageSourcesList(
            AnnotationImageSources.builder().sourceId(sourceId).build());

        if (annotationImageSources.isEmpty()) {
            log.error("数据源未找到，sourceId: {}", sourceId);
            throw new IllegalArgumentException("数据源未找到，sourceId: " + sourceId);
        }

        AnnotationImageSources source = annotationImageSources.get(0);
        if (source.getFileId() == null || source.getFileId().isEmpty()) {
            log.error("数据源文件ID为空，sourceId: {}", sourceId);
            throw new IllegalArgumentException("数据源文件ID为空，sourceId: " + sourceId);
        }

        // 处理MinIO中的图片文件
        processImagesFromMinIO(sourceId, categoryId, source.getFileId());
    }

    /**
     * 处理MinIO中的图片文件
     */
    private void processImagesFromMinIO(Long sourceId, Long categoryId, String fileId) {
        try {
            log.info("开始处理MinIO中的图片文件，sourceId: {}, categoryId: {}, fileId: {}", sourceId, categoryId, fileId);

            // 从MinIO获取文件夹中的所有图片文件
            List<com.ylzx.file.domain.FileInfo> imageFiles = fileService.listFolderFiles("annotation/sources/" + fileId);

            if (imageFiles.isEmpty()) {
                log.warn("MinIO中没有找到图片文件，fileId: {}", fileId);
                return;
            }

            List<AnnotationImages> imagesToStore = new ArrayList<>();

            for (com.ylzx.file.domain.FileInfo fileInfo : imageFiles) {
                try {
                    if (isSupportedImageFile(fileInfo.getOriginalFilename())) {
                        AnnotationImages image = createImageRecord(fileInfo, sourceId, categoryId);
                        if (image != null) {
                            imagesToStore.add(image);
                            log.debug("准备保存图片记录: {}", fileInfo.getOriginalFilename());
                        }
                    }
                } catch (Exception e) {
                    log.error("处理单个图片文件失败: {}", fileInfo.getOriginalFilename(), e);
                }
            }

            // 批量保存图片记录
            if (!imagesToStore.isEmpty()) {
                batchInsertImages(imagesToStore);
                log.info("成功处理 {} 张图片，sourceId: {}, categoryId: {}", imagesToStore.size(), sourceId, categoryId);

                // 保存图片与分类的关联关系
                if (categoryId != null) {
                    saveImageCategoryAssociations(imagesToStore, categoryId);
                }
            } else {
                log.warn("MinIO中没有找到有效的图片文件，fileId: {}", fileId);
            }

        } catch (Exception e) {
            log.error("处理MinIO图片文件失败，sourceId: {}, categoryId: {}, fileId: {}", sourceId, categoryId, fileId, e);
            throw new RuntimeException("处理MinIO图片文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 创建图片记录
     */
    private AnnotationImages createImageRecord(com.ylzx.file.domain.FileInfo fileInfo, Long sourceId, Long categoryId) {
        try {
            // 检查MD5是否重复
            String md5Hash = fileInfo.getMd5Hash();
            if (md5Hash != null) {
                int count = annotationImagesMapper.countByMd5Hash(md5Hash);
                if (count > 0) {
                    log.debug("跳过重复图片（MD5已存在）: {} - MD5: {}", fileInfo.getOriginalFilename(), md5Hash);
                    return null;
                }
            }

            AnnotationImages image = new AnnotationImages();
            image.setSourceId(sourceId);
            image.setCategoryId(categoryId);
            image.setFileId(fileInfo.getId());
            image.setOriginalFilename(fileInfo.getOriginalFilename());
            image.setFileSizeBytes(fileInfo.getFileSize());
            image.setMd5Hash(md5Hash);
            image.setUploadedAt(LocalDateTime.now());
            image.setCreateBy(SecurityUtils.getUsername());

            // 从MinIO获取图片尺寸信息（如果需要的话）
            // TODO: 可以通过下载图片文件来获取尺寸信息，但这会增加网络开销
            // 暂时设置为默认值，后续可以优化
            image.setWidth(0L);
            image.setHeight(0L);

            return image;

        } catch (Exception e) {
            log.error("创建图片记录失败: {}", fileInfo.getOriginalFilename(), e);
            return null;
        }
    }

    /**
     * 检查是否为支持的图片文件
     */
    private boolean isSupportedImageFile(String filename) {
        if (filename == null) return false;
        String lowerName = filename.toLowerCase();
        return SUPPORTED_IMAGE_EXTENSIONS.stream().anyMatch(lowerName::endsWith);
    }

    /**
     * 保存图片与分类的关联关系
     */
    private void saveImageCategoryAssociations(List<AnnotationImages> images, Long categoryId) {
        try {
            // 获取已保存的图片记录（包含imageId）
            List<AnnotationImages> savedImages = new ArrayList<>();
            for (AnnotationImages image : images) {
                if (image.getMd5Hash() != null) {
                    List<AnnotationImages> found = annotationImagesMapper.selectAnnotationImagesList(
                        AnnotationImages.builder().md5Hash(image.getMd5Hash()).build());
                    if (!found.isEmpty()) {
                        savedImages.add(found.get(0));
                    }
                }
            }

            // 创建关联关系
            List<AnnotationCategoriesImages> associations = new ArrayList<>();
            for (AnnotationImages image : savedImages) {
                AnnotationCategoriesImages association = new AnnotationCategoriesImages();
                association.setCategoryId(categoryId);
                association.setImageId(image.getImageId());
                associations.add(association);
            }

            // 批量保存关联关系
            if (!associations.isEmpty()) {
                categoriesImagesMapper.insertBatch(associations);
                log.info("成功保存 {} 条图片-分类关联关系", associations.size());
            }

        } catch (Exception e) {
            log.error("保存图片-分类关联关系失败，categoryId: {}", categoryId, e);
        }
    }

    /**
     * 批量插入图片记录
     */
    private void batchInsertImages(List<AnnotationImages> images) {
        if (images.isEmpty()) {
            return;
        }

        final int batchSize = 500;
        log.info("正在向数据库分批保存 {} 张图片，每批 {} 条", images.size(), batchSize);

        for (int i = 0; i < images.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, images.size());
            List<AnnotationImages> batch = images.subList(i, endIndex);
            log.info("正在保存第 {} 批图片，数量: {} (总进度: {}/{})",
                    (i / batchSize + 1), batch.size(), endIndex, images.size());
            annotationImagesMapper.insertBatch(batch);
        }
    }


    // ylzx-file服务的基础URL
    private static final String FILE_SERVICE_URL = "http://localhost:8083/file/api/file";

    @Override
    public String getImagePresignedUrl(Long imageId) {
        try {
            AnnotationImages image = annotationImagesMapper.selectAnnotationImagesByImageId(imageId);
            if (image == null || image.getFileId() == null) {
                return null;
            }

            ResponseEntity<String> response = restTemplate.getForEntity(
                FILE_SERVICE_URL + "/presigned-url/" + image.getFileId(), String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                JsonNode jsonNode = objectMapper.readTree(response.getBody());
                if (jsonNode.get("code").asInt() == 200) {
                    return jsonNode.get("data").asText();
                }
            }
            return null;
        } catch (Exception e) {
            log.error("获取图片预签名URL失败: {}", imageId, e);
            return null;
        }
    }

    @Override
    public Map<String, String> getImagePresignedUrls(List<Long> imageIds) {
        Map<String, String> result = new HashMap<>();

        // 批量查询图片信息
        List<AnnotationImages> images = annotationImagesMapper.selectBatchIds(imageIds);

        // 提取fileId列表
        List<String> fileIds = images.stream()
            .filter(img -> img.getFileId() != null)
            .map(AnnotationImages::getFileId)
            .collect(Collectors.toList());

        if (fileIds.isEmpty()) {
            return result;
        }

        // 批量获取预签名URL
        for (String fileId : fileIds) {
            try {
                ResponseEntity<String> response = restTemplate.getForEntity(
                    FILE_SERVICE_URL + "/presigned-url/" + fileId, String.class);

                if (response.getStatusCode() == HttpStatus.OK) {
                    JsonNode jsonNode = objectMapper.readTree(response.getBody());
                    if (jsonNode.get("code").asInt() == 200) {
                        String url = jsonNode.get("data").asText();
                        // 找到对应的imageId
                        images.stream()
                            .filter(img -> fileId.equals(img.getFileId()))
                            .forEach(img -> result.put(img.getImageId().toString(), url));
                    }
                }
            } catch (Exception e) {
                log.error("获取预签名URL失败: {}", fileId, e);
            }
        }

        return result;
    }

    @Override
    public List<AnnotationImages> addPresignedUrlsToImages(List<AnnotationImages> images) {
        if (images == null || images.isEmpty()) {
            return images;
        }

        // 提取imageId列表
        List<Long> imageIds = images.stream()
            .map(AnnotationImages::getImageId)
            .collect(Collectors.toList());

        // 批量获取预签名URL
        Map<String, String> urlMap = getImagePresignedUrls(imageIds);

        // 为每个图片设置预签名URL
        images.forEach(image -> {
            String url = urlMap.get(image.getImageId().toString());
            if (url != null) {
                // 这里可以设置一个临时字段来存储URL，或者通过其他方式传递给前端
                // 由于AnnotationImages实体类可能没有url字段，可以考虑扩展DTO
                log.debug("图片 {} 的预签名URL: {}", image.getImageId(), url);
            }
        });

        return images;
    }
}

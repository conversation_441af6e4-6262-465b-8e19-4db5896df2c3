# 项目相关配置
ylzx:
  # 名称
  name: YLZX
  # 版本
  version: 3.8.9
  # 版权年份
  copyrightYear: 2025
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: ./uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math
  # 标注文件根路径
  annotation:
    # 文件操作的根目录（必须配置）
    base-path: ./annotation_data

    # 各个子目录名称（可选，使用自定义名称）
    upload-dir: custom-upload           # 上传文件目录
    extraction-dir: custom-extraction   # 解压文件目录
    scan-dir: custom-scan              # 扫描文件目录
    processed-dir: custom-processed     # 处理完成的文件目录
    error-dir: custom-error            # 处理失败的文件目录

    # 自动处理图片配置
    auto-process-images: true          # 是否在定时扫描时自动处理图片
    default-category-id: 1             # 自动处理图片时使用的默认分类ID（请根据实际情况修改）

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.ylzx: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  # devtools:
  #   restart:
  #     # 热部署开关
  #     enabled: true
  # Redis 配置
  data:
    redis:
      # 地址
      host: ***************
      # 端口，默认为6379
      port: 6379
      # 数据库索引
      database: 7
      # 密码
      password: 'Ylzx@2024!+9821*'
      # 连接超时时间
      timeout: 10s
      lettuce:
        pool:
          # 连接池中的最小空闲连接
          min-idle: 0
          # 连接池中的最大空闲连接
          max-idle: 8
          # 连接池的最大数据库连接数
          max-active: 8
          # #连接池最大阻塞等待时间（使用负值表示没有限制）
          max-wait: -1ms

# MinIO配置
minio:
  # MinIO服务端点
  endpoint: http://***************:9000
  # 访问密钥
  access-key: admin
  # 秘密密钥
  secret-key: 'ylzx@ww+qq2213556'
  # 存储桶名称
  bucket-name: data-annotation
  # 是否使用HTTPS
  secure: false
  # 连接超时时间（毫秒）
  connect-timeout: 10000
  # 写入超时时间（毫秒）
  write-timeout: 60000
  # 读取超时时间（毫秒）
  read-timeout: 10000
  # 预签名URL过期时间（秒）
  presigned-url-expiry: 3600


# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: abcdefghijklmnopqrstuvwxyz
  # 令牌有效期（默认30分钟）
  expireTime: 30

# MyBatis-Plus配置
mybatis-plus:
  # 搜索指定包别名
  type-aliases-package: com.ylzx.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  # 启用枚举类型处理器
  configuration:
    default-enum-type-handler: com.baomidou.mybatisplus.core.handlers.MybatisEnumTypeHandler
    map-underscore-to-camel-case: true
    cache-enabled: true
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    use-generated-keys: true
    default-executor-type: simple
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  # 全局配置
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: delFlag
      logic-delete-value: 1
      logic-not-delete-value: 0

# PageHelper分页插件
pagehelper:
  helperDialect: postgresql
  supportMethodsArguments: true
  params: count=countSql

# SpringDoc OpenAPI 3 配置
springdoc:
  # API文档配置
  api-docs:
    enabled: true # 开启OpenApi接口
    path: /dev-api/v3/api-docs  # 自定义路径，默认为 "/v3/api-docs"
  # Swagger UI配置
  swagger-ui:
    enabled: true # 开启swagger界面，依赖OpenApi，需要OpenApi同时开启
    path: /dev-api/swagger-ui/index.html # 自定义路径，默认为"/swagger-ui/index.html"
    # 配置默认展开的操作级别
    doc-expansion: none
    # 配置默认的模型展开深度
    default-models-expand-depth: 1
    # 配置默认的模型示例展开深度
    default-model-expand-depth: 1
    # 配置操作和标签的排序
    operations-sorter: alpha
    tags-sorter: alpha
  # 包扫描配置
  packages-to-scan: com.ylzx
  # 路径匹配配置
  paths-to-match: /**

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# flowable相关表
flowable:
  # true 会对数据库中所有表进行更新操作。如果表不存在，则自动创建(建议开发时使用)
  database-schema-update: true
  # 关闭定时任务JOB
  async-executor-activate: false

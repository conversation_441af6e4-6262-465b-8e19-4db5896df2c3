package com.ylzx.file.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 文件上传响应DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "文件上传响应")
public class FileUploadResponse {

    /**
     * 文件ID
     */
    @Schema(description = "文件ID", example = "1234567890abcdef")
    private String id;

    /**
     * 原始文件名
     */
    @Schema(description = "原始文件名", example = "avatar.jpg")
    private String originalFilename;

    /**
     * 文件名
     */
    @Schema(description = "存储文件名", example = "20240101_123456_avatar.jpg")
    private String fileName;

    /**
     * 文件大小（字节）
     */
    @Schema(description = "文件大小（字节）", example = "1024000")
    private Long fileSize;

    /**
     * 文件类型
     */
    @Schema(description = "文件MIME类型", example = "image/jpeg")
    private String contentType;

    /**
     * 文件扩展名
     */
    @Schema(description = "文件扩展名", example = "jpg")
    private String fileExtension;

    /**
     * 文件路径
     */
    @Schema(description = "文件路径", example = "images/avatars/20240101_123456_avatar.jpg")
    private String filePath;

    /**
     * 文件访问URL
     */
    @Schema(description = "文件访问URL", example = "http://localhost:8083/file/download/1234567890abcdef")
    private String fileUrl;

    /**
     * MD5哈希值
     */
    @Schema(description = "文件MD5哈希值", example = "d41d8cd98f00b204e9800998ecf8427e")
    private String md5Hash;

    /**
     * 缩略图URL
     */
    @Schema(description = "缩略图访问URL", example = "http://localhost:8083/file/thumbnail/1234567890abcdef")
    private String thumbnailUrl;

    /**
     * 缩略图大小
     */
    @Schema(description = "缩略图大小（字节）", example = "51200")
    private Long thumbnailSize;

    /**
     * 图片宽度（像素）
     */
    @Schema(description = "图片宽度（像素），仅图片文件有效", example = "1920")
    private Integer imageWidth;

    /**
     * 图片高度（像素）
     */
    @Schema(description = "图片高度（像素），仅图片文件有效", example = "1080")
    private Integer imageHeight;

    /**
     * 上传时间
     */
    @Schema(description = "上传时间", example = "2024-01-01 12:34:56")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 备注
     */
    @Schema(description = "备注信息", example = "用户头像")
    private String remark;
}

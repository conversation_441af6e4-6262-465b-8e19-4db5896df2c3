# Tomcat
server:
  port: 19300

# Spring
spring:
  application:
    # 应用名称
    name: ruoyi-file
  profiles:
    # 环境配置
    active: prod
  cloud:
    inetutils:
      preferred-networks:
        - 172.18.0
        - 192.168.10
    nacos:
      discovery:
        # 服务注册地址
        server-addr: ***************:8848
        namespace: 24aaca1b-7113-4947-ba4e-5314fdc2011f
        username: nacos
        password: 'nacos@abc123!'
      config:
        # 配置中心地址
        server-addr: ***************:8848
        username: nacos
        password: 'nacos@abc123!'
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - common-redis.yml
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
        namespace: 24aaca1b-7113-4947-ba4e-5314fdc2011f

---
# 本地文件上传，参照测试的配置
file:
  domain: http://***************/prod-api/file
  addpaths:
    - /assets/**
  #需要排除校验的文件路径：appUpload-app相关文件
  excludepaths:
    - /assets/appUpload/**
    - /assets/signName/**
  #前缀staticFile保持不变，配置后面的路径
  prefix:
    - /appUpload
    - /fykjFile
    - /OldFile
    - /OldFileMP
    - /signName
  #存储磁盘路径地址
  path:
    - /appUpload/
    - /fykjFile/
    - /FtpFile/
    - /FtpFile/
    - /signName/

spring:
  servlet:
    multipart:
      max-file-size: 2000MB
      max-request-size: 2000MB
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          url: ********************************************************************************************************************************************************************************************
          username: root
          password: 'Ylzx@2000+!#-2'
          #          driver-class-name: com.mysql.cj.jdbc.Driver
          driver-class-name: com.p6spy.engine.spy.P6SpyDriver
          # 连接池配置，以HikariCP为例
          type: com.zaxxer.hikari.HikariDataSource
          hikari:
            minimum-idle: 5
            maximum-pool-size: 20
            is-auto-commit: true
            idle-timeout: 30000
            max-lifetime: 1800000
            connection-timeout: 30000

# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  #configLocation: classpath:mybatis/mybatis-config.xml
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
  global-config:
    db-config:
      id-type: assign_id
      capital-mode: false
      table-underline: true
      logic-delete-value: 1
      logic-not-delete-value: 0
      table-prefix: sys_

dromara:
  x-file-storage:
    default-platform: minio
    thumbnail-suffix: ".min.jpg" #缩略图后缀，例如【.min.jpg】【.png】
    minio:
      - platform: minio # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: admin
        secret-key: 'ylzx@ww+qq2213556'
        domain: http://***************/mini/
        end-point: http://***************:9000
        bucket-name: test
        base-path:  # 基础路径

      - platform: minio2 # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: admin
        secret-key: 'ylzx@ww+qq2213556'
        domain: http://***************/mini/
        end-point: http://***************:9000
        bucket-name: test2
        base-path:  # 基础路径

      - platform: fykj # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: admin
        secret-key: 'ylzx@ww+qq2213556'
        domain: http://***************/mini/
        end-point: http://***************:9000
        bucket-name: fykj
        base-path:  # 基础路径

      - platform: mpkj # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: admin
        secret-key: 'ylzx@ww+qq2213556'
        domain: http://***************/mini/
        end-point: http://***************:9000
        bucket-name: mpkj
        base-path:  # 基础路径

      - platform: system # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: admin
        secret-key: 'ylzx@ww+qq2213556'
        domain: http://***************/mini/
        end-point: http://***************:9000
        bucket-name: system
        base-path:  # 基础路径

      - platform: ylzx # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: admin
        secret-key: 'ylzx@ww+qq2213556'
        domain: http://***************/mini/
        end-point: http://***************:9000
        bucket-name: ylzx
        base-path:  # 基础路径

      - platform: jgjc # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: admin
        secret-key: 'ylzx@ww+qq2213556'
        domain: http://***************/mini/
        end-point: http://***************:9000
        bucket-name: jgjc
        base-path:  # 基础路径

      - platform: wdzy # 存储平台标识
        enable-storage: true  # 启用存储
        access-key: admin
        secret-key: 'ylzx@ww+qq2213556'
        domain: http://***************/mini/
        end-point: http://***************:9000
        bucket-name: wdzy
        base-path:  # 基础路径
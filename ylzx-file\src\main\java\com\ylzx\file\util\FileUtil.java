package com.ylzx.file.util;

import cn.hutool.core.io.FileTypeUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 文件工具类
 * 
 * <AUTHOR>
 */
@Slf4j
public class FileUtil {

    /**
     * 日期格式化器
     */
    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy/MM/dd");

    /**
     * 图片文件扩展名
     */
    private static final String[] IMAGE_EXTENSIONS = {"jpg", "jpeg", "png", "gif", "bmp", "webp"};

    /**
     * 生成文件名
     */
    public static String generateFileName(String originalFilename) {
        String extension = getFileExtension(originalFilename);
        String timestamp = String.valueOf(System.currentTimeMillis());
        String uuid = IdUtil.fastSimpleUUID().substring(0, 8);
        return timestamp + "_" + uuid + (StrUtil.isNotBlank(extension) ? "." + extension : "");
    }

    /**
     * 生成文件路径
     */
    public static String generateFilePath(String folderPath, String fileName) {
        String datePath = LocalDateTime.now().format(DATE_FORMATTER);
        
        if (StrUtil.isBlank(folderPath)) {
            return datePath + "/" + fileName;
        }
        
        // 清理文件夹路径
        folderPath = folderPath.trim().replaceAll("^/+|/+$", "");
        return folderPath + "/" + datePath + "/" + fileName;
    }

    /**
     * 获取文件扩展名
     */
    public static String getFileExtension(String filename) {
        if (StrUtil.isBlank(filename)) {
            return "";
        }
        int lastDotIndex = filename.lastIndexOf(".");
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "";
        }
        return filename.substring(lastDotIndex + 1).toLowerCase();
    }

    /**
     * 计算文件MD5
     */
    public static String calculateMD5(MultipartFile file) {
        try {
            return DigestUtil.md5Hex(file.getInputStream());
        } catch (IOException e) {
            log.error("计算文件MD5失败", e);
            return null;
        }
    }

    /**
     * 计算输入流MD5
     */
    public static String calculateMD5(InputStream inputStream) {
        try {
            return DigestUtil.md5Hex(inputStream);
        } catch (Exception e) {
            log.error("计算输入流MD5失败", e);
            return null;
        }
    }

    /**
     * 判断是否为图片文件
     */
    public static boolean isImageFile(String filename) {
        String extension = getFileExtension(filename);
        if (StrUtil.isBlank(extension)) {
            return false;
        }
        
        for (String imageExt : IMAGE_EXTENSIONS) {
            if (imageExt.equalsIgnoreCase(extension)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 判断是否为图片文件（通过MIME类型）
     */
    public static boolean isImageFile(String filename, String contentType) {
        return isImageFile(filename) || 
               (StrUtil.isNotBlank(contentType) && contentType.startsWith("image/"));
    }

    /**
     * 生成缩略图
     */
    public static byte[] generateThumbnail(InputStream inputStream, int width, int height) {
        try {
            BufferedImage originalImage = ImageIO.read(inputStream);
            if (originalImage == null) {
                return null;
            }

            // 计算缩略图尺寸，保持宽高比
            int originalWidth = originalImage.getWidth();
            int originalHeight = originalImage.getHeight();
            
            double ratio = Math.min((double) width / originalWidth, (double) height / originalHeight);
            int thumbnailWidth = (int) (originalWidth * ratio);
            int thumbnailHeight = (int) (originalHeight * ratio);

            // 创建缩略图
            BufferedImage thumbnailImage = new BufferedImage(thumbnailWidth, thumbnailHeight, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = thumbnailImage.createGraphics();
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g2d.drawImage(originalImage, 0, 0, thumbnailWidth, thumbnailHeight, null);
            g2d.dispose();

            // 转换为字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(thumbnailImage, "jpg", baos);
            return baos.toByteArray();
        } catch (IOException e) {
            log.error("生成缩略图失败", e);
            return null;
        }
    }

    /**
     * 生成缩略图文件名
     */
    public static String generateThumbnailFileName(String originalFileName) {
        String extension = getFileExtension(originalFileName);
        String nameWithoutExt = originalFileName;
        if (StrUtil.isNotBlank(extension)) {
            nameWithoutExt = originalFileName.substring(0, originalFileName.lastIndexOf("."));
        }
        return nameWithoutExt + "_thumb.jpg";
    }

    /**
     * 获取图片尺寸信息
     */
    public static ImageDimensions getImageDimensions(InputStream inputStream) {
        try {
            BufferedImage image = ImageIO.read(inputStream);
            if (image == null) {
                return null;
            }
            return new ImageDimensions(image.getWidth(), image.getHeight());
        } catch (IOException e) {
            log.error("获取图片尺寸失败", e);
            return null;
        }
    }

    /**
     * 图片尺寸信息类
     */
    public static class ImageDimensions {
        private final int width;
        private final int height;

        public ImageDimensions(int width, int height) {
            this.width = width;
            this.height = height;
        }

        public int getWidth() {
            return width;
        }

        public int getHeight() {
            return height;
        }
    }

    /**
     * 验证文件类型
     */
    public static boolean isValidFileType(MultipartFile file, String[] allowedTypes) {
        if (allowedTypes == null || allowedTypes.length == 0) {
            return true;
        }
        
        String extension = getFileExtension(file.getOriginalFilename());
        for (String allowedType : allowedTypes) {
            if (allowedType.equalsIgnoreCase(extension)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 格式化文件大小
     */
    public static String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.2f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.2f MB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.2f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 清理文件路径
     */
    public static String cleanPath(String path) {
        if (StrUtil.isBlank(path)) {
            return "";
        }
        return path.trim()
                   .replaceAll("\\\\", "/")  // 统一使用正斜杠
                   .replaceAll("/+", "/")    // 合并多个斜杠
                   .replaceAll("^/+|/+$", ""); // 去除首尾斜杠
    }
}

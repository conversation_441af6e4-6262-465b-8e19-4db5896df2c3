package com.ylzx.annotation.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylzx.annotation.domain.AnnotationImages;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 标注图片Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Mapper
public interface AnnotationImagesMapper extends BaseMapper<AnnotationImages>
{
    /**
     * 查询标注图片
     * 
     * @param imageId 标注图片主键
     * @return 标注图片
     */
    AnnotationImages selectAnnotationImagesByImageId(Long imageId);

    /**
     * 查询标注图片列表
     * 
     * @param annotationImages 标注图片
     * @return 标注图片集合
     */
    List<AnnotationImages> selectAnnotationImagesList(AnnotationImages annotationImages);

    /**
     * 新增标注图片
     * 
     * @param annotationImages 标注图片
     * @return 结果
     */
    int insertAnnotationImages(AnnotationImages annotationImages);

    /**
     * 修改标注图片
     * 
     * @param annotationImages 标注图片
     * @return 结果
     */
    int updateAnnotationImages(AnnotationImages annotationImages);

    /**
     * 删除标注图片
     * 
     * @param imageId 标注图片主键
     * @return 结果
     */
    int deleteAnnotationImagesByImageId(Long imageId);

    /**
     * 批量删除标注图片
     * 
     * @param imageIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteAnnotationImagesByImageIds(Long[] imageIds);

    /**
     * 批量插入标注图片
     * 
     * @param annotationImages 标注图片
     * @return 结果
     */

    int insertBatch(List<AnnotationImages> annotationImages);

    /**
     * 批量更新标注图片
     *
     * @param annotationImages 标注图片
     * @return 结果
     */
    int updateBatch(List<AnnotationImages> annotationImages);

    /**
     * 检查MD5哈希是否存在
     *
     * @param md5Hash MD5哈希值
     * @return 存在的记录数量，0表示不存在，大于0表示存在
     */
    int countByMd5Hash(@Param("md5Hash") String md5Hash);
}

package com.ylzx.file.util;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.archivers.zip.ZipArchiveEntry;
import org.apache.commons.compress.archivers.zip.ZipArchiveOutputStream;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * ZIP压缩工具类
 * 
 * <AUTHOR>
 */
@Slf4j
public class ZipUtil {

    /**
     * 压缩文件列表为ZIP
     * 
     * @param fileStreams 文件流映射（文件路径 -> 输入流）
     * @param zipFileName ZIP文件名
     * @return ZIP文件字节数组
     */
    public static byte[] compressFiles(Map<String, InputStream> fileStreams, String zipFileName) {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             ZipArchiveOutputStream zos = new ZipArchiveOutputStream(baos)) {
            
            // 设置编码，避免中文乱码
            zos.setEncoding(StandardCharsets.UTF_8.name());
            
            for (Map.Entry<String, InputStream> entry : fileStreams.entrySet()) {
                String filePath = entry.getKey();
                InputStream inputStream = entry.getValue();
                
                if (inputStream == null) {
                    log.warn("文件流为空，跳过：{}", filePath);
                    continue;
                }
                
                try {
                    // 创建ZIP条目
                    ZipArchiveEntry zipEntry = new ZipArchiveEntry(filePath);
                    zos.putArchiveEntry(zipEntry);
                    
                    // 写入文件内容
                    byte[] buffer = new byte[8192];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        zos.write(buffer, 0, bytesRead);
                    }
                    
                    zos.closeArchiveEntry();
                    log.debug("文件已添加到ZIP：{}", filePath);
                    
                } catch (IOException e) {
                    log.error("添加文件到ZIP失败：{}", filePath, e);
                } finally {
                    try {
                        inputStream.close();
                    } catch (IOException e) {
                        log.error("关闭文件流失败：{}", filePath, e);
                    }
                }
            }
            
            zos.finish();
            log.info("ZIP压缩完成，文件数量：{}", fileStreams.size());
            return baos.toByteArray();
            
        } catch (IOException e) {
            log.error("ZIP压缩失败", e);
            return null;
        }
    }

    /**
     * 压缩单个文件
     * 
     * @param inputStream 文件输入流
     * @param fileName 文件名
     * @param zipFileName ZIP文件名
     * @return ZIP文件字节数组
     */
    public static byte[] compressFile(InputStream inputStream, String fileName, String zipFileName) {
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
             ZipArchiveOutputStream zos = new ZipArchiveOutputStream(baos)) {
            
            zos.setEncoding(StandardCharsets.UTF_8.name());
            
            ZipArchiveEntry zipEntry = new ZipArchiveEntry(fileName);
            zos.putArchiveEntry(zipEntry);
            
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                zos.write(buffer, 0, bytesRead);
            }
            
            zos.closeArchiveEntry();
            zos.finish();
            
            log.info("单文件ZIP压缩完成：{}", fileName);
            return baos.toByteArray();
            
        } catch (IOException e) {
            log.error("单文件ZIP压缩失败：{}", fileName, e);
            return null;
        }
    }

    /**
     * 创建文件夹结构的ZIP条目
     * 
     * @param folderPath 文件夹路径
     * @param zos ZIP输出流
     */
    public static void createFolderEntry(String folderPath, ZipArchiveOutputStream zos) {
        try {
            if (StrUtil.isNotBlank(folderPath)) {
                String normalizedPath = folderPath.endsWith("/") ? folderPath : folderPath + "/";
                ZipArchiveEntry folderEntry = new ZipArchiveEntry(normalizedPath);
                zos.putArchiveEntry(folderEntry);
                zos.closeArchiveEntry();
                log.debug("文件夹条目已创建：{}", normalizedPath);
            }
        } catch (IOException e) {
            log.error("创建文件夹条目失败：{}", folderPath, e);
        }
    }

    /**
     * 验证ZIP文件名
     * 
     * @param fileName 文件名
     * @return 验证后的文件名
     */
    public static String validateZipFileName(String fileName) {
        if (StrUtil.isBlank(fileName)) {
            return "archive.zip";
        }
        
        // 清理文件名，移除非法字符
        String cleanName = fileName.replaceAll("[\\\\/:*?\"<>|]", "_");
        
        // 确保以.zip结尾
        if (!cleanName.toLowerCase().endsWith(".zip")) {
            cleanName += ".zip";
        }
        
        return cleanName;
    }

    /**
     * 计算压缩比
     * 
     * @param originalSize 原始大小
     * @param compressedSize 压缩后大小
     * @return 压缩比（百分比）
     */
    public static double calculateCompressionRatio(long originalSize, long compressedSize) {
        if (originalSize == 0) {
            return 0.0;
        }
        return ((double) (originalSize - compressedSize) / originalSize) * 100;
    }

    /**
     * 格式化压缩信息
     * 
     * @param originalSize 原始大小
     * @param compressedSize 压缩后大小
     * @return 压缩信息字符串
     */
    public static String formatCompressionInfo(long originalSize, long compressedSize) {
        double ratio = calculateCompressionRatio(originalSize, compressedSize);
        return String.format("原始大小: %s, 压缩后: %s, 压缩比: %.2f%%",
                FileUtil.formatFileSize(originalSize),
                FileUtil.formatFileSize(compressedSize),
                ratio);
    }
}

package com.ylzx.annotation.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylzx.annotation.domain.*;
import com.ylzx.annotation.domain.enums.AnnotationStatus;
import com.ylzx.annotation.mapper.AnnotationAnnotationsMapper;
import com.ylzx.annotation.mapper.AnnotationCategoriesImagesMapper;
import com.ylzx.annotation.mapper.AnnotationCategoriesMapper;
import com.ylzx.annotation.mapper.AnnotationLabelsMapper;
import com.ylzx.annotation.service.AnnotationAnnotationsService;
import com.ylzx.annotation.service.AnnotationCategoriesService;
import com.ylzx.annotation.service.AnnotationReviewsService;
import com.ylzx.common.utils.SecurityUtils;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Random;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 标注Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
public class AnnotationAnnotationsServiceImpl extends ServiceImpl<AnnotationAnnotationsMapper, AnnotationAnnotations> implements AnnotationAnnotationsService {
    @Resource
    private AnnotationAnnotationsMapper annotationAnnotationsMapper;

    @Resource
    private AnnotationCategoriesImagesMapper annotationCategoriesImagesMapper;

    @Autowired
    private AnnotationCategoriesService annotationCategoriesService;

    @Lazy
    @Autowired
    private AnnotationReviewsService annotationReviewsService;


    @Autowired
    private AnnotationCategoriesMapper annotationCategoriesMapper;

    @Autowired
    private AnnotationLabelsMapper annotationLabelsMapper;

    /**
     * 查询标注
     *
     * @param annotationId 标注主键
     * @return 标注
     */
    @Override
    public AnnotationAnnotations selectAnnotationAnnotationsByAnnotationId(Long annotationId) {

        AnnotationAnnotations annotationAnnotations = annotationAnnotationsMapper.selectAnnotationAnnotationsByAnnotationId(annotationId);
        AnnotationLabels annotationLabels = annotationLabelsMapper.selectAnnotationLabelsByLabelId(annotationAnnotations.getLabelId());
        annotationAnnotations.setLabelName(annotationLabels.getName());


        return annotationAnnotations;
    }

    /**
     * 查询标注列表
     *
     * @param annotationAnnotations 标注
     * @return 标注集合
     */
    @Override
    public List<AnnotationAnnotations> selectAnnotationAnnotationsList(AnnotationAnnotations annotationAnnotations) {
        List<AnnotationAnnotations> annotationAnnotationsList = annotationAnnotationsMapper.selectAnnotationAnnotationsList(annotationAnnotations);


        List<AnnotationCategories> annotationCategoriesList = annotationCategoriesMapper.selectAnnotationCategoriesList(new AnnotationCategories());
        List<AnnotationLabels> annotationLabelsList = annotationLabelsMapper.selectAnnotationLabelsList(AnnotationLabels.builder().build());

        for (AnnotationAnnotations annotations : annotationAnnotationsList) {

            for (AnnotationCategories category : annotationCategoriesList) {
                if (annotations.getCategoryId().equals(category.getCategoryId())) {
                    annotations.setCategoryName(category.getName());
                    break;
                }
            }
            for (AnnotationLabels label : annotationLabelsList) {
                if (annotations.getLabelId().equals(label.getLabelId())) {
                    annotations.setLabelName(label.getName());
                    break;
                }
            }
        }


        return annotationAnnotationsList;
    }

    /**
     * 新增标注
     *
     * @param annotationAnnotations 标注
     * @return 结果
     */
    @Override
    public int insertAnnotationAnnotations(AnnotationAnnotations annotationAnnotations) {
        int result = annotationAnnotationsMapper.insertAnnotationAnnotations(annotationAnnotations);

        // 如果标注状态为待审核，则根据分类的审核比例自动生成审核记录
        if (result > 0 && AnnotationStatus.PENDING_REVIEW.equals(annotationAnnotations.getStatus())) {
            autoGenerateReviewRecord(annotationAnnotations);
        }

        return result;
    }

    /**
     * 自动生成审核记录（按概率）
     *
     * @param annotation 标注记录
     */
    private void autoGenerateReviewRecord(AnnotationAnnotations annotation) {
        try {
            // 1. 获取分类的审核比例
            AnnotationCategories category = annotationCategoriesService.selectAnnotationCategoriesByCategoryId(annotation.getCategoryId());
            if (category == null || category.getReviewRatio() == null || category.getReviewRatio().compareTo(BigDecimal.ZERO) <= 0) {
                return; // 没有设置审核比例，不进行抽审
            }

            // 2. 检查是否已存在审核记录
            if (annotationReviewsService.existsReviewByAnnotationId(annotation.getAnnotationId())) {
                return; // 已存在审核记录，不重复生成
            }

            // 3. 按概率决定是否生成审核记录
            Random random = new Random();
            double randomValue = random.nextDouble();
            if (randomValue <= category.getReviewRatio().doubleValue()) {
                // 4. 获取标注人ID
                Long annotatorId = null;
                if (annotation.getImageId() != null && annotation.getCategoryId() != null) {
                    AnnotationCategoriesImages categoryImage = new AnnotationCategoriesImages();
                    categoryImage.setImageId(annotation.getImageId());
                    categoryImage.setCategoryId(annotation.getCategoryId());

                    List<AnnotationCategoriesImages> categoryImages = annotationCategoriesImagesMapper.selectAnnotationCategoriesImagesList(categoryImage);
                    if (!categoryImages.isEmpty()) {
                        annotatorId = categoryImages.get(0).getAnnotatorId();
                    }
                }

                // 5. 创建审核记录
                AnnotationReviews review = AnnotationReviews.builder()
                        .annotationId(annotation.getAnnotationId())
                        .annotatorId(annotatorId)
                        .imageId(annotation.getImageId())
                        .status(AnnotationStatus.PENDING_REVIEW) // 待审核状态
                        .build();

                annotationReviewsService.insertAnnotationReviews(review);
            }
        } catch (Exception e) {
            // 记录日志但不影响主流程
            System.err.println("自动生成审核记录失败: " + e.getMessage());
        }
    }


    /**
     * 自动生成审核记录（按概率）
     *
     * @param annotation 标注记录
     */
    private Long generateReviewRecord(AnnotationAnnotations annotation) {
        try {
            // 1. 获取分类的审核比例
            AnnotationCategories category = annotationCategoriesService.selectAnnotationCategoriesByCategoryId(annotation.getCategoryId());
            if (category == null || category.getReviewRatio() == null || category.getReviewRatio().compareTo(BigDecimal.ZERO) <= 0) {
                return null; // 没有设置审核比例，不进行抽审
            }

            // 2. 检查是否已存在审核记录
            if (annotationReviewsService.existsReviewByAnnotationId(annotation.getAnnotationId())) {
                return null; // 已存在审核记录，不重复生成
            }

            // 3. 按概率决定是否生成审核记录
            Random random = new Random();
            double randomValue = random.nextDouble();
            if (randomValue <= category.getReviewRatio().doubleValue()) {
                // 4. 获取标注人ID
                Long annotatorId = null;
                if (annotation.getImageId() != null && annotation.getCategoryId() != null) {
                    AnnotationCategoriesImages categoryImage = new AnnotationCategoriesImages();
                    categoryImage.setImageId(annotation.getImageId());
                    categoryImage.setCategoryId(annotation.getCategoryId());

                    List<AnnotationCategoriesImages> categoryImages = annotationCategoriesImagesMapper.selectAnnotationCategoriesImagesList(categoryImage);
                    if (!categoryImages.isEmpty()) {
                        annotatorId = categoryImages.get(0).getAnnotatorId();
                    }
                }

                // 5. 创建审核记录
                AnnotationReviews review = AnnotationReviews.builder()
                        .annotationId(annotation.getAnnotationId())
                        .annotatorId(annotatorId)
                        .imageId(annotation.getImageId())
                        .status(AnnotationStatus.PENDING_REVIEW) // 待审核状态
                        .build();

                annotationReviewsService.insertAnnotationReviews(review);

                return annotation.getAnnotationId();
            }
        } catch (Exception e) {
            // 记录日志但不影响主流程
            System.err.println("自动生成审核记录失败: " + e.getMessage());
        }

        return null;
    }

    /**
     * 修改标注
     *
     * @param annotationAnnotations 标注
     * @return 结果
     */
    @Override
    public int updateAnnotationAnnotations(AnnotationAnnotations annotationAnnotations) {

        int i = annotationAnnotationsMapper.updateAnnotationAnnotations(annotationAnnotations);

        if (i > 0 && AnnotationStatus.PENDING_REVIEW.equals(annotationAnnotations.getStatus())) {
            // 不重新抽， 直接更新状态
            // Long aLong = generateReviewRecord(annotationAnnotations);
            List<AnnotationReviews> annotationReviews = annotationReviewsService.selectAnnotationReviewsList(AnnotationReviews.builder().annotationId(annotationAnnotations.getAnnotationId()).build());
            if(annotationReviews.size() == 1){
                annotationReviews.get(0).setStatus(AnnotationStatus.PENDING_REVIEW);
                annotationReviewsService.updateAnnotationReviews(annotationReviews.get(0));
            }
        }
        return i;
    }

    /**
     * 修改标注状态
     *
     * @param annotationAnnotations 标注
     * @return 结果
     */
    @Override
    public int updateAnnotationAnnotationsStatus(AnnotationAnnotations annotationAnnotations) {
        int rows = annotationAnnotationsMapper.updateAnnotationAnnotationsStatus(annotationAnnotations);
        if (rows > 0) {
            AnnotationAnnotations overallStatusResult = this.checkOverallStatusByAnnotationId(annotationAnnotations.getAnnotationId());
            if (Objects.nonNull(overallStatusResult) && Objects.equals(annotationAnnotations.getStatus(), overallStatusResult.getStatus())) {
                AnnotationCategoriesImages imageCategory = new AnnotationCategoriesImages();
                imageCategory.setCategoryId(overallStatusResult.getCategoryId());
                imageCategory.setImageId(overallStatusResult.getImageId());
                imageCategory.setStatus(overallStatusResult.getStatus());
                annotationCategoriesImagesMapper.updateAnnotationCategoriesImages(imageCategory);
            }
        }
        return rows;
    }

    /**
     * 批量逻辑删除标注
     *
     * @param annotationIds 需要删除的标注主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationAnnotationsByAnnotationIds(Long[] annotationIds) {
        return annotationAnnotationsMapper.logicDeleteAnnotationAnnotationsByAnnotationIds(annotationIds);
    }

    /**
     * 逻辑删除标注信息
     *
     * @param annotationId 标注主键
     * @return 结果
     */
    @Override
    public int deleteAnnotationAnnotationsByAnnotationId(Long annotationId) {
        return annotationAnnotationsMapper.logicDeleteAnnotationAnnotationsByAnnotationId(annotationId);
    }

    /**
     * 【SQL优化】根据单个标注ID，一次性查询同一个图片和类别下的所有标注的整体状态
     *
     * @param annotationId 标注ID
     * @return 整体状态 ('1': 未审核完, '2': 审核通过, '3': 审核不通过)
     */
    @Override
    public AnnotationAnnotations checkOverallStatusByAnnotationId(Long annotationId) {
        return annotationAnnotationsMapper.checkOverallStatusInSql(annotationId);
    }

    @Override
    public int insertAnnotationAnnotationsBatch(List<AnnotationAnnotations> annotationAnnotationsList) {


        annotationAnnotationsList.forEach(item -> {
            item.setLabeler(SecurityUtils.getUsername());
        });
        int result = annotationAnnotationsMapper.insertAnnotationAnnotationsBatch(annotationAnnotationsList);

        if (result > 0) {
            AtomicBoolean isSelect = new AtomicBoolean(false);
            annotationAnnotationsList.forEach(item -> {
                List<AnnotationAnnotations> annotationAnnotationsList1 = annotationAnnotationsMapper.selectAnnotationAnnotationsList(item);
                if (annotationAnnotationsList1.size() == 1)
                    item.setAnnotationId(annotationAnnotationsList1.get(0).getAnnotationId());
                Long aLong = generateReviewRecord(item);
                if (aLong != null) {
                    isSelect.set(true);
                }else {
                    item.setStatus(AnnotationStatus.ANNOTATED);
                    annotationAnnotationsMapper.updateAnnotationAnnotationsStatus(item);
                }
            });

            // 更新任务状态
            AnnotationAnnotations annotationAnnotations = annotationAnnotationsList.get(0);
            if (annotationAnnotations != null) {
                annotationCategoriesImagesMapper.updateAnnotationCategoriesImages(AnnotationCategoriesImages.builder().categoryId(annotationAnnotations.getCategoryId()).imageId(annotationAnnotations.getImageId()).status(isSelect.get() ? AnnotationStatus.PENDING_REVIEW : AnnotationStatus.ANNOTATED).build());
            }
        }
        return result;
    }

    @Override
    public int deleteAnnotationAnnotations(AnnotationAnnotations annotationAnnotations) {
        return annotationAnnotationsMapper.deleteAnnotationAnnotations(annotationAnnotations);
    }
}
